<!DOCTYPE html>
<html lang="so">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Image Generation Demo - Tincada AI</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
            padding: 2rem;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 3rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        h1 {
            text-align: center;
            font-size: 2.5rem;
            margin-bottom: 1rem;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .subtitle {
            text-align: center;
            font-size: 1.2rem;
            margin-bottom: 3rem;
            opacity: 0.9;
        }

        .image-generator {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            border-left: 4px solid #4ecdc4;
        }

        .image-generator h2 {
            color: #4ecdc4;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-label {
            display: block;
            color: white;
            font-weight: 500;
            margin-bottom: 0.5rem;
            font-size: 0.9rem;
        }

        .form-input, .form-select {
            width: 100%;
            padding: 12px 15px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            color: white;
            font-family: inherit;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .form-input:focus, .form-select:focus {
            outline: none;
            border-color: #4ecdc4;
            background: rgba(255, 255, 255, 0.15);
            box-shadow: 0 0 0 3px rgba(78, 205, 196, 0.2);
        }

        .form-input::placeholder {
            color: rgba(255, 255, 255, 0.6);
        }

        .form-select option {
            background: #333;
            color: white;
        }

        .generate-btn {
            width: 100%;
            background: linear-gradient(45deg, #4ecdc4, #44a08d);
            border: none;
            color: white;
            padding: 15px;
            border-radius: 10px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(78, 205, 196, 0.3);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }

        .generate-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(78, 205, 196, 0.4);
        }

        .generate-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .image-result {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            padding: 2rem;
            margin-top: 2rem;
            border: 1px solid rgba(255, 255, 255, 0.1);
            display: none;
        }

        .image-result.show {
            display: block;
            animation: fadeIn 0.5s ease;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .generated-image {
            text-align: center;
            margin: 2rem 0;
        }

        .generated-image img {
            max-width: 100%;
            height: auto;
            border-radius: 12px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
        }

        .image-placeholder {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border-radius: 12px;
            padding: 3rem;
            text-align: center;
            border: 2px dashed rgba(255, 255, 255, 0.3);
        }

        .image-placeholder-icon {
            font-size: 4rem;
            margin-bottom: 1rem;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .image-actions {
            display: flex;
            gap: 10px;
            justify-content: center;
            flex-wrap: wrap;
            margin-top: 1rem;
        }

        .action-btn {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.9rem;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .action-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-1px);
        }

        .status-message {
            padding: 1rem;
            border-radius: 10px;
            margin: 1rem 0;
            text-align: center;
            font-weight: 500;
        }

        .status-success {
            background: rgba(76, 175, 80, 0.2);
            border: 1px solid rgba(76, 175, 80, 0.3);
            color: #4caf50;
        }

        .status-error {
            background: rgba(244, 67, 54, 0.2);
            border: 1px solid rgba(244, 67, 54, 0.3);
            color: #f44336;
        }

        .status-info {
            background: rgba(33, 150, 243, 0.2);
            border: 1px solid rgba(33, 150, 243, 0.3);
            color: #2196f3;
        }

        .examples-section {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            padding: 2rem;
            margin-top: 2rem;
        }

        .examples-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin-top: 1rem;
        }

        .example-card {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 10px;
            padding: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .example-card:hover {
            transform: translateY(-2px);
            border-color: rgba(255, 255, 255, 0.2);
        }

        .example-card h4 {
            color: #4ecdc4;
            margin-bottom: 0.5rem;
            font-size: 0.9rem;
        }

        .example-card p {
            color: #cccccc;
            font-size: 0.8rem;
            line-height: 1.4;
        }

        @media (max-width: 768px) {
            body { padding: 1rem; }
            .container { padding: 2rem 1rem; }
            h1 { font-size: 2rem; }
            .examples-grid { grid-template-columns: 1fr; }
            .image-actions { flex-direction: column; align-items: center; }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎨 AI Image Generation</h1>
        <p class="subtitle">Create stunning images using OpenAI DALL-E 3 API</p>
        
        <div class="image-generator">
            <h2>🖼️ Generate Image</h2>
            <p>Describe the image you want to create and let AI bring it to life:</p>
            
            <form id="imageForm">
                <div class="form-group">
                    <label class="form-label" for="imagePrompt">Image Description:</label>
                    <textarea id="imagePrompt" class="form-input" rows="3" 
                              placeholder="Describe your image in detail... e.g., 'A beautiful sunset over mountains with a lake in the foreground'"></textarea>
                </div>

                <div class="form-group">
                    <label class="form-label" for="imageStyle">Art Style:</label>
                    <select id="imageStyle" class="form-select">
                        <option value="realistic">Realistic</option>
                        <option value="artistic">Artistic</option>
                        <option value="cartoon">Cartoon</option>
                        <option value="abstract">Abstract</option>
                        <option value="digital-art">Digital Art</option>
                    </select>
                </div>

                <div class="form-group">
                    <label class="form-label" for="imageSize">Image Size:</label>
                    <select id="imageSize" class="form-select">
                        <option value="square">Square (1024x1024)</option>
                        <option value="landscape">Landscape (1792x1024)</option>
                        <option value="portrait">Portrait (1024x1792)</option>
                    </select>
                </div>

                <button type="submit" class="generate-btn" id="generateBtn">
                    <i class="fas fa-magic"></i>
                    <span>Generate Image</span>
                </button>
            </form>
        </div>
        
        <div class="image-result" id="imageResult">
            <h3>🎨 Generated Image</h3>
            <div class="generated-image" id="generatedImage">
                <div class="image-placeholder">
                    <div class="image-placeholder-icon">🎨</div>
                    <h4>Generating your image...</h4>
                    <p>This may take 30-60 seconds</p>
                </div>
            </div>
            
            <div class="status-message" id="statusMessage"></div>
            
            <div class="image-actions" id="imageActions" style="display: none;">
                <button class="action-btn" onclick="downloadImage()">
                    <i class="fas fa-download"></i> Download
                </button>
                <button class="action-btn" onclick="shareImage()">
                    <i class="fas fa-share"></i> Share
                </button>
                <button class="action-btn" onclick="regenerateImage()">
                    <i class="fas fa-redo"></i> Regenerate
                </button>
            </div>
        </div>
        
        <div class="examples-section">
            <h3>💡 Example Prompts</h3>
            <p>Click on any example to try it:</p>
            
            <div class="examples-grid">
                <div class="example-card" onclick="useExample('A majestic lion in the African savanna at golden hour')">
                    <h4>🦁 Wildlife</h4>
                    <p>A majestic lion in the African savanna at golden hour</p>
                </div>
                
                <div class="example-card" onclick="useExample('A futuristic city with flying cars and neon lights')">
                    <h4>🏙️ Sci-Fi</h4>
                    <p>A futuristic city with flying cars and neon lights</p>
                </div>
                
                <div class="example-card" onclick="useExample('A peaceful Japanese garden with cherry blossoms')">
                    <h4>🌸 Nature</h4>
                    <p>A peaceful Japanese garden with cherry blossoms</p>
                </div>
                
                <div class="example-card" onclick="useExample('An astronaut floating in space with Earth in background')">
                    <h4>🚀 Space</h4>
                    <p>An astronaut floating in space with Earth in background</p>
                </div>
                
                <div class="example-card" onclick="useExample('A cozy coffee shop on a rainy day')">
                    <h4>☕ Lifestyle</h4>
                    <p>A cozy coffee shop on a rainy day</p>
                </div>
                
                <div class="example-card" onclick="useExample('Abstract geometric patterns in vibrant colors')">
                    <h4>🎨 Abstract</h4>
                    <p>Abstract geometric patterns in vibrant colors</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentImageUrl = null;

        document.getElementById('imageForm').addEventListener('submit', function(e) {
            e.preventDefault();
            generateImage();
        });

        async function generateImage() {
            const prompt = document.getElementById('imagePrompt').value.trim();
            const style = document.getElementById('imageStyle').value;
            const size = document.getElementById('imageSize').value;
            
            if (!prompt) {
                showStatus('Please enter an image description', 'error');
                return;
            }
            
            const generateBtn = document.getElementById('generateBtn');
            const imageResult = document.getElementById('imageResult');
            const generatedImage = document.getElementById('generatedImage');
            const imageActions = document.getElementById('imageActions');
            
            // Show loading state
            generateBtn.disabled = true;
            generateBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> <span>Generating...</span>';
            
            imageResult.classList.add('show');
            generatedImage.innerHTML = `
                <div class="image-placeholder">
                    <div class="image-placeholder-icon">🎨</div>
                    <h4>Generating your image...</h4>
                    <p>Using DALL-E 3 API - This may take 30-60 seconds</p>
                </div>
            `;
            imageActions.style.display = 'none';
            
            try {
                const response = await fetch('http://localhost:5001/api/tools/create-image', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        prompt: prompt,
                        style: style,
                        size: size,
                        user_id: 'image_demo_user'
                    })
                });
                
                const data = await response.json();
                
                if (data.error) {
                    showStatus(data.error, 'error');
                    
                    if (data.error.includes('billing')) {
                        generatedImage.innerHTML = `
                            <div class="image-placeholder">
                                <div class="image-placeholder-icon">💳</div>
                                <h4>Billing Limit Reached</h4>
                                <p>OpenAI account has no credits for image generation</p>
                                <p style="margin-top: 1rem; font-size: 0.9rem; opacity: 0.8;">
                                    Add credits to your OpenAI account to generate images
                                </p>
                            </div>
                        `;
                    } else {
                        generatedImage.innerHTML = `
                            <div class="image-placeholder">
                                <div class="image-placeholder-icon">❌</div>
                                <h4>Generation Failed</h4>
                                <p>${data.error}</p>
                            </div>
                        `;
                    }
                } else if (data.image_url) {
                    // Real image generated
                    currentImageUrl = data.image_url;
                    generatedImage.innerHTML = `
                        <img src="${data.image_url}" alt="Generated Image" style="max-width: 100%; height: auto; border-radius: 12px; box-shadow: 0 8px 25px rgba(0,0,0,0.3);">
                    `;
                    imageActions.style.display = 'flex';
                    showStatus('Image generated successfully!', 'success');
                } else {
                    // Fallback response (no real image)
                    generatedImage.innerHTML = `
                        <div class="image-placeholder">
                            <div class="image-placeholder-icon">🎨</div>
                            <h4>Demo Mode</h4>
                            <p>Image generation configured but requires OpenAI credits</p>
                            <p style="margin-top: 1rem; font-size: 0.9rem; opacity: 0.8;">
                                Prompt: "${prompt}"<br>
                                Style: ${style}<br>
                                Size: ${size}
                            </p>
                        </div>
                    `;
                    showStatus('Demo mode - Add OpenAI credits to generate real images', 'info');
                }
                
            } catch (error) {
                console.error('Error:', error);
                showStatus('Connection error. Please check if the server is running.', 'error');
                
                generatedImage.innerHTML = `
                    <div class="image-placeholder">
                        <div class="image-placeholder-icon">🔌</div>
                        <h4>Connection Error</h4>
                        <p>Please start the Flask server</p>
                        <p style="margin-top: 1rem; font-size: 0.9rem; opacity: 0.8;">
                            Run: python flask_server.py
                        </p>
                    </div>
                `;
            } finally {
                generateBtn.disabled = false;
                generateBtn.innerHTML = '<i class="fas fa-magic"></i> <span>Generate Image</span>';
            }
        }

        function showStatus(message, type) {
            const statusMessage = document.getElementById('statusMessage');
            statusMessage.textContent = message;
            statusMessage.className = `status-message status-${type}`;
            statusMessage.style.display = 'block';
            
            setTimeout(() => {
                statusMessage.style.display = 'none';
            }, 5000);
        }

        function useExample(prompt) {
            document.getElementById('imagePrompt').value = prompt;
            document.getElementById('imagePrompt').focus();
        }

        function downloadImage() {
            if (currentImageUrl) {
                const link = document.createElement('a');
                link.href = currentImageUrl;
                link.download = 'tincada-ai-generated-image.png';
                link.click();
            }
        }

        function shareImage() {
            if (currentImageUrl) {
                if (navigator.share) {
                    navigator.share({
                        title: 'AI Generated Image',
                        text: 'Check out this image I created with Tincada AI!',
                        url: currentImageUrl
                    });
                } else {
                    navigator.clipboard.writeText(currentImageUrl);
                    showStatus('Image URL copied to clipboard!', 'success');
                }
            }
        }

        function regenerateImage() {
            generateImage();
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🎨 Image generation demo loaded');
            
            // Test server connection
            fetch('http://localhost:5001/api/health')
                .then(response => response.json())
                .then(data => {
                    console.log('✅ Server connected:', data);
                    if (data.openai_available) {
                        showStatus('✅ OpenAI API connected - Ready to generate images!', 'success');
                    } else {
                        showStatus('⚠️ OpenAI API not configured - Demo mode only', 'info');
                    }
                })
                .catch(error => {
                    console.log('⚠️ Server not connected:', error);
                    showStatus('❌ Server not connected. Please start: python flask_server.py', 'error');
                });
        });
    </script>
</body>
</html>
