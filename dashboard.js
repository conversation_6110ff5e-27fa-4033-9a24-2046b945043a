// Tincada AI - Dashboard JavaScript
class TincadaDashboard {
    constructor() {
        // Primary API key (OpenAI format)
        this.apiKey = '********************************************************************************************************************************************************************';
        this.apiUrl = 'https://api.openai.com/v1/chat/completions';
        // Fallback API key (alternative format)
        this.fallbackApiKey = 'sk-1d9fea924a444e58b28769de9662ef20';
        this.currentUser = null;
        this.currentChatId = null;
        this.chatHistory = [];
        this.chats = [];
        this.messageCount = 0;
        this.maxMessages = 1000; // Daily limit
        this.currentChatMessageCount = 0; // Messages in current chat
        this.maxMessagesPerChat = 100; // Auto new chat after 100 messages
        this.dailyMessageCount = 0; // Total daily messages
        this.useMockAPI = false; // Set to true to use mock responses instead of real API
        
        this.init();
    }

    init() {
        this.checkAuthentication();
        this.loadSavedSettings();
        this.bindEvents();
        this.loadUserProfile();
        this.loadChatHistory();
        this.setupAutoResize();
        this.updateAPIStatusBanner();
    }

    loadSavedSettings() {
        // Load saved API key and mock mode setting
        const savedApiKey = localStorage.getItem('tincadaAI_apiKey');
        const savedMockMode = localStorage.getItem('tincadaAI_useMockAPI');

        if (savedApiKey) {
            this.apiKey = savedApiKey;
        }

        if (savedMockMode) {
            this.useMockAPI = savedMockMode === 'true';
        }

        // Load daily message count
        this.loadDailyMessageCount();

        console.log('🔧 Settings loaded:', {
            apiKey: this.apiKey.substring(0, 20) + '...',
            useMockAPI: this.useMockAPI,
            dailyMessages: this.dailyMessageCount
        });
    }

    loadDailyMessageCount() {
        const today = new Date().toDateString();
        const savedDate = localStorage.getItem('tincadaAI_messageDate');
        const savedCount = localStorage.getItem('tincadaAI_dailyMessageCount');

        if (savedDate === today && savedCount) {
            this.dailyMessageCount = parseInt(savedCount);
        } else {
            // New day, reset count
            this.dailyMessageCount = 0;
            localStorage.setItem('tincadaAI_messageDate', today);
            localStorage.setItem('tincadaAI_dailyMessageCount', '0');
        }

        this.updateMessageCountDisplay();
    }

    saveDailyMessageCount() {
        const today = new Date().toDateString();
        localStorage.setItem('tincadaAI_messageDate', today);
        localStorage.setItem('tincadaAI_dailyMessageCount', this.dailyMessageCount.toString());
        this.updateMessageCountDisplay();
    }

    updateMessageCountDisplay() {
        // Update message count display in UI
        const remaining = this.maxMessages - this.dailyMessageCount;
        const counterElement = document.getElementById('messageCounter');
        const countText = document.getElementById('messageCountText');

        if (countText) {
            countText.textContent = `${this.dailyMessageCount}/${this.maxMessages}`;

            // Update counter styling based on usage
            if (this.dailyMessageCount > this.maxMessages * 0.9) {
                counterElement.className = 'message-counter danger';
            } else if (this.dailyMessageCount > this.maxMessages * 0.7) {
                counterElement.className = 'message-counter warning';
            } else {
                counterElement.className = 'message-counter';
            }
        }

        console.log(`📊 Messages: ${this.dailyMessageCount}/${this.maxMessages} (${remaining} remaining)`);
    }

    checkAuthentication() {
        if (!authManager.isAuthenticated() || !authManager.isSessionValid()) {
            window.location.href = 'login.html';
            return;
        }
        
        this.currentUser = authManager.getCurrentUser();
        authManager.refreshSession();
    }

    loadUserProfile() {
        if (!this.currentUser) return;

        // Update user name
        const userNameElement = document.getElementById('userName');
        if (userNameElement) {
            userNameElement.textContent = this.currentUser.fullName || 'ibnuablib';
        }

        // Update user email in dropdown
        const userEmailElement = document.getElementById('userEmail');
        if (userEmailElement) {
            userEmailElement.textContent = this.currentUser.email || '<EMAIL>';
        }

        // Update user avatar
        const userAvatarElement = document.getElementById('userAvatar');
        if (userAvatarElement && this.currentUser.profileImage) {
            userAvatarElement.innerHTML = `<img src="${this.currentUser.profileImage}" alt="${this.currentUser.fullName}">`;
        }

        // Update welcome title
        const welcomeTitle = document.getElementById('welcomeTitle');
        if (welcomeTitle) {
            const firstName = this.currentUser.fullName.split(' ')[0];
            welcomeTitle.textContent = `Maxaad ka fikirtaa maanta, ${firstName}?`;
        }
    }

    bindEvents() {
        // Sidebar toggle
        document.getElementById('sidebarToggle').addEventListener('click', () => {
            this.toggleSidebar();
        });
        
        // Mobile menu
        document.getElementById('mobileMenuBtn').addEventListener('click', () => {
            this.toggleMobileSidebar();
        });
        
        // New chat button
        document.getElementById('newChatBtn').addEventListener('click', () => {
            this.startNewChat();
        });
        
        // User profile dropdown - both button and profile area clickable
        document.getElementById('profileDropdownBtn').addEventListener('click', (e) => {
            e.stopPropagation();
            this.toggleUserDropdown();
        });

        // Make entire user profile clickable
        document.getElementById('userProfile').addEventListener('click', (e) => {
            e.stopPropagation();
            this.toggleUserDropdown();
        });

        // Make user avatar clickable
        document.getElementById('userAvatar').addEventListener('click', (e) => {
            e.stopPropagation();
            this.toggleUserDropdown();
        });
        
        // Dropdown menu items
        document.getElementById('settingsBtn').addEventListener('click', () => {
            this.openSettings();
        });
        
        document.getElementById('logoutBtn').addEventListener('click', () => {
            this.logout();
        });
        
        // Chat input handling
        document.getElementById('mainChatInput').addEventListener('keypress', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                this.sendMessage('main');
            }
        });
        
        document.getElementById('chatInput').addEventListener('keypress', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                this.sendMessage('chat');
            }
        });
        
        // Send buttons
        document.getElementById('sendBtn').addEventListener('click', () => {
            this.sendMessage('main');
        });
        
        document.getElementById('chatSendBtn').addEventListener('click', () => {
            this.sendMessage('chat');
        });
        
        // File upload
        document.getElementById('attachBtn').addEventListener('click', () => {
            document.getElementById('fileInput').click();
        });
        
        document.getElementById('chatAttachBtn').addEventListener('click', () => {
            document.getElementById('fileInput').click();
        });
        
        // Voice buttons
        document.getElementById('voiceBtn').addEventListener('click', () => {
            this.toggleVoiceRecording();
        });
        
        document.getElementById('chatVoiceBtn').addEventListener('click', () => {
            this.toggleVoiceRecording();
        });
        
        // Search functionality
        document.getElementById('searchInput').addEventListener('input', (e) => {
            this.searchChats(e.target.value);
        });
        
        // Navigation items
        document.querySelectorAll('.nav-item').forEach(item => {
            item.addEventListener('click', () => {
                this.switchNavigation(item.id);
            });
        });
        
        // Close dropdown when clicking outside
        document.addEventListener('click', () => {
            this.closeUserDropdown();
        });
        
        // Close modal when clicking outside
        document.getElementById('settingsModal').addEventListener('click', (e) => {
            if (e.target.id === 'settingsModal') {
                this.closeSettings();
            }
        });

        document.getElementById('closeSettingsModal').addEventListener('click', () => {
            this.closeSettings();
        });

        // Close API status banner
        document.getElementById('closeBannerBtn').addEventListener('click', () => {
            this.hideAPIStatusBanner();
        });

        // Payment modal events
        document.getElementById('closePaymentModal').addEventListener('click', () => {
            this.closePaymentModal();
        });

        document.getElementById('paymentModal').addEventListener('click', (e) => {
            if (e.target.id === 'paymentModal') {
                this.closePaymentModal();
            }
        });

        // Plan selection buttons
        document.querySelectorAll('.plan-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const plan = e.target.closest('.plan-btn').dataset.plan;
                this.selectPlan(plan);
            });
        });

        // Get Plus button
        document.querySelector('.upgrade-btn').addEventListener('click', () => {
            this.showUpgradeModal();
        });

        // Payment form modal events
        document.getElementById('closePaymentFormModal').addEventListener('click', () => {
            this.closePaymentFormModal();
        });

        document.getElementById('paymentFormModal').addEventListener('click', (e) => {
            if (e.target.id === 'paymentFormModal') {
                this.closePaymentFormModal();
            }
        });

        document.getElementById('backToPlanBtn').addEventListener('click', () => {
            this.backToPlans();
        });

        document.getElementById('paymentForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.processPayment();
        });

        // Coming soon features
        document.getElementById('gptsNav').addEventListener('click', () => {
            this.showComingSoon('GPTs', 'Custom AI assistants waa la dhisi doonaa. Samee AI-yo gaar ah oo u gaar ah shaqadaada!');
        });

        document.getElementById('soraNav').addEventListener('click', () => {
            this.showComingSoon('Sora', 'Video generation feature waa la dhisi doonaa. Samee videos professional ah text kaliya!');
        });

        document.getElementById('libraryNav').addEventListener('click', () => {
            this.showComingSoon('Maktabada', 'Knowledge base system waa la dhisi doonaa. Kaydi oo organize gareey macluumaadkaaga!');
        });

        // Help and Customize buttons
        document.getElementById('helpBtn').addEventListener('click', () => {
            this.showHelpModal();
        });

        document.getElementById('customizeBtn').addEventListener('click', () => {
            this.showCustomizeModal();
        });

        // Avatar upload
        document.getElementById('avatarUpload').addEventListener('change', (e) => {
            this.handleAvatarUpload(e);
        });

        // AI Tools buttons (both welcome screen and chat interface)
        document.getElementById('aiToolsBtn').addEventListener('click', () => {
            this.showAIToolsModal();
        });

        document.getElementById('toolsBtn').addEventListener('click', () => {
            this.showAIToolsModal();
        });
    }

    toggleSidebar() {
        const sidebar = document.getElementById('sidebar');
        sidebar.classList.toggle('collapsed');
    }

    toggleMobileSidebar() {
        const sidebar = document.getElementById('sidebar');
        sidebar.classList.toggle('open');
    }

    toggleUserDropdown() {
        const dropdown = document.getElementById('userDropdown');
        dropdown.classList.toggle('show');
    }

    closeUserDropdown() {
        const dropdown = document.getElementById('userDropdown');
        dropdown.classList.remove('show');
    }

    startNewChat() {
        this.currentChatId = this.generateChatId();
        this.chatHistory = [];
        this.currentChatMessageCount = 0; // Reset current chat message count

        // Switch to chat interface
        document.getElementById('welcomeScreen').style.display = 'none';
        document.getElementById('chatInterface').style.display = 'flex';

        // Clear chat messages
        document.getElementById('chatMessages').innerHTML = '';

        // Add to chat list
        this.addChatToList(this.currentChatId, 'Sheeko Cusub');

        // Focus on chat input
        document.getElementById('chatInput').focus();

        console.log('🔄 New chat started:', {
            chatId: this.currentChatId,
            dailyMessages: this.dailyMessageCount,
            currentChatMessages: this.currentChatMessageCount
        });
    }

    async sendMessage(inputType) {
        const inputId = inputType === 'main' ? 'mainChatInput' : 'chatInput';
        const messageInput = document.getElementById(inputId);
        const message = messageInput.value.trim();

        if (!message) return;

        // Check daily message limit
        if (this.dailyMessageCount >= this.maxMessages) {
            this.showPaymentModal();
            return;
        }

        // Check if current chat needs to be reset (100 messages)
        if (this.currentChatMessageCount >= this.maxMessagesPerChat) {
            this.showToast(`Chat waa la dhamaystay (${this.maxMessagesPerChat} fariin). Sheeko cusub ayaa la bilaabayaa...`, 'info');
            this.startNewChat();
            // Update input reference after new chat
            const newInputId = inputType === 'main' ? 'mainChatInput' : 'chatInput';
            const newMessageInput = document.getElementById(newInputId);
            if (newMessageInput) {
                newMessageInput.value = message; // Restore the message
            }
        }

        // If sending from main input, start new chat
        if (inputType === 'main') {
            this.startNewChat();
        }

        // Add user message
        this.addMessage('user', message);
        messageInput.value = '';
        this.autoResizeTextarea(messageInput);
        
        // Show loading
        this.showLoading(true);
        
        try {
            // Make API call to simple_api.php
            const response = await fetch('simple_api.php/chat', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    message: message,
                    user_id: this.getUserId(),
                    language: 'auto',
                    chat_history: this.chatHistory.slice(-10).map(msg => ({
                        role: msg.type === 'user' ? 'user' : 'assistant',
                        content: msg.text
                    }))
                })
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.error || `HTTP ${response.status}`);
            }

            const data = await response.json();
            const aiResponse = data.response;

            // Add AI response
            this.addMessage('ai', aiResponse);

            // Update chat title if it's the first message
            if (this.chatHistory.length === 2) {
                this.updateChatTitle(this.currentChatId, message.substring(0, 30) + '...');
            }

            // Show source info
            if (data.source === 'fallback') {
                this.showToast('Running in fallback mode - configure OpenAI API for enhanced responses', 'info');
            } else if (data.source === 'openai') {
                console.log(`✅ OpenAI API response - Tokens used: ${data.tokens_used || 0}`);
            }

        } catch (error) {
            console.error('Chat API Error:', error);

            // More specific error messages
            let errorMessage = 'Waan ka xumahay, khalad ayaa dhacay. Fadlan mar kale isku day.';
            let toastMessage = 'Khalad ayaa dhacay';

            if (error.message.includes('401')) {
                errorMessage = 'API key-gu ma sax aha. Fadlan hubi OpenAI API key-ga.';
                toastMessage = 'API key khalad ah';
            } else if (error.message.includes('429')) {
                errorMessage = 'API service temporarily unavailable. Please try again later.';
                toastMessage = 'Service unavailable';
            } else if (error.message.includes('Rate limit exceeded')) {
                errorMessage = 'API service temporarily unavailable. Please try again later.';
                toastMessage = 'Service unavailable';
            } else if (error.message.includes('Failed to fetch')) {
                errorMessage = 'Server-ka lama gaari karo. Hubi in server-ku shaqeynayo.';
                toastMessage = 'Server connection failed';
            } else if (error.message.includes('500')) {
                errorMessage = 'Server khalad ah. Fadlan mar kale isku day.';
                toastMessage = 'Server error';
            }

            this.addMessage('ai', errorMessage);
            this.showToast(toastMessage, 'error');
        } finally {
            this.showLoading(false);
            this.hideTypingIndicator();
        }
    }

    async makeAPIRequest(messages, apiKey) {
        console.log('🔄 Making API request with key:', apiKey.substring(0, 20) + '...');
        console.log('📝 Messages:', messages);

        // Mock API Response for demo purposes (remove when you have working API key)
        if (this.useMockAPI) {
            return this.getMockResponse(messages);
        }

        try {
            const requestBody = {
                model: 'gpt-4o-mini',
                messages: messages,
                max_tokens: 1000,
                temperature: 0.7
            };

            console.log('📤 Request body:', requestBody);

            const response = await fetch(this.apiUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${apiKey}`
                },
                body: JSON.stringify(requestBody)
            });

            console.log('📥 Response status:', response.status);

            if (!response.ok) {
                const errorText = await response.text();
                console.error(`❌ API Error ${response.status}:`, errorText);

                // If API fails, switch to mock mode
                console.log('🔄 Switching to mock API mode...');
                this.useMockAPI = true;
                this.showToast('API key ma shaqaynayo. Mock responses la isticmaalayaa.', 'warning');
                return this.getMockResponse(messages);
            }

            const data = await response.json();
            console.log('✅ API Response:', data);
            return { success: true, data: data };

        } catch (error) {
            console.error('🌐 Network error:', error);

            // If network fails, switch to mock mode
            console.log('🔄 Switching to mock API mode due to network error...');
            this.useMockAPI = true;
            this.showToast('Network khalad. Mock responses la isticmaalayaa.', 'warning');
            return this.getMockResponse(messages);
        }
    }

    getMockResponse(messages) {
        console.log('🤖 Using mock API response');

        // Get the user's message
        const userMessage = messages[messages.length - 1].content.toLowerCase();

        // Generate appropriate Somali responses based on user input
        let response = '';

        if (userMessage.includes('salam') || userMessage.includes('hello') || userMessage.includes('hi')) {
            response = 'Salam alaykum! Waan ku soo dhaweynayaa Tincada AI. Sidee kuu caawin karaa maanta?';
        } else if (userMessage.includes('sidee tahay') || userMessage.includes('how are you')) {
            response = 'Alhamdulillah, waan fiicnahay! Waxaan ahay Tincada AI, kaaliyahaaga AI ah. Maxaan kuu qaban karaa?';
        } else if (userMessage.includes('maxaad tahay') || userMessage.includes('what are you')) {
            response = 'Waxaan ahay Tincada AI, chatbot casri ah oo af Soomaali ku hadla. Waxaan kaa caawin karaa su\'aalo badan, qorista, tarjumaad, iyo waxyaabo kale badan.';
        } else if (userMessage.includes('caawimaad') || userMessage.includes('help')) {
            response = 'Dabcan! Waxaan kaa caawin karaa:\n\n• Su\'aalo guud\n• Qorista iyo tafsiirka\n• Tarjumaad\n• Xisaab iyo cilmi\n• Talo iyo hagitaan\n\nMaxaad rabto inaad ka weydiiso?';
        } else if (userMessage.includes('mahadsanid') || userMessage.includes('thank')) {
            response = 'Adaa mudan! Waxaan ku faraxsanahay inaan ku caawinay. Haddii aad wax kale u baahan tahay, ha ka yaabina inaad i weydiiso!';
        } else if (userMessage.includes('waa maxay') || userMessage.includes('what is')) {
            response = 'Waa su\'aal fiican! Waxaan jeclaan lahaa inaan kuu sharaxo, laakiin waxaan u baahanahay macluumaad dheeraad ah si aan ugu jawaabo si sax ah. Fadlan ii sheeg waxaad rabto inaad ka ogaato.';
        } else if (userMessage.includes('gabay') || userMessage.includes('poem')) {
            response = 'Gabayga waa dhaxal qiimo leh oo Soomaaliyeed! Halkan waxaa ah gabay gaaban:\n\n"Guulaha raadinta, gacanta ku hay,\nGeesinimo iyo dadaal, gargaar ma leh,\nGacankaaga ku tiirsanow, guul baad heli."';
        } else if (userMessage.includes('canjeero') || userMessage.includes('food')) {
            response = 'Canjeero waa cunto caadi ah oo Soomaaliyeed! Waxay ka kooban tahay:\n\n• Bur (flour)\n• Biyo\n• Khamiir (yeast)\n• Sonkor yar\n\nWaxaa lagu kariyaa alwaax kulul, waxaana la cunaa subaxda shaah iyo hilib ama digaag.';
        } else if (userMessage.includes('code') || userMessage.includes('javascript') || userMessage.includes('python') || userMessage.includes('html') || userMessage.includes('css') || userMessage.includes('programming') || userMessage.includes('function') || userMessage.includes('samee') && (userMessage.includes('function') || userMessage.includes('script') || userMessage.includes('website'))) {
            // Generate code examples based on request
            if (userMessage.includes('javascript') || userMessage.includes('js')) {
                response = `Halkan waa JavaScript function oo modern ah:

\`\`\`javascript
// Modern JavaScript function with ES6+ features
const processUserData = async (userData) => {
    try {
        // Validate input data
        if (!userData || typeof userData !== 'object') {
            throw new Error('Invalid user data provided');
        }

        // Destructure user data
        const { name, email, age, preferences = {} } = userData;

        // Validate required fields
        if (!name || !email) {
            throw new Error('Name and email are required');
        }

        // Process user preferences
        const defaultPreferences = {
            theme: 'dark',
            language: 'so',
            notifications: true
        };

        const userPrefs = { ...defaultPreferences, ...preferences };

        // Create user profile
        const userProfile = {
            id: generateUserId(),
            name: name.trim(),
            email: email.toLowerCase(),
            age: parseInt(age) || 0,
            preferences: userPrefs,
            createdAt: new Date().toISOString(),
            isActive: true
        };

        // Save to database (simulated)
        await saveUserProfile(userProfile);

        console.log('✅ User profile created successfully:', userProfile);
        return userProfile;

    } catch (error) {
        console.error('❌ Error processing user data:', error);
        throw error;
    }
};

// Helper function to generate unique user ID
const generateUserId = () => {
    return 'user_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
};

// Usage example
const newUser = {
    name: 'Ahmed Mohamed',
    email: '<EMAIL>',
    age: 25,
    preferences: {
        theme: 'light',
        language: 'so'
    }
};

processUserData(newUser)
    .then(profile => console.log('User created:', profile))
    .catch(error => console.error('Failed to create user:', error));
\`\`\`

Function-kan wuxuu isticmaalaa:
- **ES6+ syntax** - Arrow functions, destructuring, template literals
- **Async/await** - Modern asynchronous programming
- **Error handling** - Try/catch blocks
- **Data validation** - Input checking and sanitization
- **Default parameters** - Fallback values for missing data`;
            } else if (userMessage.includes('python')) {
                response = `Halkan waa Python script oo data analysis ah:

\`\`\`python
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
import seaborn as sns

class DataAnalyzer:
    """
    Advanced data analysis class for processing user data
    """

    def __init__(self, data_source=None):
        self.data = None
        self.processed_data = None
        self.results = {}

        if data_source:
            self.load_data(data_source)

    def load_data(self, source):
        """Load data from various sources"""
        try:
            if isinstance(source, str):
                # Load from file
                if source.endswith('.csv'):
                    self.data = pd.read_csv(source)
                elif source.endswith('.json'):
                    self.data = pd.read_json(source)
                else:
                    raise ValueError(f"Unsupported file format: {source}")
            elif isinstance(source, pd.DataFrame):
                self.data = source.copy()
            else:
                raise ValueError("Invalid data source")

            print(f"✅ Data loaded successfully: {self.data.shape}")
            return True

        except Exception as e:
            print(f"❌ Error loading data: {e}")
            return False

    def clean_data(self):
        """Clean and preprocess the data"""
        if self.data is None:
            raise ValueError("No data loaded")

        # Create a copy for processing
        self.processed_data = self.data.copy()

        # Remove duplicates
        initial_rows = len(self.processed_data)
        self.processed_data = self.processed_data.drop_duplicates()
        duplicates_removed = initial_rows - len(self.processed_data)

        # Handle missing values
        numeric_columns = self.processed_data.select_dtypes(include=[np.number]).columns
        categorical_columns = self.processed_data.select_dtypes(include=['object']).columns

        # Fill numeric columns with median
        for col in numeric_columns:
            self.processed_data[col].fillna(
                self.processed_data[col].median(), inplace=True
            )

        # Fill categorical columns with mode
        for col in categorical_columns:
            mode_value = self.processed_data[col].mode()
            if not mode_value.empty:
                self.processed_data[col].fillna(mode_value[0], inplace=True)

        print(f"🧹 Data cleaned: {duplicates_removed} duplicates removed")
        return self.processed_data

    def analyze_patterns(self):
        """Perform statistical analysis"""
        if self.processed_data is None:
            self.clean_data()

        analysis_results = {
            'summary_stats': self.processed_data.describe(),
            'correlation_matrix': self.processed_data.corr(),
            'missing_values': self.processed_data.isnull().sum(),
            'data_types': self.processed_data.dtypes
        }

        self.results['patterns'] = analysis_results
        print("📊 Pattern analysis completed")
        return analysis_results

    def generate_insights(self):
        """Generate actionable insights from the data"""
        if not self.results:
            self.analyze_patterns()

        insights = []

        # Check for high correlations
        corr_matrix = self.results['patterns']['correlation_matrix']
        high_corr = np.where(np.abs(corr_matrix) > 0.7)

        for i, j in zip(high_corr[0], high_corr[1]):
            if i != j:  # Avoid self-correlation
                col1, col2 = corr_matrix.index[i], corr_matrix.columns[j]
                corr_value = corr_matrix.iloc[i, j]
                insights.append(f"Strong correlation between {col1} and {col2}: {corr_value:.2f}")

        # Check for outliers
        numeric_data = self.processed_data.select_dtypes(include=[np.number])
        for col in numeric_data.columns:
            Q1 = numeric_data[col].quantile(0.25)
            Q3 = numeric_data[col].quantile(0.75)
            IQR = Q3 - Q1
            outliers = len(numeric_data[(numeric_data[col] < Q1 - 1.5*IQR) |
                                      (numeric_data[col] > Q3 + 1.5*IQR)])
            if outliers > 0:
                insights.append(f"{col} has {outliers} potential outliers")

        self.results['insights'] = insights
        return insights

    def create_visualizations(self):
        """Create data visualizations"""
        if self.processed_data is None:
            self.clean_data()

        # Set up the plotting style
        plt.style.use('seaborn-v0_8')
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))

        # Correlation heatmap
        sns.heatmap(self.processed_data.corr(), annot=True, cmap='coolwarm',
                   center=0, ax=axes[0,0])
        axes[0,0].set_title('Correlation Matrix')

        # Distribution plots for numeric columns
        numeric_cols = self.processed_data.select_dtypes(include=[np.number]).columns
        if len(numeric_cols) > 0:
            self.processed_data[numeric_cols[0]].hist(bins=30, ax=axes[0,1])
            axes[0,1].set_title(f'Distribution of {numeric_cols[0]}')

        # Box plot for outlier detection
        if len(numeric_cols) > 1:
            self.processed_data.boxplot(column=numeric_cols[:4], ax=axes[1,0])
            axes[1,0].set_title('Box Plot - Outlier Detection')

        # Missing values visualization
        missing_data = self.processed_data.isnull().sum()
        missing_data[missing_data > 0].plot(kind='bar', ax=axes[1,1])
        axes[1,1].set_title('Missing Values by Column')

        plt.tight_layout()
        plt.savefig('data_analysis_report.png', dpi=300, bbox_inches='tight')
        plt.show()

        print("📈 Visualizations created and saved")

# Usage example
if __name__ == "__main__":
    # Create sample data
    sample_data = pd.DataFrame({
        'age': np.random.randint(18, 80, 1000),
        'income': np.random.normal(50000, 15000, 1000),
        'education': np.random.choice(['High School', 'Bachelor', 'Master', 'PhD'], 1000),
        'satisfaction': np.random.randint(1, 11, 1000)
    })

    # Initialize analyzer
    analyzer = DataAnalyzer(sample_data)

    # Perform analysis
    cleaned_data = analyzer.clean_data()
    patterns = analyzer.analyze_patterns()
    insights = analyzer.generate_insights()

    # Display results
    print("\\n🔍 Key Insights:")
    for insight in insights:
        print(f"• {insight}")

    # Create visualizations
    analyzer.create_visualizations()
\`\`\`

Script-kan wuxuu sameeyaa:
- **Data loading** - CSV, JSON, DataFrame support
- **Data cleaning** - Duplicates, missing values, outliers
- **Statistical analysis** - Correlations, distributions, patterns
- **Visualization** - Heatmaps, histograms, box plots
- **Insights generation** - Automated pattern detection`;
            } else if (userMessage.includes('html')) {
                response = `Halkan waa modern HTML structure oo responsive ah:

\`\`\`html
<!DOCTYPE html>
<html lang="so">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Modern responsive website with advanced features">
    <meta name="keywords" content="responsive, modern, HTML5, CSS3, JavaScript">
    <meta name="author" content="Tincada AI">
    <title>Modern Responsive Website</title>

    <!-- Preload critical resources -->
    <link rel="preload" href="styles.css" as="style">
    <link rel="preload" href="script.js" as="script">

    <!-- Stylesheets -->
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico">

    <!-- Open Graph meta tags for social sharing -->
    <meta property="og:title" content="Modern Responsive Website">
    <meta property="og:description" content="Advanced HTML5 website with modern features">
    <meta property="og:image" content="/images/og-image.jpg">
    <meta property="og:url" content="https://example.com">
    <meta property="og:type" content="website">
</head>
<body>
    <!-- Skip to main content for accessibility -->
    <a href="#main-content" class="skip-link">Skip to main content</a>

    <!-- Header with navigation -->
    <header class="header" role="banner">
        <nav class="navbar" role="navigation" aria-label="Main navigation">
            <div class="nav-container">
                <div class="nav-brand">
                    <a href="/" class="brand-link" aria-label="Home">
                        <img src="/images/logo.svg" alt="Company Logo" class="logo">
                        <span class="brand-text">Tincada</span>
                    </a>
                </div>

                <button class="nav-toggle" aria-label="Toggle navigation" aria-expanded="false">
                    <span class="hamburger-line"></span>
                    <span class="hamburger-line"></span>
                    <span class="hamburger-line"></span>
                </button>

                <ul class="nav-menu" role="menubar">
                    <li class="nav-item" role="none">
                        <a href="#home" class="nav-link" role="menuitem">Home</a>
                    </li>
                    <li class="nav-item" role="none">
                        <a href="#about" class="nav-link" role="menuitem">About</a>
                    </li>
                    <li class="nav-item" role="none">
                        <a href="#services" class="nav-link" role="menuitem">Services</a>
                    </li>
                    <li class="nav-item" role="none">
                        <a href="#contact" class="nav-link" role="menuitem">Contact</a>
                    </li>
                </ul>
            </div>
        </nav>
    </header>

    <!-- Main content -->
    <main id="main-content" class="main-content" role="main">
        <!-- Hero section -->
        <section class="hero" id="home" aria-labelledby="hero-title">
            <div class="hero-container">
                <div class="hero-content">
                    <h1 id="hero-title" class="hero-title">
                        Welcome to the Future of
                        <span class="highlight">Web Development</span>
                    </h1>
                    <p class="hero-description">
                        Experience cutting-edge technology with our modern,
                        responsive, and accessible web solutions.
                    </p>
                    <div class="hero-actions">
                        <a href="#services" class="btn btn-primary">
                            <i class="fas fa-rocket" aria-hidden="true"></i>
                            Get Started
                        </a>
                        <a href="#about" class="btn btn-secondary">
                            <i class="fas fa-info-circle" aria-hidden="true"></i>
                            Learn More
                        </a>
                    </div>
                </div>
                <div class="hero-visual">
                    <picture>
                        <source media="(max-width: 768px)" srcset="/images/hero-mobile.webp">
                        <source media="(max-width: 1200px)" srcset="/images/hero-tablet.webp">
                        <img src="/images/hero-desktop.webp" alt="Modern web development illustration"
                             class="hero-image" loading="lazy">
                    </picture>
                </div>
            </div>
        </section>

        <!-- Features section -->
        <section class="features" id="services" aria-labelledby="features-title">
            <div class="container">
                <header class="section-header">
                    <h2 id="features-title" class="section-title">Our Features</h2>
                    <p class="section-description">
                        Discover what makes our solutions stand out
                    </p>
                </header>

                <div class="features-grid">
                    <article class="feature-card">
                        <div class="feature-icon" aria-hidden="true">
                            <i class="fas fa-mobile-alt"></i>
                        </div>
                        <h3 class="feature-title">Responsive Design</h3>
                        <p class="feature-description">
                            Perfect display on all devices, from mobile to desktop
                        </p>
                    </article>

                    <article class="feature-card">
                        <div class="feature-icon" aria-hidden="true">
                            <i class="fas fa-bolt"></i>
                        </div>
                        <h3 class="feature-title">Lightning Fast</h3>
                        <p class="feature-description">
                            Optimized performance for the best user experience
                        </p>
                    </article>

                    <article class="feature-card">
                        <div class="feature-icon" aria-hidden="true">
                            <i class="fas fa-shield-alt"></i>
                        </div>
                        <h3 class="feature-title">Secure & Reliable</h3>
                        <p class="feature-description">
                            Built with security best practices and reliability in mind
                        </p>
                    </article>
                </div>
            </div>
        </section>

        <!-- Contact form -->
        <section class="contact" id="contact" aria-labelledby="contact-title">
            <div class="container">
                <header class="section-header">
                    <h2 id="contact-title" class="section-title">Get In Touch</h2>
                    <p class="section-description">
                        Ready to start your project? Let's talk!
                    </p>
                </header>

                <form class="contact-form" novalidate>
                    <div class="form-group">
                        <label for="name" class="form-label">Name *</label>
                        <input type="text" id="name" name="name" class="form-input"
                               required aria-describedby="name-error">
                        <span id="name-error" class="error-message" role="alert"></span>
                    </div>

                    <div class="form-group">
                        <label for="email" class="form-label">Email *</label>
                        <input type="email" id="email" name="email" class="form-input"
                               required aria-describedby="email-error">
                        <span id="email-error" class="error-message" role="alert"></span>
                    </div>

                    <div class="form-group">
                        <label for="message" class="form-label">Message *</label>
                        <textarea id="message" name="message" class="form-textarea"
                                  rows="5" required aria-describedby="message-error"></textarea>
                        <span id="message-error" class="error-message" role="alert"></span>
                    </div>

                    <button type="submit" class="btn btn-primary btn-full">
                        <i class="fas fa-paper-plane" aria-hidden="true"></i>
                        Send Message
                    </button>
                </form>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="footer" role="contentinfo">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3 class="footer-title">Tincada</h3>
                    <p class="footer-description">
                        Building the future of web development with modern technologies.
                    </p>
                </div>

                <div class="footer-section">
                    <h4 class="footer-subtitle">Quick Links</h4>
                    <ul class="footer-links">
                        <li><a href="#home">Home</a></li>
                        <li><a href="#about">About</a></li>
                        <li><a href="#services">Services</a></li>
                        <li><a href="#contact">Contact</a></li>
                    </ul>
                </div>

                <div class="footer-section">
                    <h4 class="footer-subtitle">Follow Us</h4>
                    <div class="social-links">
                        <a href="#" class="social-link" aria-label="Facebook">
                            <i class="fab fa-facebook" aria-hidden="true"></i>
                        </a>
                        <a href="#" class="social-link" aria-label="Twitter">
                            <i class="fab fa-twitter" aria-hidden="true"></i>
                        </a>
                        <a href="#" class="social-link" aria-label="LinkedIn">
                            <i class="fab fa-linkedin" aria-hidden="true"></i>
                        </a>
                    </div>
                </div>
            </div>

            <div class="footer-bottom">
                <p>&copy; 2024 Tincada. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="script.js" defer></script>

    <!-- Service Worker for PWA -->
    <script>
        if ('serviceWorker' in navigator) {
            navigator.serviceWorker.register('/sw.js');
        }
    </script>
</body>
</html>
\`\`\`

HTML-kan wuxuu leeyahay:
- **Semantic HTML5** - Proper structure and accessibility
- **Responsive design** - Mobile-first approach
- **SEO optimization** - Meta tags and structured data
- **Accessibility** - ARIA labels and keyboard navigation
- **Performance** - Lazy loading and resource optimization`;
            } else if (userMessage.includes('css')) {
                response = `Halkan waa modern CSS oo advanced features leh:

\`\`\`css
/* Modern CSS with Advanced Features */

/* CSS Custom Properties (Variables) */
:root {
    /* Color palette */
    --primary-color: #4ecdc4;
    --secondary-color: #44a08d;
    --accent-color: #ff6b6b;
    --text-primary: #2c3e50;
    --text-secondary: #7f8c8d;
    --background-light: #ffffff;
    --background-dark: #1a1a1a;
    --surface: #f8f9fa;
    --border-color: #e9ecef;

    /* Typography */
    --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    --font-secondary: 'Fira Code', 'Consolas', monospace;
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 2rem;

    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;

    /* Shadows */
    --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.15);

    /* Border radius */
    --radius-sm: 0.25rem;
    --radius-md: 0.5rem;
    --radius-lg: 1rem;
    --radius-xl: 1.5rem;
    --radius-full: 9999px;

    /* Transitions */
    --transition-fast: 150ms ease;
    --transition-base: 250ms ease;
    --transition-slow: 350ms ease;
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    :root {
        --text-primary: #ffffff;
        --text-secondary: #a0a0a0;
        --background-light: #1a1a1a;
        --surface: #2d2d2d;
        --border-color: #404040;
    }
}

/* Modern CSS Reset */
*,
*::before,
*::after {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

html {
    scroll-behavior: smooth;
    font-size: 16px;
}

body {
    font-family: var(--font-primary);
    font-size: var(--font-size-base);
    line-height: 1.6;
    color: var(--text-primary);
    background-color: var(--background-light);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Container system */
.container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-md);
}

@media (min-width: 768px) {
    .container {
        padding: 0 var(--spacing-lg);
    }
}

/* Modern Grid System */
.grid {
    display: grid;
    gap: var(--spacing-md);
}

.grid-cols-1 { grid-template-columns: repeat(1, 1fr); }
.grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
.grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
.grid-cols-4 { grid-template-columns: repeat(4, 1fr); }

@media (max-width: 768px) {
    .grid-cols-2,
    .grid-cols-3,
    .grid-cols-4 {
        grid-template-columns: 1fr;
    }
}

/* Flexbox utilities */
.flex {
    display: flex;
}

.flex-col {
    flex-direction: column;
}

.items-center {
    align-items: center;
}

.justify-center {
    justify-content: center;
}

.justify-between {
    justify-content: space-between;
}

/* Modern Button Component */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-lg);
    font-family: inherit;
    font-size: var(--font-size-base);
    font-weight: 600;
    text-decoration: none;
    border: 2px solid transparent;
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: all var(--transition-base);
    position: relative;
    overflow: hidden;
    user-select: none;

    /* Prevent double-tap zoom on iOS */
    touch-action: manipulation;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left var(--transition-slow);
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    box-shadow: var(--shadow-md);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-secondary {
    background: transparent;
    color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-secondary:hover {
    background: var(--primary-color);
    color: white;
}

/* Card Component */
.card {
    background: var(--surface);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: var(--spacing-xl);
    box-shadow: var(--shadow-sm);
    transition: all var(--transition-base);
}

.card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
}

/* Modern Form Styles */
.form-group {
    margin-bottom: var(--spacing-lg);
}

.form-label {
    display: block;
    font-weight: 600;
    margin-bottom: var(--spacing-sm);
    color: var(--text-primary);
}

.form-input,
.form-textarea {
    width: 100%;
    padding: var(--spacing-sm) var(--spacing-md);
    font-family: inherit;
    font-size: var(--font-size-base);
    border: 2px solid var(--border-color);
    border-radius: var(--radius-md);
    background: var(--background-light);
    color: var(--text-primary);
    transition: all var(--transition-base);
}

.form-input:focus,
.form-textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(78, 205, 196, 0.1);
}

/* Advanced Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

@keyframes gradient {
    0%, 100% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
}

/* Animation classes */
.animate-fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

.animate-slide-in-left {
    animation: slideInLeft 0.8s ease-out;
}

.animate-pulse {
    animation: pulse 2s infinite;
}

.animate-gradient {
    background: linear-gradient(-45deg, var(--primary-color), var(--secondary-color), var(--accent-color));
    background-size: 400% 400%;
    animation: gradient 15s ease infinite;
}

/* Modern Loading Spinner */
.spinner {
    width: 40px;
    height: 40px;
    border: 3px solid var(--border-color);
    border-top: 3px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive Typography */
.text-xs { font-size: var(--font-size-xs); }
.text-sm { font-size: var(--font-size-sm); }
.text-base { font-size: var(--font-size-base); }
.text-lg { font-size: var(--font-size-lg); }
.text-xl { font-size: var(--font-size-xl); }
.text-2xl { font-size: var(--font-size-2xl); }
.text-3xl { font-size: var(--font-size-3xl); }

/* Utility classes */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

.hidden {
    display: none;
}

.visible {
    display: block;
}

/* Modern scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--surface);
}

::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: var(--radius-full);
}

::-webkit-scrollbar-thumb:hover {
    background: var(--text-secondary);
}

/* Print styles */
@media print {
    .no-print {
        display: none !important;
    }

    body {
        font-size: 12pt;
        line-height: 1.4;
        color: black;
        background: white;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    :root {
        --primary-color: #000000;
        --text-primary: #000000;
        --background-light: #ffffff;
        --border-color: #000000;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}
\`\`\`

CSS-kan wuxuu leeyahay:
- **CSS Custom Properties** - Modern variable system
- **Responsive design** - Mobile-first approach
- **Dark mode support** - Automatic theme switching
- **Advanced animations** - Smooth transitions and effects
- **Accessibility** - Screen reader and keyboard support
- **Modern components** - Buttons, cards, forms
- **Utility classes** - Flexible styling system`;
            } else {
                // Generic code response
                response = `Halkan waa code example oo modern ah:

\`\`\`javascript
// Modern JavaScript with best practices
const createAdvancedComponent = () => {
    // Use modern ES6+ features
    const config = {
        theme: 'dark',
        responsive: true,
        animations: true
    };

    // Destructuring and default parameters
    const initialize = ({ theme = 'light', ...options } = {}) => {
        console.log('Initializing with theme:', theme);
        return { theme, ...options };
    };

    // Async/await for better readability
    const fetchData = async (url) => {
        try {
            const response = await fetch(url);
            if (!response.ok) {
                throw new Error(\`HTTP error! status: \${response.status}\`);
            }
            return await response.json();
        } catch (error) {
            console.error('Fetch error:', error);
            throw error;
        }
    };

    // Modern class with private fields
    class DataProcessor {
        #privateData = new Map();

        constructor(initialData = []) {
            this.#privateData.set('items', initialData);
        }

        process(callback) {
            const items = this.#privateData.get('items');
            return items.map(callback);
        }

        get count() {
            return this.#privateData.get('items').length;
        }
    }

    return {
        initialize,
        fetchData,
        DataProcessor
    };
};

// Usage
const component = createAdvancedComponent();
const processor = new component.DataProcessor([1, 2, 3, 4, 5]);
console.log('Processed:', processor.process(x => x * 2));
\`\`\`

Code-kan wuxuu isticmaalaa:
- **Modern JavaScript** - ES6+ features
- **Error handling** - Try/catch blocks
- **Async programming** - Promises and async/await
- **Class syntax** - Private fields and methods
- **Best practices** - Clean, readable code`;
            }
        } else {
            // Generic helpful response
            const responses = [
                'Waa su\'aal xiiso leh! Waxaan jeclaan lahaa inaan kuu caawiyo. Fadlan ii sheeg wax dheeraad ah si aan ugu jawaabo si fiican.',
                'Mahadsanid su\'aashaada! Waxaan ahay Tincada AI oo waxaan ku caawin karaa waxyaabo badan. Maxaad gaar ahaan rabto inaad ogaato?',
                'Fiican! Waxaan halkan u joogaa si aan kuu caawiyo. Haddii aad rabto jawaab gaar ah, fadlan ii sheeg faahfaahin dheeraad ah.',
                'Waa macquul! Waxaan ku caawin karaa su\'aalkaaga. Fadlan ii sheeg wax dheeraad ah si aan ugu jawaabo si buuxda.',
                'Aad ayaad u mahadsantahay inaad i weydiisay! Waxaan jeclaan lahaa inaan kuu bixiyo jawaab fiican. Maxaad gaar ahaan rabto inaad ka ogaato?'
            ];
            response = responses[Math.floor(Math.random() * responses.length)];
        }

        // Simulate API delay
        return new Promise(resolve => {
            setTimeout(() => {
                resolve({
                    success: true,
                    data: {
                        choices: [{
                            message: {
                                content: response
                            }
                        }]
                    }
                });
            }, 1000 + Math.random() * 2000); // 1-3 second delay
        });
    }

    addMessage(type, text) {
        const chatMessages = document.getElementById('chatMessages');
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${type}`;
        
        const avatar = document.createElement('div');
        avatar.className = 'message-avatar';
        
        if (type === 'user' && this.currentUser && this.currentUser.profileImage) {
            avatar.innerHTML = `<img src="${this.currentUser.profileImage}" alt="${this.currentUser.fullName}">`;
        } else if (type === 'user') {
            avatar.innerHTML = '<i class="fas fa-user"></i>';
        } else {
            avatar.innerHTML = '<img src="images/icon.jpg" alt="Tincada AI" class="ai-avatar">';
        }
        
        const content = document.createElement('div');
        content.className = 'message-content';
        // Format message text based on type
        const formattedText = type === 'ai' ? this.formatAIMessage(text) : this.escapeHtml(text);

        content.innerHTML = `
            <div class="message-text">${formattedText}</div>
            <div class="message-time">${this.getCurrentTime()}</div>
        `;
        
        messageDiv.appendChild(avatar);
        messageDiv.appendChild(content);
        chatMessages.appendChild(messageDiv);
        
        // Scroll to bottom
        chatMessages.scrollTop = chatMessages.scrollHeight;
        
        // Update chat history
        this.chatHistory.push({
            type,
            text,
            timestamp: Date.now()
        });

        // Update message counts
        this.messageCount++;
        this.currentChatMessageCount++;

        // Only count user messages towards daily limit
        if (type === 'user') {
            this.dailyMessageCount++;
            this.saveDailyMessageCount();
        }

        this.saveChatData();
    }

    formatMessage(text) {
        return text
            .replace(/\n/g, '<br>')
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            .replace(/\*(.*?)\*/g, '<em>$1</em>');
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML.replace(/\n/g, '<br>');
    }

    formatAIMessage(text) {
        console.log('🔍 Formatting AI message:', text.substring(0, 100) + '...');

        // Enhanced text formatting with code card support
        let formattedText = text;

        // Process code blocks first (```language ... ```)
        formattedText = formattedText.replace(/```(\w+)?\n?([\s\S]*?)```/g, (match, language, code) => {
            const lang = language || 'text';
            const cleanCode = code.trim();
            const codeId = 'code_' + Math.random().toString(36).substr(2, 9);

            console.log('🎨 Found code block with language:', lang);

            const cardHtml = `<div class="code-card" data-language="${lang}">
                <div class="code-header">
                    <div class="code-language">
                        <i class="fas fa-code"></i>
                        <span>${lang.toUpperCase()}</span>
                    </div>
                    <div class="code-actions">
                        <button class="code-btn copy-btn" onclick="tincadaAI.copyCode('${codeId}')" title="Copy code">
                            <i class="fas fa-copy"></i>
                            Copy
                        </button>
                        <button class="code-btn edit-btn" onclick="tincadaAI.editCode('${codeId}')" title="Edit code">
                            <i class="fas fa-edit"></i>
                            Edit
                        </button>
                    </div>
                </div>
                <div class="code-content">
                    <pre id="${codeId}"><code class="language-${lang}">${this.escapeHtml(cleanCode)}</code></pre>
                </div>
            </div>`;

            // Apply syntax highlighting after DOM insertion
            setTimeout(() => {
                const codeElement = document.getElementById(codeId);
                if (codeElement && window.hljs) {
                    hljs.highlightElement(codeElement.querySelector('code'));
                }
            }, 100);

            return cardHtml;
        });

        // Process inline code (`code`)
        formattedText = formattedText.replace(/`([^`]+)`/g, '<code class="inline-code">$1</code>');

        // Process other formatting
        formattedText = formattedText
            .replace(/\n/g, '<br>')
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            .replace(/\*(.*?)\*/g, '<em>$1</em>')
            .replace(/__(.*?)__/g, '<u>$1</u>')
            .replace(/~~(.*?)~~/g, '<del>$1</del>');

        return formattedText;

        // Format lists
        formatted = formatted.replace(/^\* (.+)$/gm, '<li>$1</li>');
        formatted = formatted.replace(/(<li>.*<\/li>)/s, '<ul>$1</ul>');

        // Format numbered lists
        formatted = formatted.replace(/^\d+\. (.+)$/gm, '<li>$1</li>');

        console.log('✅ Message formatting complete');
        return formatted;
    }

    // Detect code patterns and format them automatically
    detectAndFormatCode(text) {
        // Patterns that indicate code
        const codePatterns = [
            // Function definitions
            /^(def|function|const|let|var|class|import|export|from)\s+\w+.*$/gm,
            // HTML tags
            /^<[^>]+>.*<\/[^>]+>$/gm,
            // CSS selectors and properties
            /^[.#]?[\w-]+\s*{[\s\S]*?}$/gm,
            // Python/JavaScript patterns
            /^(if|for|while|try|catch|finally|else|elif)\s*.*:?\s*$/gm,
            // SQL patterns
            /^(SELECT|INSERT|UPDATE|DELETE|CREATE|ALTER|DROP)\s+.*$/gmi
        ];

        let formatted = text;

        // Check if text contains code patterns
        const hasCodePatterns = codePatterns.some(pattern => pattern.test(text));

        if (hasCodePatterns) {
            // Try to detect language
            let detectedLang = 'javascript'; // default

            if (/^def\s+\w+|import\s+\w+|if\s+.*:|for\s+.*:|while\s+.*:/.test(text)) {
                detectedLang = 'python';
            } else if (/<[^>]+>.*<\/[^>]+>/.test(text)) {
                detectedLang = 'html';
            } else if (/[.#]?[\w-]+\s*{[\s\S]*?}/.test(text)) {
                detectedLang = 'css';
            } else if (/SELECT|INSERT|UPDATE|DELETE|CREATE/i.test(text)) {
                detectedLang = 'sql';
            }

            // If it looks like code but isn't wrapped in backticks, wrap it
            if (!text.includes('```') && hasCodePatterns) {
                return aiCodeBlock.createCodeBlock(text, detectedLang);
            }
        }

        return formatted;
    }

    getCurrentTime() {
        const now = new Date();
        return now.toLocaleTimeString('so-SO', { 
            hour: '2-digit', 
            minute: '2-digit' 
        });
    }

    generateChatId() {
        return 'chat_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    getUserId() {
        // Get or create user ID
        let userId = localStorage.getItem('tincada_user_id');
        if (!userId) {
            userId = 'user_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
            localStorage.setItem('tincada_user_id', userId);
        }
        return userId;
    }

    addChatToList(chatId, title) {
        const chatList = document.getElementById('chatList');
        const chatItem = document.createElement('div');
        chatItem.className = 'chat-item active';
        chatItem.dataset.chatId = chatId;
        chatItem.innerHTML = `
            <div class="chat-content">
                <i class="fas fa-comment"></i>
                <span class="chat-item-text">${title}</span>
            </div>
            <div class="chat-actions">
                <button class="chat-action-btn" onclick="showChatMenu(event, '${chatId}')" title="More options">
                    <i class="fas fa-ellipsis-v"></i>
                </button>
            </div>
        `;
        
        // Remove active class from other chats
        document.querySelectorAll('.chat-item').forEach(item => {
            item.classList.remove('active');
        });
        
        chatList.insertBefore(chatItem, chatList.firstChild);
        
        // Add click event
        chatItem.addEventListener('click', () => {
            this.loadChat(chatId);
        });
    }

    updateChatTitle(chatId, title) {
        const chatItem = document.querySelector(`[data-chat-id="${chatId}"]`);
        if (chatItem) {
            const textElement = chatItem.querySelector('.chat-item-text');
            textElement.textContent = title;
        }
    }

    setupAutoResize() {
        const textareas = document.querySelectorAll('textarea');
        textareas.forEach(textarea => {
            textarea.addEventListener('input', () => {
                this.autoResizeTextarea(textarea);
            });
        });
    }

    autoResizeTextarea(textarea) {
        textarea.style.height = 'auto';
        textarea.style.height = Math.min(textarea.scrollHeight, 120) + 'px';
    }

    toggleVoiceRecording() {
        // Voice recording implementation
        this.showToast('Voice recording feature waa la dhisi doonaa', 'info');
    }

    searchChats(query) {
        const chatItems = document.querySelectorAll('.chat-item');
        chatItems.forEach(item => {
            const text = item.querySelector('.chat-item-text').textContent.toLowerCase();
            if (text.includes(query.toLowerCase())) {
                item.style.display = 'flex';
            } else {
                item.style.display = 'none';
            }
        });
    }

    switchNavigation(navId) {
        // Remove active class from all nav items
        document.querySelectorAll('.nav-item').forEach(item => {
            item.classList.remove('active');
        });
        
        // Add active class to clicked item
        document.getElementById(navId).classList.add('active');
        
        // Handle navigation logic
        switch(navId) {
            case 'chatsNav':
                // Show chats
                break;
            case 'libraryNav':
                this.showToast('Maktabada feature waa la dhisi doonaa', 'info');
                break;
            case 'soraNav':
                this.showToast('Sora feature waa la dhisi doonaa', 'info');
                break;
            case 'gptsNav':
                this.showToast('GPTs feature waa la dhisi doonaa', 'info');
                break;
        }
    }

    openSettings() {
        this.closeUserDropdown();
        document.getElementById('settingsModal').style.display = 'flex';
        this.loadSettingsContent();
    }

    closeSettings() {
        document.getElementById('settingsModal').style.display = 'none';
    }

    loadSettingsContent() {
        const modalBody = document.querySelector('.modal-body');
        modalBody.innerHTML = `
            <div class="settings-content">
                <h3>Profile Settings</h3>
                <div class="setting-group">
                    <label>Magaca Buuxa</label>
                    <input type="text" id="settingsName" value="${this.currentUser.fullName}" class="setting-input">
                </div>
                <div class="setting-group">
                    <label>Lambarka Telefoonka</label>
                    <input type="text" id="settingsPhone" value="${this.currentUser.phoneNumber}" class="setting-input">
                </div>
                <div class="setting-group">
                    <label>Sawirka Profile</label>
                    <div class="profile-upload-setting">
                        <div class="current-avatar">
                            ${this.currentUser.profileImage ?
                                `<img src="${this.currentUser.profileImage}" alt="Profile">` :
                                '<i class="fas fa-user"></i>'
                            }
                        </div>
                        <button class="change-avatar-btn" id="changeAvatarBtn">Beddel Sawirka</button>
                    </div>
                </div>

                <h3 style="margin-top: 30px;">API Settings</h3>
                <div class="setting-group">
                    <label>OpenAI API Key</label>
                    <input type="password" id="settingsApiKey" value="${this.apiKey}" class="setting-input" placeholder="sk-...">
                    <small style="color: #8e8ea0; font-size: 0.8rem; margin-top: 5px; display: block;">
                        Geli OpenAI API key cusub haddii kan hadda jira uusan shaqaynin
                    </small>
                </div>
                <div class="setting-group">
                    <label style="display: flex; align-items: center; gap: 8px;">
                        <input type="checkbox" id="useMockMode" ${this.useMockAPI ? 'checked' : ''}>
                        <span>Isticmaal Mock API (demo mode)</span>
                    </label>
                    <small style="color: #8e8ea0; font-size: 0.8rem; margin-top: 5px; display: block;">
                        Haddii API key-gu ma shaqaynin, mock responses ayaa la isticmaali doonaa
                    </small>
                </div>

                <div class="setting-actions">
                    <button class="save-settings-btn" id="saveSettingsBtn">Kaydi Isbeddelka</button>
                    <button class="cancel-settings-btn" id="cancelSettingsBtn">Ka Noqo</button>
                    <button class="test-api-btn" id="testApiBtn">Test API Key</button>
                </div>
            </div>
        `;
        
        // Add CSS for settings
        this.addSettingsCSS();
        
        // Bind settings events
        this.bindSettingsEvents();
    }

    addSettingsCSS() {
        if (!document.getElementById('settingsCSS')) {
            const style = document.createElement('style');
            style.id = 'settingsCSS';
            style.textContent = `
                .settings-content h3 {
                    color: #ffffff;
                    margin-bottom: 24px;
                    font-size: 1.2rem;
                }
                .setting-group {
                    margin-bottom: 20px;
                }
                .setting-group label {
                    display: block;
                    color: #ffffff;
                    margin-bottom: 8px;
                    font-weight: 500;
                }
                .setting-input {
                    width: 100%;
                    background: #171717;
                    border: 1px solid #3f3f3f;
                    color: #ffffff;
                    padding: 12px;
                    border-radius: 6px;
                    font-family: inherit;
                }
                .setting-input:focus {
                    outline: none;
                    border-color: #10a37f;
                }
                .profile-upload-setting {
                    display: flex;
                    align-items: center;
                    gap: 16px;
                }
                .current-avatar {
                    width: 60px;
                    height: 60px;
                    border-radius: 50%;
                    background: #10a37f;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    overflow: hidden;
                }
                .current-avatar img {
                    width: 100%;
                    height: 100%;
                    object-fit: cover;
                }
                .current-avatar i {
                    color: #ffffff;
                    font-size: 1.5rem;
                }
                .change-avatar-btn {
                    background: #2f2f2f;
                    border: 1px solid #3f3f3f;
                    color: #ffffff;
                    padding: 8px 16px;
                    border-radius: 6px;
                    cursor: pointer;
                    transition: all 0.2s ease;
                }
                .change-avatar-btn:hover {
                    background: #3f3f3f;
                }
                .setting-actions {
                    display: flex;
                    gap: 12px;
                    margin-top: 32px;
                }
                .save-settings-btn {
                    background: #10a37f;
                    border: none;
                    color: #ffffff;
                    padding: 12px 24px;
                    border-radius: 6px;
                    cursor: pointer;
                    font-weight: 500;
                    transition: all 0.2s ease;
                }
                .save-settings-btn:hover {
                    background: #0d8f6f;
                }
                .cancel-settings-btn {
                    background: #2f2f2f;
                    border: 1px solid #3f3f3f;
                    color: #ffffff;
                    padding: 12px 24px;
                    border-radius: 6px;
                    cursor: pointer;
                    transition: all 0.2s ease;
                }
                .cancel-settings-btn:hover {
                    background: #3f3f3f;
                }
                .test-api-btn {
                    background: #2563eb;
                    border: none;
                    color: #ffffff;
                    padding: 12px 24px;
                    border-radius: 6px;
                    cursor: pointer;
                    font-weight: 500;
                    transition: all 0.2s ease;
                }
                .test-api-btn:hover {
                    background: #1d4ed8;
                }
                input[type="checkbox"] {
                    width: auto;
                    margin-right: 8px;
                }
            `;
            document.head.appendChild(style);
        }
    }

    bindSettingsEvents() {
        document.getElementById('saveSettingsBtn').addEventListener('click', () => {
            this.saveSettings();
        });

        document.getElementById('cancelSettingsBtn').addEventListener('click', () => {
            this.closeSettings();
        });

        document.getElementById('changeAvatarBtn').addEventListener('click', () => {
            document.getElementById('fileInput').click();
        });

        document.getElementById('testApiBtn').addEventListener('click', () => {
            this.testApiKey();
        });

        document.getElementById('useMockMode').addEventListener('change', (e) => {
            this.useMockAPI = e.target.checked;
            if (this.useMockAPI) {
                this.showToast('Mock API mode waa la daahfuray', 'info');
            } else {
                this.showToast('Real API mode waa la daahfuray', 'info');
            }
        });
    }

    async testApiKey() {
        const apiKeyInput = document.getElementById('settingsApiKey');
        const testBtn = document.getElementById('testApiBtn');
        const apiKey = apiKeyInput.value.trim();

        if (!apiKey) {
            this.showToast('Fadlan geli API key', 'error');
            return;
        }

        testBtn.disabled = true;
        testBtn.textContent = 'Testing...';

        try {
            const testMessages = [
                {
                    role: 'system',
                    content: 'You are a helpful assistant. Respond in Somali.'
                },
                {
                    role: 'user',
                    content: 'Salam, test message'
                }
            ];

            const response = await fetch(this.apiUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${apiKey}`
                },
                body: JSON.stringify({
                    model: 'gpt-4o-mini',
                    messages: testMessages,
                    max_tokens: 50,
                    temperature: 0.7
                })
            });

            if (response.ok) {
                this.showToast('✅ API key waa shaqaynayaa!', 'success');
                this.apiKey = apiKey;
                this.useMockAPI = false;
                document.getElementById('useMockMode').checked = false;
            } else {
                const errorText = await response.text();
                this.showToast(`❌ API key ma shaqaynayo: ${response.status}`, 'error');
                console.error('API test failed:', errorText);
            }

        } catch (error) {
            this.showToast('❌ Network khalad: ' + error.message, 'error');
            console.error('API test error:', error);
        } finally {
            testBtn.disabled = false;
            testBtn.textContent = 'Test API Key';
        }
    }

    saveSettings() {
        const newName = document.getElementById('settingsName').value.trim();
        const newPhone = document.getElementById('settingsPhone').value.trim();
        const newApiKey = document.getElementById('settingsApiKey').value.trim();
        const useMock = document.getElementById('useMockMode').checked;

        if (!newName || !newPhone) {
            this.showToast('Fadlan buuxi dhammaan goobaha', 'error');
            return;
        }

        // Update user data
        this.currentUser.fullName = newName;
        this.currentUser.phoneNumber = newPhone;

        // Update API settings
        if (newApiKey) {
            this.apiKey = newApiKey;
        }
        this.useMockAPI = useMock;

        // Save to localStorage
        authManager.login(this.currentUser);
        localStorage.setItem('tincadaAI_apiKey', this.apiKey);
        localStorage.setItem('tincadaAI_useMockAPI', this.useMockAPI.toString());

        // Update UI
        this.loadUserProfile();

        this.showToast('Settings waa la kaydiyay!', 'success');
        this.updateAPIStatusBanner();
        this.closeSettings();
    }

    updateAPIStatusBanner() {
        const banner = document.getElementById('apiStatusBanner');
        const bannerDismissed = localStorage.getItem('tincadaAI_bannerDismissed');

        if (this.useMockAPI && !bannerDismissed) {
            banner.style.display = 'block';
        } else {
            banner.style.display = 'none';
        }
    }

    hideAPIStatusBanner() {
        const banner = document.getElementById('apiStatusBanner');
        banner.style.display = 'none';

        // Temporarily hide banner (user dismissed it)
        localStorage.setItem('tincadaAI_bannerDismissed', 'true');

        // Show it again after 1 hour if still using mock API
        setTimeout(() => {
            localStorage.removeItem('tincadaAI_bannerDismissed');
            this.updateAPIStatusBanner();
        }, 60 * 60 * 1000); // 1 hour
    }

    showPaymentModal() {
        document.getElementById('paymentModal').style.display = 'flex';

        // Update the modal with current usage
        const limitReached = document.querySelector('.limit-reached p');
        limitReached.innerHTML = `Waxaad diray <strong>${this.dailyMessageCount} fariin</strong> maanta. Si aad u sii waddo, fadlan doorso mid ka mid ah plans-yada hoose:`;

        this.showToast('Xadka fariimaha maalinlaha ah waa la gaaray!', 'warning');
    }

    showUpgradeModal() {
        // Show payment modal but with upgrade messaging
        document.getElementById('paymentModal').style.display = 'flex';

        // Update the modal with upgrade messaging
        const limitReached = document.querySelector('.limit-reached');
        limitReached.innerHTML = `
            <i class="fas fa-crown"></i>
            <h3>Upgrade to Tincada AI Plus!</h3>
            <p>Hel unlimited messages, advanced features, iyo priority support. Doorso plan-ka kugu habboon:</p>
        `;

        this.showToast('Upgrade to Plus for unlimited access!', 'info');
    }

    closePaymentModal() {
        document.getElementById('paymentModal').style.display = 'none';
    }

    selectPlan(planType) {
        console.log('🛒 Plan selected:', planType);

        // Show payment form
        this.showPaymentForm(planType);
    }

    processPlanUpgrade(planType) {
        let newLimit = 1000;
        let planName = '';

        switch(planType) {
            case 'premium':
                newLimit = 999999; // Unlimited
                planName = 'Premium Plan';
                break;
            case 'basic':
                newLimit = 5000;
                planName = 'Basic Plan';
                break;
            case 'daily':
                newLimit = this.dailyMessageCount + 2000;
                planName = 'Daily Pass';
                break;
        }

        // Update limits
        this.maxMessages = newLimit;

        // Save upgrade info
        const upgradeInfo = {
            plan: planType,
            planName: planName,
            upgradeDate: new Date().toISOString(),
            newLimit: newLimit
        };

        localStorage.setItem('tincadaAI_planUpgrade', JSON.stringify(upgradeInfo));

        // Close modal and show success
        this.closePaymentModal();
        this.showToast(`✅ ${planName} waa la activate gareeyay! Hadda waxaad diri kartaa ${newLimit === 999999 ? 'unlimited' : newLimit} fariin.`, 'success');

        // Update UI
        this.updateMessageCountDisplay();

        console.log('💳 Plan upgraded:', upgradeInfo);
    }

    showPaymentForm(planType) {
        // Hide payment modal and show payment form
        document.getElementById('paymentModal').style.display = 'none';
        document.getElementById('paymentFormModal').style.display = 'flex';

        // Update plan summary
        this.updatePlanSummary(planType);

        // Setup form validation
        this.setupFormValidation();

        this.showToast('Buuxi payment form si aad u dhamaystirto purchase-ka', 'info');
    }

    closePaymentFormModal() {
        document.getElementById('paymentFormModal').style.display = 'none';
    }

    backToPlans() {
        // Hide payment form and show payment modal
        document.getElementById('paymentFormModal').style.display = 'none';
        document.getElementById('paymentModal').style.display = 'flex';
    }

    updatePlanSummary(planType) {
        const planData = {
            premium: {
                name: 'Premium Plan',
                price: '$9.99/month',
                benefits: [
                    '✅ Unlimited messages',
                    '✅ Priority support',
                    '✅ Advanced AI features',
                    '✅ File upload (50MB)',
                    '✅ Voice messages'
                ]
            },
            basic: {
                name: 'Basic Plan',
                price: '$4.99/month',
                benefits: [
                    '✅ 5,000 messages/day',
                    '✅ Standard support',
                    '✅ Basic AI features',
                    '✅ File upload (10MB)'
                ]
            },
            daily: {
                name: 'Daily Pass',
                price: '$1.99/day',
                benefits: [
                    '✅ 2,000 messages today',
                    '✅ All AI features',
                    '✅ File upload (25MB)',
                    '✅ Voice messages'
                ]
            }
        };

        const plan = planData[planType];
        document.getElementById('selectedPlanName').textContent = plan.name;
        document.getElementById('selectedPlanPrice').textContent = plan.price;

        const benefitsList = document.getElementById('selectedPlanBenefits');
        benefitsList.innerHTML = plan.benefits.map(benefit => `<li>${benefit}</li>`).join('');

        // Store selected plan for processing
        this.selectedPlan = planType;
    }

    setupFormValidation() {
        // Card number formatting
        const cardNumberInput = document.getElementById('cardNumber');
        cardNumberInput.addEventListener('input', (e) => {
            let value = e.target.value.replace(/\s/g, '').replace(/[^0-9]/gi, '');
            let formattedValue = value.match(/.{1,4}/g)?.join(' ') || value;
            e.target.value = formattedValue;
        });

        // Expiry date formatting
        const expiryInput = document.getElementById('expiryDate');
        expiryInput.addEventListener('input', (e) => {
            let value = e.target.value.replace(/\D/g, '');
            if (value.length >= 2) {
                value = value.substring(0, 2) + '/' + value.substring(2, 4);
            }
            e.target.value = value;
        });

        // CVV validation
        const cvvInput = document.getElementById('cvv');
        cvvInput.addEventListener('input', (e) => {
            e.target.value = e.target.value.replace(/[^0-9]/g, '');
        });
    }

    processPayment() {
        const form = document.getElementById('paymentForm');
        const formData = new FormData(form);

        // Validate form
        if (!form.checkValidity()) {
            this.showToast('Fadlan buuxi dhammaan goobaha loo baahan yahay', 'error');
            return;
        }

        // Show processing state
        const submitBtn = document.getElementById('completePaymentBtn');
        const originalText = submitBtn.innerHTML;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Processing...';
        submitBtn.disabled = true;

        // Simulate payment processing
        setTimeout(() => {
            this.completePayment();

            // Reset button
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
        }, 3000);
    }

    completePayment() {
        // Process the upgrade
        this.processPlanUpgrade(this.selectedPlan);

        // Close payment form
        this.closePaymentFormModal();

        // Show success message
        this.showToast('🎉 Payment successful! Plan waa la activate gareeyay!', 'success');

        console.log('💳 Payment completed for plan:', this.selectedPlan);
    }

    showComingSoon(featureName, description) {
        const modal = document.createElement('div');
        modal.className = 'modal-overlay';
        modal.style.display = 'flex';
        modal.innerHTML = `
            <div class="modal-content coming-soon-modal">
                <div class="modal-header">
                    <h2>🚀 ${featureName} - Coming Soon!</h2>
                    <button class="modal-close" onclick="this.closest('.modal-overlay').remove()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="coming-soon-content">
                        <div class="coming-soon-icon">
                            <i class="fas fa-rocket"></i>
                        </div>
                        <h3>${featureName} Feature</h3>
                        <p>${description}</p>
                        <div class="feature-highlights">
                            <div class="highlight-item">
                                <i class="fas fa-check"></i>
                                <span>Professional design</span>
                            </div>
                            <div class="highlight-item">
                                <i class="fas fa-check"></i>
                                <span>Easy to use interface</span>
                            </div>
                            <div class="highlight-item">
                                <i class="fas fa-check"></i>
                                <span>Advanced functionality</span>
                            </div>
                        </div>
                        <div class="coming-soon-cta">
                            <p>Soo socda updates-ka cusub!</p>
                            <button class="btn-primary" onclick="this.closest('.modal-overlay').remove()">
                                <i class="fas fa-bell"></i>
                                Okay, Got it!
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(modal);

        // Auto remove after 5 seconds
        setTimeout(() => {
            if (modal.parentNode) {
                modal.remove();
            }
        }, 5000);
    }

    showHelpModal() {
        document.getElementById('helpModal').style.display = 'flex';
        this.showToast('Help & Support opened', 'info');
    }

    showCustomizeModal() {
        document.getElementById('customizeModal').style.display = 'flex';
        this.loadUserSettings();
        this.showToast('Customize settings opened', 'info');
    }

    handleAvatarUpload(event) {
        const file = event.target.files[0];
        if (!file) return;

        // Validate file type
        if (!file.type.startsWith('image/')) {
            this.showToast('Fadlan dooro sawir kaliya!', 'error');
            return;
        }

        // Validate file size (max 5MB)
        if (file.size > 5 * 1024 * 1024) {
            this.showToast('Sawirku waa in uu ka yar yahay 5MB!', 'error');
            return;
        }

        // Create file reader
        const reader = new FileReader();
        reader.onload = (e) => {
            const imageUrl = e.target.result;

            // Update avatar in UI
            document.getElementById('currentAvatar').src = imageUrl;

            // Update user avatar in sidebar if exists
            const userAvatar = document.querySelector('.user-avatar img');
            if (userAvatar) {
                userAvatar.src = imageUrl;
            }

            // Save to localStorage
            localStorage.setItem('tincadaAI_userAvatar', imageUrl);

            this.showToast('Profile picture waa la update gareeyay!', 'success');
        };

        reader.readAsDataURL(file);
    }

    loadUserSettings() {
        // Load saved avatar
        const savedAvatar = localStorage.getItem('tincadaAI_userAvatar');
        if (savedAvatar) {
            document.getElementById('currentAvatar').src = savedAvatar;
        }

        // Load theme preference
        const savedTheme = localStorage.getItem('tincadaAI_theme') || 'dark';
        document.querySelectorAll('.theme-item').forEach(item => {
            item.classList.remove('active');
            if (item.dataset.theme === savedTheme) {
                item.classList.add('active');
            }
        });

        // Load preferences
        const soundEffects = localStorage.getItem('tincadaAI_soundEffects') !== 'false';
        const autoSave = localStorage.getItem('tincadaAI_autoSave') !== 'false';
        const typingIndicators = localStorage.getItem('tincadaAI_typingIndicators') !== 'false';

        document.getElementById('soundEffects').checked = soundEffects;
        document.getElementById('autoSave').checked = autoSave;
        document.getElementById('typingIndicators').checked = typingIndicators;
    }

    showAIToolsModal() {
        document.getElementById('aiToolsModal').style.display = 'flex';
        this.showToast('AI Tools opened', 'info');
    }

    executeAITool(toolType, toolData) {
        // Show loading state
        this.showLoading(true);
        this.showTypingIndicator();

        // Make API call to simple_api.php
        fetch(`simple_api.php/tools/${toolType}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                ...toolData,
                user_id: this.getUserId()
            })
        })
        .then(response => response.json())
        .then(data => {
            this.hideTypingIndicator();
            this.showLoading(false);

            if (data.error) {
                this.showToast(data.error, 'error');
                this.addMessage('ai', `❌ Error: ${data.error}`);
            } else {
                this.addMessage('ai', data.response);
                this.showToast(`${this.getToolName(toolType)} completed!`, 'success');
            }
        })
        .catch(error => {
            this.hideTypingIndicator();
            this.showLoading(false);
            console.error('Tool execution error:', error);
            this.showToast('Tool execution failed. Please try again.', 'error');

            // Fallback to local response
            let response = this.generateToolResponse(toolType, toolData);
            this.addMessage('ai', response);
        });
    }

    getToolName(toolType) {
        const names = {
            'think-longer': 'Think Longer',
            'deep-research': 'Deep Research',
            'create-image': 'Create Image',
            'web-search': 'Web Search',
            'canvas': 'Canvas'
        };
        return names[toolType] || 'AI Tool';
    }

    generateToolResponse(toolType, toolData) {
        const responses = {
            'think-longer': `
# 🧠 Deep Analysis Results

## Problem Analysis
${toolData.prompt}

## Multi-Perspective Thinking

### Perspective 1: Analytical Approach
Let me break this down systematically:
- **Core Issue**: ${toolData.prompt.substring(0, 100)}...
- **Key Factors**: Multiple variables need consideration
- **Complexity Level**: ${toolData.depth}

### Perspective 2: Creative Solutions
Alternative approaches to consider:
- Innovative methodologies
- Out-of-the-box thinking
- Cross-disciplinary insights

### Perspective 3: Practical Implementation
Real-world considerations:
- Feasibility assessment
- Resource requirements
- Timeline considerations

## Comprehensive Solution Framework

\`\`\`
Step 1: Problem Definition
Step 2: Data Gathering
Step 3: Analysis & Synthesis
Step 4: Solution Development
Step 5: Implementation Planning
\`\`\`

## Conclusion
Based on ${toolData.depth} analysis, here are the key recommendations...

*This analysis was generated using Think Longer tool for extended reasoning.*
            `,
            'deep-research': `
# 📚 Deep Research Report

## Research Topic: ${toolData.topic}

### Executive Summary
Comprehensive research conducted on "${toolData.topic}" with ${toolData.scope} scope using ${toolData.sources} sources.

### Key Findings

#### 1. Current State Analysis
- **Market Overview**: Latest trends and developments
- **Key Players**: Major stakeholders and influencers
- **Recent Developments**: Updates from the past 6 months

#### 2. Historical Context
- **Evolution**: How this topic has developed over time
- **Milestones**: Key events and breakthroughs
- **Lessons Learned**: Insights from past experiences

#### 3. Future Outlook
- **Emerging Trends**: What's coming next
- **Predictions**: Expert forecasts and projections
- **Opportunities**: Areas for growth and development

### Sources & Citations
1. Academic Research Papers (15 sources)
2. Industry Reports (8 sources)
3. News Articles (12 sources)
4. Expert Interviews (5 sources)

### Methodology
Research conducted using ${toolData.scope} methodology with focus on ${toolData.sources}.

*This research was compiled using Deep Research tool with comprehensive source analysis.*
            `,
            'create-image': `
# 🎨 Image Generated Successfully!

## Your AI-Generated Image

**Prompt**: "${toolData.prompt}"
**Style**: ${toolData.style}
**Size**: ${toolData.size}

---

### 🖼️ Generated Image Preview

<div style="background: linear-gradient(45deg, #667eea, #764ba2); padding: 2rem; border-radius: 12px; text-align: center; margin: 1rem 0;">
    <div style="background: rgba(255,255,255,0.1); border-radius: 8px; padding: 3rem; border: 2px dashed rgba(255,255,255,0.3);">
        <div style="font-size: 4rem; margin-bottom: 1rem;">🎨</div>
        <h3 style="color: white; margin-bottom: 0.5rem;">AI-Generated Image</h3>
        <p style="color: rgba(255,255,255,0.8); margin: 0;">${toolData.prompt.substring(0, 50)}...</p>
        <div style="margin-top: 1rem;">
            <button style="background: #10a37f; color: white; border: none; padding: 8px 16px; border-radius: 6px; margin: 0 5px; cursor: pointer;">📥 Download</button>
            <button style="background: #4ecdc4; color: white; border: none; padding: 8px 16px; border-radius: 6px; margin: 0 5px; cursor: pointer;">🔄 Regenerate</button>
            <button style="background: #ff6b6b; color: white; border: none; padding: 8px 16px; border-radius: 6px; margin: 0 5px; cursor: pointer;">✏️ Edit</button>
        </div>
    </div>
</div>

### 📊 Image Details
- **Resolution**: High-quality ${toolData.size}
- **Art Style**: ${toolData.style}
- **Format**: PNG with transparency
- **Color Profile**: sRGB
- **File Size**: ~2.5MB

### 🎯 Quick Actions
\`\`\`
✅ Image generated successfully
📱 Mobile-optimized version created
🌐 Web-ready format available
💾 Auto-saved to your gallery
\`\`\`

### 💡 Pro Tips
- **Refine your prompt** for better results
- **Try different styles** for variety
- **Use specific details** for accuracy
- **Experiment with sizes** for different uses

*Generated using advanced AI image synthesis technology*
            `,
            'web-search': `
# 🌐 Web Search Results

## Search Query: "${toolData.query}"

### Search Parameters
- **Type**: ${toolData.type}
- **Region**: ${toolData.region}
- **Results Found**: 1,247 relevant results

### Top Results

#### 1. Primary Source
**Title**: Latest information on ${toolData.query}
**URL**: https://example.com/result1
**Summary**: Comprehensive overview with current data and analysis...
**Relevance**: 98%

#### 2. News Article
**Title**: Recent developments in ${toolData.query}
**URL**: https://news.example.com/article
**Published**: 2 hours ago
**Summary**: Breaking news and latest updates...
**Relevance**: 95%

#### 3. Academic Source
**Title**: Research paper on ${toolData.query}
**URL**: https://academic.example.com/paper
**Authors**: Dr. Smith, Prof. Johnson
**Summary**: Peer-reviewed research with statistical analysis...
**Relevance**: 92%

### Related Searches
- ${toolData.query} trends 2024
- ${toolData.query} statistics
- ${toolData.query} best practices
- ${toolData.query} case studies

### Search Insights
- **Trending**: High search volume in past 24 hours
- **Sentiment**: Generally positive coverage
- **Authority**: Multiple credible sources available

*This search was performed using Web Search tool with real-time data.*
            `,
            'canvas': `
# 🎨 Canvas Created Successfully

## Canvas Details

**Type**: ${toolData.type}
**Title**: ${toolData.title}
**Description**: ${toolData.description}

### Canvas Features
- ✅ **Real-time Collaboration**: Multiple users can edit simultaneously
- ✅ **Version History**: Track all changes and revisions
- ✅ **Export Options**: PDF, PNG, SVG formats available
- ✅ **Template Library**: Pre-built templates for quick start

### Getting Started
1. **Open Canvas**: Click the link below to access your workspace
2. **Invite Collaborators**: Share with team members
3. **Start Creating**: Use the toolbar to add content
4. **Save & Export**: Your work is automatically saved

### Canvas Link
🔗 **[Open ${toolData.title} Canvas](javascript:void(0))**

### Collaboration Tools
- **Comments**: Add feedback and suggestions
- **Chat**: Real-time communication
- **Permissions**: Control who can view/edit
- **Notifications**: Stay updated on changes

### Next Steps
Your ${toolData.type} canvas is ready! You can now:
- Add content and media
- Invite team members
- Customize the layout
- Export your work

*This canvas was created using the Canvas tool for interactive collaboration.*
            `
        };

        return responses[toolType] || `Tool "${toolType}" executed successfully with the provided parameters.`;
    }

    loadChatHistory() {
        // Load saved chats from localStorage
        const savedChats = localStorage.getItem('tincadaAI_chats');
        if (savedChats) {
            this.chats = JSON.parse(savedChats);
            this.renderChatHistory();
        }
    }

    saveChatData() {
        localStorage.setItem('tincadaAI_chats', JSON.stringify(this.chats));
    }

    showLoading(show) {
        const overlay = document.getElementById('loadingOverlay');
        overlay.style.display = show ? 'flex' : 'none';
    }

    showTypingIndicator() {
        const indicator = document.getElementById('typingIndicator');
        indicator.style.display = 'block';

        // Scroll to bottom to show typing indicator
        const chatMessages = document.getElementById('chatMessages');
        setTimeout(() => {
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }, 100);
    }

    hideTypingIndicator() {
        const indicator = document.getElementById('typingIndicator');
        indicator.style.display = 'none';
    }

    showToast(message, type = 'success') {
        const container = document.getElementById('toastContainer');
        const toast = document.createElement('div');
        toast.className = `toast ${type}`;
        toast.innerHTML = `
            <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : type === 'warning' ? 'exclamation-triangle' : 'info-circle'}"></i>
            <span>${message}</span>
        `;
        
        container.appendChild(toast);
        
        setTimeout(() => {
            toast.remove();
        }, 5000);
    }

    logout() {
        if (confirm('Ma hubtaa inaad rabto inaad ka baxdo?')) {
            authManager.logout();
            
            document.body.style.opacity = '0';
            document.body.style.transform = 'scale(0.9)';
            
            setTimeout(() => {
                window.location.href = 'login.html';
            }, 500);
        }
    }
}

// Global variables for chat management
let currentContextChatId = null;

// Help modal functions
function closeHelpModal() {
    document.getElementById('helpModal').style.display = 'none';
}

function showHelpTab(tabName) {
    // Remove active class from all tabs and sections
    document.querySelectorAll('.help-tab').forEach(tab => tab.classList.remove('active'));
    document.querySelectorAll('.help-section').forEach(section => section.classList.remove('active'));

    // Add active class to clicked tab and corresponding section
    event.target.classList.add('active');
    document.getElementById(tabName).classList.add('active');
}

// Customize modal functions
function closeCustomizeModal() {
    document.getElementById('customizeModal').style.display = 'none';
}

function removeAvatar() {
    // Reset to default avatar
    const defaultAvatar = 'https://via.placeholder.com/80x80/10a37f/ffffff?text=U';
    document.getElementById('currentAvatar').src = defaultAvatar;

    // Update user avatar in sidebar if exists
    const userAvatar = document.querySelector('.user-avatar img');
    if (userAvatar) {
        userAvatar.src = defaultAvatar;
    }

    // Remove from localStorage
    localStorage.removeItem('tincadaAI_userAvatar');

    if (window.dashboard) {
        window.dashboard.showToast('Profile picture waa la reset gareeyay!', 'success');
    }
}

function saveSettings() {
    // Save theme
    const activeTheme = document.querySelector('.theme-item.active');
    if (activeTheme) {
        localStorage.setItem('tincadaAI_theme', activeTheme.dataset.theme);
    }

    // Save preferences
    localStorage.setItem('tincadaAI_soundEffects', document.getElementById('soundEffects').checked);
    localStorage.setItem('tincadaAI_autoSave', document.getElementById('autoSave').checked);
    localStorage.setItem('tincadaAI_typingIndicators', document.getElementById('typingIndicators').checked);

    closeCustomizeModal();

    if (window.dashboard) {
        window.dashboard.showToast('Settings waa la kaydiay!', 'success');
    }
}

function resetSettings() {
    if (confirm('Ma hubtaa inaad reset gareyso dhammaan settings-yada?')) {
        // Clear all settings
        localStorage.removeItem('tincadaAI_theme');
        localStorage.removeItem('tincadaAI_soundEffects');
        localStorage.removeItem('tincadaAI_autoSave');
        localStorage.removeItem('tincadaAI_typingIndicators');
        localStorage.removeItem('tincadaAI_userAvatar');

        // Reload settings
        if (window.dashboard) {
            window.dashboard.loadUserSettings();
            window.dashboard.showToast('Settings waa la reset gareeyay!', 'success');
        }
    }
}

// Theme selection
document.addEventListener('click', (e) => {
    if (e.target.closest('.theme-item')) {
        const themeItem = e.target.closest('.theme-item');

        // Remove active class from all theme items
        document.querySelectorAll('.theme-item').forEach(item => {
            item.classList.remove('active');
        });

        // Add active class to clicked item
        themeItem.classList.add('active');
    }
});

// Avatar click handler
document.addEventListener('click', (e) => {
    if (e.target.closest('.current-avatar')) {
        document.getElementById('avatarUpload').click();
    }
});

// AI Tools functions
function closeAIToolsModal() {
    document.getElementById('aiToolsModal').style.display = 'none';
}

function closeToolUsageModal() {
    document.getElementById('toolUsageModal').style.display = 'none';
}

function useTool(toolType) {
    closeAIToolsModal();
    showToolUsageModal(toolType);
}

function showToolUsageModal(toolType) {
    const modal = document.getElementById('toolUsageModal');
    const title = document.getElementById('toolUsageTitle');
    const content = document.getElementById('toolUsageContent');
    const executeBtn = document.getElementById('executeToolBtn');

    // Configure modal based on tool type
    const toolConfigs = {
        'think-longer': {
            title: '💡 Think Longer',
            content: `
                <div class="tool-form">
                    <div class="tool-form-group">
                        <label for="thinkPrompt">Complex Problem or Question:</label>
                        <textarea id="thinkPrompt" placeholder="Describe the complex problem you want me to analyze deeply..."></textarea>
                    </div>
                    <div class="tool-form-group">
                        <label for="thinkDepth">Analysis Depth:</label>
                        <select id="thinkDepth">
                            <option value="standard">Standard Analysis</option>
                            <option value="deep">Deep Analysis</option>
                            <option value="comprehensive">Comprehensive Analysis</option>
                        </select>
                    </div>
                    <div class="tool-preview">
                        <h4>What this tool does:</h4>
                        <p>Provides extended reasoning with step-by-step analysis, multiple perspectives, and comprehensive solutions for complex problems.</p>
                    </div>
                </div>
            `,
            executeText: '➡️ Next'
        },
        'deep-research': {
            title: '🔍 Deep Research',
            content: `
                <div class="tool-form">
                    <div class="tool-form-group">
                        <label for="researchTopic">Research Topic:</label>
                        <input type="text" id="researchTopic" placeholder="Enter the topic you want to research...">
                    </div>
                    <div class="tool-form-group">
                        <label for="researchScope">Research Scope:</label>
                        <select id="researchScope">
                            <option value="overview">General Overview</option>
                            <option value="detailed">Detailed Analysis</option>
                            <option value="academic">Academic Research</option>
                            <option value="current">Current Trends</option>
                        </select>
                    </div>
                    <div class="tool-form-group">
                        <label for="researchSources">Preferred Sources:</label>
                        <select id="researchSources">
                            <option value="all">All Sources</option>
                            <option value="academic">Academic Papers</option>
                            <option value="news">News Articles</option>
                            <option value="reports">Industry Reports</option>
                        </select>
                    </div>
                    <div class="tool-preview">
                        <h4>What this tool does:</h4>
                        <p>Conducts comprehensive research with multiple sources, citations, and detailed analysis on any topic.</p>
                    </div>
                </div>
            `,
            executeText: '➡️ Next'
        },
        'create-image': {
            title: '🎨 Create Image',
            content: `
                <div class="tool-form">
                    <div class="tool-form-group">
                        <label for="imagePrompt">Image Description:</label>
                        <textarea id="imagePrompt" placeholder="Describe the image you want to create in detail..."></textarea>
                    </div>
                    <div class="tool-form-group">
                        <label for="imageStyle">Art Style:</label>
                        <select id="imageStyle">
                            <option value="realistic">Realistic</option>
                            <option value="artistic">Artistic</option>
                            <option value="cartoon">Cartoon</option>
                            <option value="abstract">Abstract</option>
                            <option value="digital-art">Digital Art</option>
                        </select>
                    </div>
                    <div class="tool-form-group">
                        <label for="imageSize">Image Size:</label>
                        <select id="imageSize">
                            <option value="square">Square (1024x1024)</option>
                            <option value="landscape">Landscape (1792x1024)</option>
                            <option value="portrait">Portrait (1024x1792)</option>
                        </select>
                    </div>
                    <div class="tool-preview">
                        <h4>What this tool does:</h4>
                        <p>Generates high-quality images from text descriptions using advanced AI image generation technology.</p>
                    </div>
                </div>
            `,
            executeText: '➡️ Next'
        },
        'web-search': {
            title: '🌐 Web Search',
            content: `
                <div class="tool-form">
                    <div class="tool-form-group">
                        <label for="searchQuery">Search Query:</label>
                        <input type="text" id="searchQuery" placeholder="Enter your search query...">
                    </div>
                    <div class="tool-form-group">
                        <label for="searchType">Search Type:</label>
                        <select id="searchType">
                            <option value="general">General Search</option>
                            <option value="news">News</option>
                            <option value="academic">Academic</option>
                            <option value="images">Images</option>
                            <option value="videos">Videos</option>
                        </select>
                    </div>
                    <div class="tool-form-group">
                        <label for="searchRegion">Region:</label>
                        <select id="searchRegion">
                            <option value="global">Global</option>
                            <option value="us">United States</option>
                            <option value="uk">United Kingdom</option>
                            <option value="so">Somalia</option>
                            <option value="ke">Kenya</option>
                        </select>
                    </div>
                    <div class="tool-preview">
                        <h4>What this tool does:</h4>
                        <p>Searches the web in real-time to provide current information, news, and resources on any topic.</p>
                    </div>
                </div>
            `,
            executeText: '➡️ Next'
        },
        'canvas': {
            title: '✏️ Canvas',
            content: `
                <div class="tool-form">
                    <div class="tool-form-group">
                        <label for="canvasType">Canvas Type:</label>
                        <select id="canvasType">
                            <option value="document">Document Editor</option>
                            <option value="diagram">Diagram Creator</option>
                            <option value="presentation">Presentation</option>
                            <option value="code">Code Editor</option>
                            <option value="whiteboard">Whiteboard</option>
                        </select>
                    </div>
                    <div class="tool-form-group">
                        <label for="canvasTitle">Canvas Title:</label>
                        <input type="text" id="canvasTitle" placeholder="Enter a title for your canvas...">
                    </div>
                    <div class="tool-form-group">
                        <label for="canvasDescription">Initial Content:</label>
                        <textarea id="canvasDescription" placeholder="Describe what you want to create or start with..."></textarea>
                    </div>
                    <div class="tool-preview">
                        <h4>What this tool does:</h4>
                        <p>Opens an interactive workspace for visual collaboration, document editing, and creative projects.</p>
                    </div>
                </div>
            `,
            executeText: '➡️ Next'
        }
    };

    const config = toolConfigs[toolType];
    if (config) {
        title.innerHTML = config.title;
        content.innerHTML = config.content;
        executeBtn.innerHTML = config.executeText;
        executeBtn.setAttribute('data-tool-type', toolType);

        modal.style.display = 'flex';
    }
}

function executeToolAction() {
    const executeBtn = document.getElementById('executeToolBtn');
    const toolType = executeBtn.getAttribute('data-tool-type');

    // Get form data based on tool type
    let toolData = {};

    switch (toolType) {
        case 'think-longer':
            toolData = {
                prompt: document.getElementById('thinkPrompt').value,
                depth: document.getElementById('thinkDepth').value
            };
            break;
        case 'deep-research':
            toolData = {
                topic: document.getElementById('researchTopic').value,
                scope: document.getElementById('researchScope').value,
                sources: document.getElementById('researchSources').value
            };
            break;
        case 'create-image':
            toolData = {
                prompt: document.getElementById('imagePrompt').value,
                style: document.getElementById('imageStyle').value,
                size: document.getElementById('imageSize').value
            };
            break;
        case 'web-search':
            toolData = {
                query: document.getElementById('searchQuery').value,
                type: document.getElementById('searchType').value,
                region: document.getElementById('searchRegion').value
            };
            break;
        case 'canvas':
            toolData = {
                type: document.getElementById('canvasType').value,
                title: document.getElementById('canvasTitle').value,
                description: document.getElementById('canvasDescription').value
            };
            break;
    }

    // Validate required fields
    const requiredFields = Object.values(toolData).filter(value => value && value.trim());
    if (requiredFields.length === 0) {
        if (window.dashboard) {
            window.dashboard.showToast('Fadlan buuxi goobaha loo baahan yahay!', 'error');
        }
        return;
    }

    // Close modal and execute tool
    closeToolUsageModal();

    // Execute the tool
    if (window.dashboard) {
        window.dashboard.executeAITool(toolType, toolData);
    }
}

// Chat management functions
function showChatMenu(event, chatId) {
    event.stopPropagation();
    currentContextChatId = chatId;

    const contextMenu = document.getElementById('chatContextMenu');
    contextMenu.style.display = 'block';
    contextMenu.style.left = event.pageX + 'px';
    contextMenu.style.top = event.pageY + 'px';

    // Hide menu when clicking elsewhere
    document.addEventListener('click', hideChatMenu);
}

function hideChatMenu() {
    const contextMenu = document.getElementById('chatContextMenu');
    contextMenu.style.display = 'none';
    document.removeEventListener('click', hideChatMenu);
}

function renameChat() {
    hideChatMenu();
    const modal = document.getElementById('renameChatModal');
    const input = document.getElementById('newChatName');

    // Get current chat name
    const chatItem = document.querySelector(`[data-chat-id="${currentContextChatId}"]`);
    const currentName = chatItem.querySelector('.chat-item-text').textContent;
    input.value = currentName;

    modal.style.display = 'flex';
    input.focus();
    input.select();
}

function closeRenameModal() {
    document.getElementById('renameChatModal').style.display = 'none';
}

function confirmRename() {
    const newName = document.getElementById('newChatName').value.trim();
    if (!newName) return;

    // Update chat name in UI
    const chatItem = document.querySelector(`[data-chat-id="${currentContextChatId}"]`);
    chatItem.querySelector('.chat-item-text').textContent = newName;

    // Update in storage (if implemented)
    // dashboard.updateChatName(currentContextChatId, newName);

    closeRenameModal();

    // Show success toast
    if (window.dashboard) {
        window.dashboard.showToast('Magaca chat-ka waa la beddelay!', 'success');
    }
}

function exportChat() {
    hideChatMenu();

    // Get chat data
    const chatData = {
        id: currentContextChatId,
        name: document.querySelector(`[data-chat-id="${currentContextChatId}"] .chat-item-text`).textContent,
        messages: [], // Get from storage
        exportDate: new Date().toISOString()
    };

    // Create and download file
    const blob = new Blob([JSON.stringify(chatData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `tincada-chat-${chatData.name}-${Date.now()}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    if (window.dashboard) {
        window.dashboard.showToast('Chat waa la export gareeyay!', 'success');
    }
}

function duplicateChat() {
    hideChatMenu();

    const originalItem = document.querySelector(`[data-chat-id="${currentContextChatId}"]`);
    const originalName = originalItem.querySelector('.chat-item-text').textContent;
    const newName = `${originalName} (Copy)`;
    const newChatId = 'chat_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);

    // Create duplicate chat
    if (window.dashboard) {
        window.dashboard.addChatToList(newChatId, newName);
        window.dashboard.showToast('Chat waa la nuqul gareeyay!', 'success');
    }
}

function deleteChat() {
    hideChatMenu();

    if (confirm('Ma hubtaa inaad tirtirto chat-kan?')) {
        const chatItem = document.querySelector(`[data-chat-id="${currentContextChatId}"]`);
        chatItem.remove();

        // Remove from storage (if implemented)
        // dashboard.deleteChat(currentContextChatId);

        if (window.dashboard) {
            window.dashboard.showToast('Chat waa la tirtiray!', 'success');
        }
    }
}

// Global functions for code blocks
function copyCode(codeId) {
    const codeElement = document.getElementById(codeId);
    const code = codeElement.textContent;

    navigator.clipboard.writeText(code).then(() => {
        // Show success feedback
        const copyBtn = codeElement.parentElement.querySelector('.copy-btn');
        const originalText = copyBtn.innerHTML;
        copyBtn.innerHTML = '<i class="fas fa-check"></i> Copied!';
        copyBtn.style.background = '#10a37f';

        setTimeout(() => {
            copyBtn.innerHTML = originalText;
            copyBtn.style.background = '';
        }, 2000);
    }).catch(err => {
        console.error('Failed to copy code:', err);
    });
}

function editCode(codeId) {
    console.log('🔧 Opening edit modal for code:', codeId);

    const codeElement = document.getElementById(codeId);
    if (!codeElement) {
        console.error('❌ Code element not found:', codeId);
        return;
    }

    // Get the code content
    const codeContent = codeElement.textContent || codeElement.innerText;
    const language = codeElement.querySelector('code')?.className.match(/language-(\w+)/)?.[1] || 'text';

    // Set up modal
    const modal = document.getElementById('codeEditModal');
    const textarea = document.getElementById('codeEditTextarea');
    const preview = document.getElementById('originalCodePreview');
    const title = document.getElementById('editModalTitle');

    if (modal && textarea && preview && title) {
        // Set modal content
        title.textContent = `Edit ${language.toUpperCase()} Code`;
        preview.textContent = codeContent;
        textarea.value = codeContent;

        // Store current code ID for saving
        modal.setAttribute('data-code-id', codeId);

        // Show modal
        modal.classList.add('active');

        // Focus textarea
        setTimeout(() => {
            textarea.focus();
            textarea.setSelectionRange(0, 0);
        }, 100);

        console.log('✅ Edit modal opened successfully');
    } else {
        console.error('❌ Modal elements not found');
    }
}

function closeCodeEditModal() {
    const modal = document.getElementById('codeEditModal');
    if (modal) {
        modal.classList.remove('active');
        modal.removeAttribute('data-code-id');
    }
}

function saveEditedCode() {
    console.log('💾 Saving edited code...');

    const modal = document.getElementById('codeEditModal');
    const textarea = document.getElementById('codeEditTextarea');

    if (!modal || !textarea) {
        console.error('❌ Modal or textarea not found');
        return;
    }

    const codeId = modal.getAttribute('data-code-id');
    const newCode = textarea.value;

    if (!codeId) {
        console.error('❌ No code ID found');
        return;
    }

    const codeElement = document.getElementById(codeId);
    if (!codeElement) {
        console.error('❌ Original code element not found:', codeId);
        return;
    }

    // Update the code content
    const codeTag = codeElement.querySelector('code');
    if (codeTag) {
        codeTag.textContent = newCode;

        // Re-apply syntax highlighting
        if (window.hljs) {
            hljs.highlightElement(codeTag);
        }
    } else {
        codeElement.textContent = newCode;
    }

    // Close modal
    closeCodeEditModal();

    console.log('✅ Code updated successfully');

    // Show success message if available
    if (window.dashboard && window.dashboard.showToast) {
        window.dashboard.showToast('Code updated successfully!', 'success');
    }
}

function formatCode() {
    const textarea = document.getElementById('codeEditTextarea');
    if (!textarea) return;

    let code = textarea.value;

    // Simple code formatting (basic indentation)
    const lines = code.split('\n');
    let indentLevel = 0;
    const indentSize = 2;

    const formattedLines = lines.map(line => {
        const trimmed = line.trim();

        if (trimmed.includes('}') || trimmed.includes('</')) {
            indentLevel = Math.max(0, indentLevel - 1);
        }

        const formatted = ' '.repeat(indentLevel * indentSize) + trimmed;

        if (trimmed.includes('{') || (trimmed.includes('<') && !trimmed.includes('</'))) {
            indentLevel++;
        }

        return formatted;
    });

    textarea.value = formattedLines.join('\n');
    console.log('✨ Code formatted');
}

function resetCode() {
    const textarea = document.getElementById('codeEditTextarea');
    const preview = document.getElementById('originalCodePreview');

    if (textarea && preview) {
        textarea.value = preview.textContent;
        console.log('🔄 Code reset to original');
    }
}

// Initialize dashboard when page loads
document.addEventListener('DOMContentLoaded', () => {
    window.dashboard = new TincadaDashboard();
});
