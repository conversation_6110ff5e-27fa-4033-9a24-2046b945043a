#!/usr/bin/env python3
"""
🌍 Test Multilingual AI Capabilities
Testing all world languages with API
"""

import requests
import json

def test_multilingual_responses():
    """
    Test AI responses in multiple languages
    """
    print("🌍 TESTING MULTILINGUAL AI CAPABILITIES")
    print("=" * 60)
    print("Testing AI understanding of ALL world languages using OpenAI API")
    print("=" * 60)
    
    # Test questions in different languages
    test_cases = [
        {
            "language": "Somali (Default)",
            "question": "Maxaad tahay? Ku sharax naftaada.",
            "expected": "Somali response"
        },
        {
            "language": "English", 
            "question": "What is the capital of France?",
            "expected": "English response"
        },
        {
            "language": "Arabic",
            "question": "ما هي عاصمة مصر؟",
            "expected": "Arabic response"
        },
        {
            "language": "French",
            "question": "Quelle est la capitale de l'Allemagne?",
            "expected": "French response"
        },
        {
            "language": "Spanish",
            "question": "¿Cuál es la capital de España?",
            "expected": "Spanish response"
        },
        {
            "language": "Swahili",
            "question": "<PERSON>ji mkuu wa <PERSON> ni upi?",
            "expected": "Swahili response"
        },
        {
            "language": "Amharic",
            "question": "የኢትዮጵያ ዋና ከተማ ምንድን ነው?",
            "expected": "Amharic response"
        },
        {
            "language": "Code Request (Somali)",
            "question": "Ku qor HTML code button ah oo cagaar ah",
            "expected": "HTML code in cards"
        }
    ]
    
    results = []
    
    for i, test in enumerate(test_cases, 1):
        print(f"\n{i}. Testing {test['language']}:")
        print(f"   Question: {test['question']}")
        
        try:
            response = requests.post(
                "http://localhost:5001/api/chat",
                json={
                    "message": test['question'],
                    "user_id": "multilingual_test"
                },
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                ai_response = data['response']
                
                print(f"   ✅ Response: {ai_response[:100]}...")
                
                # Check if response is in expected language context
                success = True
                if test['language'] == "Code Request (Somali)" and "```html" not in ai_response:
                    success = False
                    print("   ⚠️  Warning: Code not in proper format")
                
                results.append({
                    "language": test['language'],
                    "success": success,
                    "response_length": len(ai_response)
                })
                
            else:
                print(f"   ❌ HTTP Error: {response.status_code}")
                results.append({
                    "language": test['language'],
                    "success": False,
                    "error": f"HTTP {response.status_code}"
                })
                
        except Exception as e:
            print(f"   ❌ Error: {e}")
            results.append({
                "language": test['language'],
                "success": False,
                "error": str(e)
            })
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 MULTILINGUAL TEST RESULTS:")
    print("-" * 40)
    
    successful = 0
    total = len(results)
    
    for result in results:
        status = "✅ SUCCESS" if result['success'] else "❌ FAILED"
        print(f"{result['language']}: {status}")
        if result['success']:
            successful += 1
    
    print(f"\n📈 Success Rate: {successful}/{total} ({(successful/total)*100:.1f}%)")
    
    if successful >= 6:  # Most languages working
        print("\n🎉 EXCELLENT! AI understands multiple languages!")
        print("✨ Using OpenAI API for accurate multilingual responses")
        print("🌍 Supports: Somali, English, Arabic, French, Spanish, Swahili, Amharic, and more")
        print("🔧 Code formatting working with language detection")
    elif successful >= 4:
        print("\n👍 GOOD! Most languages working")
        print("🔧 Some minor issues to address")
    else:
        print("\n⚠️ NEEDS IMPROVEMENT")
        print("🔧 Check API configuration and language detection")

def test_language_switching():
    """
    Test language switching in conversation
    """
    print("\n🔄 TESTING LANGUAGE SWITCHING")
    print("=" * 40)
    
    conversation = [
        ("Somali", "Maxaad tahay?"),
        ("English", "Now answer in English: What can you do?"),
        ("Arabic", "الآن أجب بالعربية: ما هي خدماتك؟"),
        ("Back to Somali", "Hadda ku jawaab Somali: Maxaad i caawin kartaa?")
    ]
    
    for lang, question in conversation:
        print(f"\n🗣️ {lang}: {question}")
        
        try:
            response = requests.post(
                "http://localhost:5001/api/chat",
                json={"message": question, "user_id": "switch_test"},
                timeout=25
            )
            
            if response.status_code == 200:
                data = response.json()
                print(f"🤖 Response: {data['response'][:80]}...")
            else:
                print(f"❌ Error: {response.status_code}")
                
        except Exception as e:
            print(f"❌ Error: {e}")

def main():
    """
    Run all multilingual tests
    """
    print("🌍 COMPREHENSIVE MULTILINGUAL AI TEST")
    print("=" * 70)
    print("Testing Tincada AI's ability to understand and respond in ALL world languages")
    print("Using OpenAI API with enhanced multilingual system prompts")
    print("=" * 70)
    
    # Test 1: Multiple languages
    test_multilingual_responses()
    
    # Test 2: Language switching
    test_language_switching()
    
    print("\n" + "=" * 70)
    print("🎯 FINAL ASSESSMENT:")
    print("✅ AI uses OpenAI API for ALL responses")
    print("🌍 AI understands and responds in multiple world languages")
    print("🔧 Code formatting works with language detection")
    print("💬 Natural conversation flow maintained")
    print("🌐 Server: http://localhost:5001")

if __name__ == "__main__":
    main()
