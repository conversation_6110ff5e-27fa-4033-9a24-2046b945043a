<!DOCTYPE html>
<html lang="so">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Social Login Test - Tincada AI</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
            padding: 2rem;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .container {
            max-width: 500px;
            width: 100%;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 3rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
            text-align: center;
        }

        h1 {
            font-size: 2.5rem;
            margin-bottom: 1rem;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .subtitle {
            margin-bottom: 3rem;
            opacity: 0.9;
        }

        .social-buttons {
            display: flex;
            flex-direction: column;
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .google-btn, .apple-btn {
            width: 100%;
            padding: 15px;
            border-radius: 12px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 12px;
            border: none;
            position: relative;
            overflow: hidden;
        }

        .google-btn {
            background: #ffffff;
            color: #333;
            border: 1px solid rgba(0, 0, 0, 0.1);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .google-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
        }

        .google-btn:disabled {
            opacity: 0.7;
            cursor: not-allowed;
            transform: none;
        }

        .apple-btn {
            background: #000000;
            color: #ffffff;
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
        }

        .apple-btn:hover {
            transform: translateY(-2px);
            background: #1a1a1a;
        }

        .apple-btn:disabled {
            opacity: 0.7;
            cursor: not-allowed;
            transform: none;
        }

        .google-icon {
            width: 20px;
            height: 20px;
            background: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTIyLjU2IDEyLjI1QzIyLjU2IDExLjQ3IDIyLjQ5IDEwLjcyIDIyLjM2IDEwSDEyVjE0LjI2SDE3LjkyQzE3LjY2IDE1LjYgMTYuOTIgMTYuNzQgMTUuODQgMTcuNVYyMC4yNkgxOS4yOEMyMS4zNiAxOC40MyAyMi41NiAxNS42IDIyLjU2IDEyLjI1WiIgZmlsbD0iIzQyODVGNCIvPgo8cGF0aCBkPSJNMTIgMjNDMTUuMjQgMjMgMTcuOTUgMjEuOTIgMTkuMjggMjAuMjZMMTUuODQgMTcuNUMxNC43OCAxOC4xMyAxMy40NyAxOC41IDEyIDE4LjVDOC44NyAxOC41IDYuMjIgMTYuNjQgNS4yNyAxMy45NEgyLjc2VjE2Ljc0QzQuMDUgMTkuMyA3Ljc5IDIzIDEyIDIzWiIgZmlsbD0iIzM0QTg1MyIvPgo8cGF0aCBkPSJNNS4yNyAxMy45NEM1LjAyIDEzLjMxIDQuODkgMTIuNjYgNC44OSAxMkM0Ljg5IDExLjM0IDUuMDIgMTAuNjkgNS4yNyAxMC4wNlY3LjI2SDIuNzZDMi4xIDguNTkgMS43NSAxMC4yNSAxLjc1IDEyQzEuNzUgMTMuNzUgMi4xIDE1LjQxIDIuNzYgMTYuNzRMNS4yNyAxMy45NFoiIGZpbGw9IiNGQkJDMDQiLz4KPHBhdGggZD0iTTEyIDUuNUMxMy42MiA1LjUgMTUuMDYgNi4wOSAxNi4yIDcuMkwxOS4xOCA0LjIyQzE3Ljk1IDMuMDkgMTUuMjQgMi4yNSAxMiAyLjI1QzcuNzkgMi4yNSA0LjA1IDUuOTUgMi43NiA4LjUxTDUuMjcgMTEuMzFDNi4yMiA4LjYxIDguODcgNi43NSAxMiA2Ljc1WiIgZmlsbD0iI0VBNDMzNSIvPgo8L3N2Zz4K') no-repeat center;
            background-size: contain;
        }

        .status-display {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 10px;
            padding: 1.5rem;
            margin-top: 2rem;
            text-align: left;
        }

        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.5rem 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .status-item:last-child {
            border-bottom: none;
        }

        .status-label {
            color: #cccccc;
        }

        .status-value {
            color: #4ecdc4;
            font-family: monospace;
        }

        .message {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 10px;
            color: white;
            font-weight: 500;
            z-index: 10000;
            max-width: 300px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
            transition: all 0.3s ease;
            display: none;
        }

        .message.success {
            background: linear-gradient(45deg, #4caf50, #45a049);
        }

        .message.error {
            background: linear-gradient(45deg, #f44336, #d32f2f);
        }

        .message.info {
            background: linear-gradient(45deg, #2196f3, #1976d2);
        }

        @media (max-width: 768px) {
            body { padding: 1rem; }
            .container { padding: 2rem 1rem; }
            h1 { font-size: 2rem; }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 Social Login Test</h1>
        <p class="subtitle">Test Google iyo Apple Sign In functionality</p>
        
        <div class="social-buttons">
            <button type="button" class="google-btn" id="googleSignIn">
                <div class="google-icon"></div>
                <span>Sign in with Google</span>
            </button>

            <button type="button" class="apple-btn" id="appleSignIn">
                <i class="fab fa-apple" style="font-size: 1.2rem;"></i>
                <span>Sign in with Apple</span>
            </button>
        </div>
        
        <div class="status-display">
            <div class="status-item">
                <span class="status-label">Google SDK:</span>
                <span class="status-value" id="googleStatus">Loading...</span>
            </div>
            <div class="status-item">
                <span class="status-label">Apple SDK:</span>
                <span class="status-value" id="appleStatus">Loading...</span>
            </div>
            <div class="status-item">
                <span class="status-label">Client ID:</span>
                <span class="status-value">************-shjvho7kcqn6vpjk1scrijfr88cg4g1n.apps.googleusercontent.com</span>
            </div>
        </div>
    </div>

    <div class="message" id="messageDisplay"></div>

    <!-- Google Sign In SDK -->
    <script src="https://accounts.google.com/gsi/client" async defer></script>
    
    <!-- Apple Sign In SDK -->
    <script type="text/javascript" src="https://appleid.cdn-apple.com/appleauth/static/jsapi/appleid/1/en_US/appleid.auth.js"></script>

    <script>
        const GOOGLE_CLIENT_ID = '************-shjvho7kcqn6vpjk1scrijfr88cg4g1n.apps.googleusercontent.com';
        
        // Message display function
        function showMessage(message, type = 'info') {
            const messageElement = document.getElementById('messageDisplay');
            messageElement.textContent = message;
            messageElement.className = `message ${type}`;
            messageElement.style.display = 'block';
            
            setTimeout(() => {
                messageElement.style.display = 'none';
            }, 4000);
        }

        // Check SDK status
        function checkSDKStatus() {
            // Check Google SDK
            if (typeof google !== 'undefined' && google.accounts) {
                document.getElementById('googleStatus').textContent = '✅ Loaded';
                document.getElementById('googleStatus').style.color = '#4caf50';
            } else {
                document.getElementById('googleStatus').textContent = '❌ Not loaded';
                document.getElementById('googleStatus').style.color = '#f44336';
            }

            // Check Apple SDK
            if (typeof AppleID !== 'undefined') {
                document.getElementById('appleStatus').textContent = '✅ Loaded';
                document.getElementById('appleStatus').style.color = '#4caf50';
            } else {
                document.getElementById('appleStatus').textContent = '❌ Not loaded';
                document.getElementById('appleStatus').style.color = '#f44336';
            }
        }

        // Initialize Google OAuth
        function initializeGoogle() {
            if (typeof google !== 'undefined' && google.accounts) {
                google.accounts.id.initialize({
                    client_id: GOOGLE_CLIENT_ID,
                    callback: handleGoogleResponse,
                    auto_select: false,
                    cancel_on_tap_outside: false
                });
                console.log('✅ Google OAuth initialized');
                showMessage('Google OAuth initialized successfully', 'success');
            } else {
                console.log('❌ Google SDK not available');
                showMessage('Google SDK not loaded', 'error');
            }
        }

        // Handle Google response
        function handleGoogleResponse(response) {
            try {
                const payload = JSON.parse(atob(response.credential.split('.')[1]));
                console.log('Google Sign In Success:', payload);
                
                showMessage(`Welcome ${payload.name}! Google Sign In successful`, 'success');
                
                // Store user info
                localStorage.setItem('tincada_user', JSON.stringify({
                    id: payload.sub,
                    name: payload.name,
                    email: payload.email,
                    picture: payload.picture,
                    provider: 'google',
                    loginTime: new Date().toISOString()
                }));
                
                // Redirect after delay
                setTimeout(() => {
                    window.location.href = 'dashboard.html';
                }, 2000);
                
            } catch (error) {
                console.error('Google Sign In Error:', error);
                showMessage('Google Sign In failed', 'error');
            }
        }

        // Initialize Apple OAuth
        function initializeApple() {
            if (typeof AppleID !== 'undefined') {
                AppleID.auth.init({
                    clientId: 'com.tincada.ai',
                    scope: 'name email',
                    redirectURI: window.location.origin + '/auth/apple/callback',
                    state: 'tincada-login-' + Date.now(),
                    usePopup: true
                });
                console.log('✅ Apple OAuth initialized');
                showMessage('Apple OAuth initialized successfully', 'success');
            } else {
                console.log('❌ Apple SDK not available');
                showMessage('Apple SDK not loaded', 'error');
            }
        }

        // Google Sign In button
        document.getElementById('googleSignIn').addEventListener('click', function() {
            const btn = this;
            btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Signing in...';
            btn.disabled = true;
            
            try {
                if (typeof google !== 'undefined' && google.accounts) {
                    google.accounts.id.prompt((notification) => {
                        console.log('Google prompt:', notification);
                        
                        if (notification.isNotDisplayed() || notification.isSkippedMoment()) {
                            // Fallback: render button
                            google.accounts.id.renderButton(btn, {
                                theme: 'outline',
                                size: 'large',
                                width: '100%'
                            });
                        }
                        
                        // Reset button after delay
                        setTimeout(() => {
                            btn.innerHTML = '<div class="google-icon"></div><span>Sign in with Google</span>';
                            btn.disabled = false;
                        }, 3000);
                    });
                } else {
                    throw new Error('Google Sign In not available');
                }
            } catch (error) {
                console.error('Google Sign In Error:', error);
                showMessage('Google Sign In failed: ' + error.message, 'error');
                
                btn.innerHTML = '<div class="google-icon"></div><span>Sign in with Google</span>';
                btn.disabled = false;
            }
        });

        // Apple Sign In button
        document.getElementById('appleSignIn').addEventListener('click', function() {
            const btn = this;
            btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Signing in...';
            btn.disabled = true;
            
            try {
                if (typeof AppleID !== 'undefined') {
                    AppleID.auth.signIn().then((response) => {
                        console.log('Apple Sign In Success:', response);
                        
                        const { authorization, user } = response;
                        const userName = user?.name ? `${user.name.firstName} ${user.name.lastName}` : 'Apple User';
                        
                        showMessage(`Welcome ${userName}! Apple Sign In successful`, 'success');
                        
                        // Store user info
                        localStorage.setItem('tincada_user', JSON.stringify({
                            id: authorization.code,
                            name: userName,
                            email: user?.email || '<EMAIL>',
                            provider: 'apple',
                            loginTime: new Date().toISOString()
                        }));
                        
                        // Redirect after delay
                        setTimeout(() => {
                            window.location.href = 'dashboard.html';
                        }, 2000);
                        
                    }).catch((error) => {
                        console.error('Apple Sign In Error:', error);
                        showMessage('Apple Sign In failed: ' + error.message, 'error');
                    }).finally(() => {
                        btn.innerHTML = '<i class="fab fa-apple" style="font-size: 1.2rem;"></i><span>Sign in with Apple</span>';
                        btn.disabled = false;
                    });
                } else {
                    throw new Error('Apple Sign In not available');
                }
            } catch (error) {
                console.error('Apple Sign In Error:', error);
                showMessage('Apple Sign In failed: ' + error.message, 'error');
                
                btn.innerHTML = '<i class="fab fa-apple" style="font-size: 1.2rem;"></i><span>Sign in with Apple</span>';
                btn.disabled = false;
            }
        });

        // Initialize when page loads
        window.addEventListener('load', function() {
            console.log('🚀 Social Login Test page loaded');
            
            // Check SDK status immediately
            checkSDKStatus();
            
            // Initialize OAuth after delay
            setTimeout(() => {
                initializeGoogle();
                initializeApple();
                checkSDKStatus(); // Check again after initialization
            }, 1000);
            
            // Periodic status check
            setInterval(checkSDKStatus, 2000);
        });
    </script>
</body>
</html>
