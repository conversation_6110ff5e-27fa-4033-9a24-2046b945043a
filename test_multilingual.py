#!/usr/bin/env python3
"""
Test Tincada AI multilingual capabilities
"""

import requests
import json
import time

def test_language(language_name, message, expected_keywords=None):
    """Test AI response in specific language"""
    try:
        data = {
            "message": message,
            "user_id": "multilingual_test",
            "chat_history": []
        }
        
        print(f"\n🌍 Testing {language_name}")
        print(f"📤 Input: {message}")
        
        response = requests.post(
            'http://localhost:5001/api/chat',
            json=data,
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            ai_response = result['response']
            
            print(f"✅ Response received")
            print(f"📊 Source: {result['source']}")
            print(f"🔢 Tokens: {result['tokens_used']}")
            print(f"📝 Response: {ai_response[:200]}...")
            
            # Check if response contains expected keywords (basic language detection)
            if expected_keywords:
                found_keywords = [kw for kw in expected_keywords if kw.lower() in ai_response.lower()]
                if found_keywords:
                    print(f"✅ Language detected: Found keywords {found_keywords}")
                else:
                    print(f"⚠️ Language detection uncertain")
            
            return True
        else:
            print(f"❌ Failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def main():
    """Test multiple languages"""
    
    print("🌐 Testing Tincada AI Multilingual Capabilities")
    print("=" * 60)
    
    # Test cases for different languages
    test_cases = [
        {
            "language": "Somali",
            "message": "Salam alaykum! Sidee tahay? Maxaad sameen kartaa?",
            "keywords": ["salam", "tahay", "caawin"]
        },
        {
            "language": "Arabic", 
            "message": "السلام عليكم! كيف حالك؟ ماذا تستطيع أن تفعل؟",
            "keywords": ["السلام", "كيف", "مساعدة"]
        },
        {
            "language": "English",
            "message": "Hello! How are you? What can you do?",
            "keywords": ["hello", "help", "can"]
        },
        {
            "language": "Spanish",
            "message": "¡Hola! ¿Cómo estás? ¿Qué puedes hacer?",
            "keywords": ["hola", "cómo", "puedo"]
        },
        {
            "language": "French",
            "message": "Bonjour! Comment allez-vous? Que pouvez-vous faire?",
            "keywords": ["bonjour", "comment", "puis"]
        },
        {
            "language": "German",
            "message": "Hallo! Wie geht es dir? Was kannst du tun?",
            "keywords": ["hallo", "wie", "kann"]
        },
        {
            "language": "Italian",
            "message": "Ciao! Come stai? Cosa puoi fare?",
            "keywords": ["ciao", "come", "posso"]
        },
        {
            "language": "Turkish",
            "message": "Merhaba! Nasılsın? Ne yapabilirsin?",
            "keywords": ["merhaba", "nasıl", "yardım"]
        },
        {
            "language": "Swahili",
            "message": "Hujambo! Habari? Unaweza kufanya nini?",
            "keywords": ["hujambo", "habari", "kusaidia"]
        },
        {
            "language": "Amharic",
            "message": "ሰላም! እንዴት ነህ? ምን ማድረግ ትችላለህ?",
            "keywords": ["ሰላም", "እንዴት", "መርዳት"]
        }
    ]
    
    successful_tests = 0
    total_tests = len(test_cases)
    
    for test_case in test_cases:
        success = test_language(
            test_case["language"],
            test_case["message"], 
            test_case.get("keywords")
        )
        
        if success:
            successful_tests += 1
            
        time.sleep(1)  # Small delay between tests
        print("-" * 40)
    
    # Summary
    print(f"\n📊 Test Results Summary:")
    print(f"✅ Successful: {successful_tests}/{total_tests}")
    print(f"📈 Success Rate: {(successful_tests/total_tests)*100:.1f}%")
    
    if successful_tests == total_tests:
        print("\n🎉 All language tests passed! Tincada AI is truly multilingual!")
    elif successful_tests > total_tests * 0.7:
        print("\n👍 Most language tests passed! Good multilingual support.")
    else:
        print("\n⚠️ Some language tests failed. May need adjustment.")

if __name__ == "__main__":
    main()
