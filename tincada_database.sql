-- 🗄️ TINCADA AI DATABASE SCHEMA
-- Complete database structure for Tincada AI system
-- Version: 2.0.0

-- Create database
CREATE DATABASE IF NOT EXISTS tincada_ai_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE tincada_ai_db;

-- =====================================================
-- 1. USERS TABLE - User Management
-- =====================================================
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id VARCHAR(50) UNIQUE NOT NULL,
    username VARCHAR(100) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    phone VARCHAR(20),
    password_hash VARCHAR(255),
    profile_picture TEXT,
    account_type ENUM('free', 'premium', 'enterprise') DEFAULT 'free',
    account_status ENUM('active', 'suspended', 'deleted') DEFAULT 'active',
    total_messages INT DEFAULT 0,
    total_tokens_used INT DEFAULT 0,
    daily_message_limit INT DEFAULT 100,
    api_key VARCHAR(255),
    preferences JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    last_active TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_user_id (user_id),
    INDEX idx_email (email),
    INDEX idx_account_type (account_type),
    INDEX idx_last_active (last_active)
);

-- =====================================================
-- 2. CONVERSATIONS TABLE - Chat Sessions
-- =====================================================
CREATE TABLE conversations (
    id INT AUTO_INCREMENT PRIMARY KEY,
    conversation_id VARCHAR(50) UNIQUE NOT NULL,
    user_id VARCHAR(50) NOT NULL,
    title VARCHAR(255) DEFAULT 'New Conversation',
    message_count INT DEFAULT 0,
    total_tokens INT DEFAULT 0,
    conversation_status ENUM('active', 'archived', 'deleted') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
    INDEX idx_conversation_id (conversation_id),
    INDEX idx_user_id (user_id),
    INDEX idx_status (conversation_status),
    INDEX idx_updated_at (updated_at)
);

-- =====================================================
-- 3. CHAT MESSAGES TABLE - All Messages
-- =====================================================
CREATE TABLE chat_messages (
    id INT AUTO_INCREMENT PRIMARY KEY,
    message_id VARCHAR(50) UNIQUE NOT NULL DEFAULT (UUID()),
    conversation_id VARCHAR(50) NOT NULL,
    user_id VARCHAR(50) NOT NULL,
    message_type ENUM('user', 'ai', 'system') NOT NULL,
    message_content TEXT NOT NULL,
    message_language VARCHAR(10) DEFAULT 'en',
    tokens_used INT DEFAULT 0,
    response_source ENUM('openai', 'fallback', 'cached') DEFAULT 'fallback',
    response_time_ms INT DEFAULT 0,
    attachments JSON,
    metadata JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (conversation_id) REFERENCES conversations(conversation_id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
    INDEX idx_conversation_id (conversation_id),
    INDEX idx_user_id (user_id),
    INDEX idx_message_type (message_type),
    INDEX idx_language (message_language),
    INDEX idx_created_at (created_at),
    FULLTEXT idx_content (message_content)
);

-- =====================================================
-- 4. GENERATED IMAGES TABLE - Image Generation
-- =====================================================
CREATE TABLE generated_images (
    id INT AUTO_INCREMENT PRIMARY KEY,
    image_id VARCHAR(50) UNIQUE NOT NULL DEFAULT (UUID()),
    user_id VARCHAR(50) NOT NULL,
    conversation_id VARCHAR(50),
    image_prompt TEXT NOT NULL,
    image_url TEXT,
    image_path VARCHAR(500),
    image_size VARCHAR(20) DEFAULT '1024x1024',
    generation_method ENUM('openai', 'replicate', 'local') DEFAULT 'openai',
    generation_time_ms INT DEFAULT 0,
    file_size_bytes INT DEFAULT 0,
    status ENUM('pending', 'completed', 'failed') DEFAULT 'pending',
    error_message TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
    FOREIGN KEY (conversation_id) REFERENCES conversations(conversation_id) ON DELETE SET NULL,
    INDEX idx_user_id (user_id),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at),
    FULLTEXT idx_prompt (image_prompt)
);

-- =====================================================
-- 5. PAYMENTS TABLE - Payment Management
-- =====================================================
CREATE TABLE payments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    payment_id VARCHAR(50) UNIQUE NOT NULL DEFAULT (UUID()),
    user_id VARCHAR(50) NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'USD',
    payment_method ENUM('stripe', 'paypal', 'crypto', 'bank_transfer') NOT NULL,
    payment_status ENUM('pending', 'completed', 'failed', 'refunded') DEFAULT 'pending',
    transaction_id VARCHAR(255),
    plan_type ENUM('premium_monthly', 'premium_yearly', 'enterprise', 'tokens') NOT NULL,
    tokens_purchased INT DEFAULT 0,
    payment_gateway_response JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP NULL,
    
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_status (payment_status),
    INDEX idx_plan_type (plan_type),
    INDEX idx_created_at (created_at)
);

-- =====================================================
-- 6. API USAGE TABLE - Usage Statistics
-- =====================================================
CREATE TABLE api_usage (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id VARCHAR(50) NOT NULL,
    api_type ENUM('chat', 'image', 'voice', 'file_upload') NOT NULL,
    endpoint VARCHAR(100) NOT NULL,
    tokens_used INT DEFAULT 0,
    request_count INT DEFAULT 1,
    response_time_ms INT DEFAULT 0,
    success BOOLEAN DEFAULT TRUE,
    error_message TEXT,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_api_type (api_type),
    INDEX idx_success (success),
    INDEX idx_created_at (created_at)
);

-- =====================================================
-- 7. ACTIVITY LOGS TABLE - System Activity
-- =====================================================
CREATE TABLE activity_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id VARCHAR(50),
    action VARCHAR(100) NOT NULL,
    details TEXT,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE SET NULL,
    INDEX idx_user_id (user_id),
    INDEX idx_action (action),
    INDEX idx_created_at (created_at)
);

-- =====================================================
-- 8. SYSTEM SETTINGS TABLE - Configuration
-- =====================================================
CREATE TABLE system_settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    setting_key VARCHAR(100) UNIQUE NOT NULL,
    setting_value TEXT,
    setting_type ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string',
    description TEXT,
    is_public BOOLEAN DEFAULT FALSE,
    updated_by VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_setting_key (setting_key),
    INDEX idx_is_public (is_public)
);

-- =====================================================
-- VIEWS FOR ANALYTICS
-- =====================================================

-- User Statistics View
CREATE VIEW user_stats AS
SELECT 
    u.user_id,
    u.username,
    u.email,
    u.account_type,
    u.total_messages,
    u.total_tokens_used,
    COUNT(DISTINCT c.conversation_id) as total_conversations,
    COUNT(DISTINCT gi.image_id) as total_images_generated,
    SUM(p.amount) as total_spent,
    u.created_at,
    u.last_active
FROM users u
LEFT JOIN conversations c ON u.user_id = c.user_id
LEFT JOIN generated_images gi ON u.user_id = gi.user_id
LEFT JOIN payments p ON u.user_id = p.user_id AND p.payment_status = 'completed'
GROUP BY u.user_id;

-- Daily Activity View
CREATE VIEW daily_activity AS
SELECT 
    DATE(created_at) as activity_date,
    COUNT(*) as total_messages,
    COUNT(DISTINCT user_id) as active_users,
    SUM(tokens_used) as total_tokens,
    AVG(response_time_ms) as avg_response_time
FROM chat_messages
WHERE created_at >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
GROUP BY DATE(created_at)
ORDER BY activity_date DESC;

-- Language Usage View
CREATE VIEW language_usage AS
SELECT 
    message_language,
    COUNT(*) as message_count,
    COUNT(DISTINCT user_id) as unique_users,
    AVG(tokens_used) as avg_tokens_per_message
FROM chat_messages
WHERE message_language IS NOT NULL
GROUP BY message_language
ORDER BY message_count DESC;

-- Recent Activity View
CREATE VIEW recent_activity AS
SELECT 
    cm.id,
    cm.user_id,
    u.username,
    cm.conversation_id,
    c.title as conversation_title,
    cm.message_type,
    LEFT(cm.message_content, 100) as message_preview,
    cm.tokens_used,
    cm.response_source,
    cm.created_at
FROM chat_messages cm
JOIN users u ON cm.user_id = u.user_id
JOIN conversations c ON cm.conversation_id = c.conversation_id
ORDER BY cm.created_at DESC
LIMIT 50;

-- =====================================================
-- SAMPLE DATA FOR TESTING
-- =====================================================

-- Insert sample users
INSERT INTO users (user_id, username, email, account_type) VALUES
('user_001', 'Ahmed123', '<EMAIL>', 'premium'),
('user_002', 'Fatima_AI', '<EMAIL>', 'free'),
('user_003', 'Mohamed_Dev', '<EMAIL>', 'enterprise'),
('user_004', 'Khadija_Tech', '<EMAIL>', 'free'),
('user_005', 'Omar_Code', '<EMAIL>', 'premium');

-- Insert sample conversations
INSERT INTO conversations (conversation_id, user_id, title) VALUES
('conv_001', 'user_001', 'AI Programming Help'),
('conv_002', 'user_002', 'Somali Language Chat'),
('conv_003', 'user_003', 'Code Generation'),
('conv_004', 'user_004', 'General Questions'),
('conv_005', 'user_005', 'Image Generation');

-- Insert sample messages
INSERT INTO chat_messages (conversation_id, user_id, message_type, message_content, message_language, tokens_used, response_source) VALUES
('conv_001', 'user_001', 'user', 'Can you help me with JavaScript?', 'en', 8, 'openai'),
('conv_001', 'user_001', 'ai', 'Of course! I can help you with JavaScript. What specific topic would you like to learn about?', 'en', 20, 'openai'),
('conv_002', 'user_002', 'user', 'Salaan, sidee tahay?', 'so', 5, 'openai'),
('conv_002', 'user_002', 'ai', 'Salaan! Waan fiican ahay, mahadsanid. Sidee kaa caawin karaa?', 'so', 15, 'openai'),
('conv_003', 'user_003', 'user', 'Generate HTML form code', 'en', 6, 'openai'),
('conv_003', 'user_003', 'ai', 'Here is a sample HTML form:\n\n```html\n<form>\n  <input type="text" placeholder="Name">\n  <input type="email" placeholder="Email">\n  <button type="submit">Submit</button>\n</form>\n```', 'en', 35, 'openai');

-- Insert sample payments
INSERT INTO payments (user_id, amount, payment_method, payment_status, plan_type) VALUES
('user_001', 29.99, 'stripe', 'completed', 'premium_monthly'),
('user_003', 299.99, 'paypal', 'completed', 'enterprise'),
('user_005', 19.99, 'stripe', 'pending', 'premium_monthly');

-- Insert system settings
INSERT INTO system_settings (setting_key, setting_value, setting_type, description, is_public) VALUES
('max_daily_messages_free', '100', 'number', 'Maximum daily messages for free users', TRUE),
('max_daily_messages_premium', '1000', 'number', 'Maximum daily messages for premium users', TRUE),
('max_daily_messages_enterprise', '10000', 'number', 'Maximum daily messages for enterprise users', TRUE),
('openai_model', 'gpt-4o-mini', 'string', 'Default OpenAI model to use', FALSE),
('image_generation_enabled', 'true', 'boolean', 'Enable image generation feature', TRUE),
('maintenance_mode', 'false', 'boolean', 'System maintenance mode', TRUE);

-- =====================================================
-- INDEXES FOR PERFORMANCE
-- =====================================================

-- Additional indexes for better performance
CREATE INDEX idx_messages_user_date ON chat_messages(user_id, created_at);
CREATE INDEX idx_messages_conversation_date ON chat_messages(conversation_id, created_at);
CREATE INDEX idx_users_type_status ON users(account_type, account_status);
CREATE INDEX idx_payments_user_status ON payments(user_id, payment_status);
CREATE INDEX idx_images_user_status ON generated_images(user_id, status);

-- =====================================================
-- TRIGGERS FOR AUTOMATIC UPDATES
-- =====================================================

-- Update user message count when new message is added
DELIMITER //
CREATE TRIGGER update_user_message_count 
AFTER INSERT ON chat_messages
FOR EACH ROW
BEGIN
    UPDATE users 
    SET total_messages = total_messages + 1,
        total_tokens_used = total_tokens_used + COALESCE(NEW.tokens_used, 0),
        last_active = NOW()
    WHERE user_id = NEW.user_id;
    
    UPDATE conversations 
    SET message_count = message_count + 1,
        total_tokens = total_tokens + COALESCE(NEW.tokens_used, 0),
        updated_at = NOW()
    WHERE conversation_id = NEW.conversation_id;
END//
DELIMITER ;

-- Update conversation title based on first user message
DELIMITER //
CREATE TRIGGER update_conversation_title 
AFTER INSERT ON chat_messages
FOR EACH ROW
BEGIN
    IF NEW.message_type = 'user' THEN
        UPDATE conversations 
        SET title = CASE 
            WHEN message_count = 1 THEN LEFT(NEW.message_content, 50)
            ELSE title 
        END
        WHERE conversation_id = NEW.conversation_id;
    END IF;
END//
DELIMITER ;

-- =====================================================
-- STORED PROCEDURES
-- =====================================================

-- Get user dashboard statistics
DELIMITER //
CREATE PROCEDURE GetUserDashboardStats(IN p_user_id VARCHAR(50))
BEGIN
    SELECT 
        (SELECT COUNT(*) FROM chat_messages WHERE user_id = p_user_id) as total_messages,
        (SELECT COUNT(DISTINCT conversation_id) FROM chat_messages WHERE user_id = p_user_id) as total_conversations,
        (SELECT COUNT(*) FROM generated_images WHERE user_id = p_user_id) as total_images,
        (SELECT SUM(tokens_used) FROM chat_messages WHERE user_id = p_user_id) as total_tokens,
        (SELECT COUNT(*) FROM chat_messages WHERE user_id = p_user_id AND DATE(created_at) = CURDATE()) as today_messages,
        (SELECT account_type FROM users WHERE user_id = p_user_id) as account_type;
END//
DELIMITER ;

-- Get system overview statistics
DELIMITER //
CREATE PROCEDURE GetSystemOverviewStats()
BEGIN
    SELECT 
        (SELECT COUNT(*) FROM users WHERE account_status = 'active') as total_active_users,
        (SELECT COUNT(*) FROM chat_messages) as total_messages,
        (SELECT COUNT(*) FROM generated_images) as total_images,
        (SELECT SUM(amount) FROM payments WHERE payment_status = 'completed') as total_revenue,
        (SELECT COUNT(*) FROM chat_messages WHERE DATE(created_at) = CURDATE()) as today_messages,
        (SELECT COUNT(DISTINCT user_id) FROM chat_messages WHERE DATE(created_at) = CURDATE()) as today_active_users;
END//
DELIMITER ;

-- =====================================================
-- COMPLETION MESSAGE
-- =====================================================
SELECT 'Tincada AI Database Schema Created Successfully!' as Status;
SELECT 'Database: tincada_ai_db' as Database_Name;
SELECT 'Tables: 8 main tables + 4 views' as Structure;
SELECT 'Sample data inserted for testing' as Sample_Data;
SELECT 'Ready for use!' as Ready;
