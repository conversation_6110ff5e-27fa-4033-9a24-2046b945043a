<?php
/**
 * 🗄️ TINCADA AI DATABASE CONFIGURATION
 * Database connection and management classes
 */

// Database configuration
define('DB_HOST', 'localhost');
define('DB_USERNAME', 'root');
define('DB_PASSWORD', ''); // Change this to your MySQL password
define('DB_NAME', 'tincada_ai_db');
define('DB_CHARSET', 'utf8mb4');

// API Configuration
define('TINCADA_API_URL', 'http://localhost:5001/api');
define('API_TIMEOUT', 30);

// Security settings
define('SESSION_TIMEOUT', 3600); // 1 hour
define('MAX_MESSAGE_LENGTH', 5000);
define('MAX_DAILY_REQUESTS', 1000);

// Error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

/**
 * Database connection class
 */
class Database {
    private $connection;
    private static $instance = null;
    
    private function __construct() {
        try {
            $dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET;
            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
            ];
            
            $this->connection = new PDO($dsn, DB_USERNAME, DB_PASSWORD, $options);
            
        } catch (PDOException $e) {
            throw new Exception("Database connection failed: " . $e->getMessage());
        }
    }
    
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    public function getConnection() {
        return $this->connection;
    }
    
    public function query($sql, $params = []) {
        try {
            $stmt = $this->connection->prepare($sql);
            $stmt->execute($params);
            return $stmt;
        } catch (PDOException $e) {
            throw new Exception("Query failed: " . $e->getMessage());
        }
    }
}

/**
 * User Management Class
 */
class UserManager {
    private $db;
    
    public function __construct($database) {
        $this->db = $database;
    }
    
    public function getAllUsers($page = 1, $limit = 10) {
        $offset = ($page - 1) * $limit;
        $stmt = $this->db->query(
            "SELECT user_id, username, email, account_type, account_status, 
                    total_messages, total_tokens_used, created_at, last_active 
             FROM users 
             ORDER BY created_at DESC 
             LIMIT ? OFFSET ?", 
            [$limit, $offset]
        );
        return $stmt->fetchAll();
    }
    
    public function getUserStats() {
        $stmt = $this->db->query("
            SELECT 
                COUNT(*) as total_users,
                COUNT(CASE WHEN account_type = 'free' THEN 1 END) as free_users,
                COUNT(CASE WHEN account_type = 'premium' THEN 1 END) as premium_users,
                COUNT(CASE WHEN account_type = 'enterprise' THEN 1 END) as enterprise_users,
                COUNT(CASE WHEN last_active >= DATE_SUB(NOW(), INTERVAL 24 HOUR) THEN 1 END) as active_today
            FROM users 
            WHERE account_status = 'active'
        ");
        return $stmt->fetch();
    }
}

/**
 * Message Management Class
 */
class MessageManager {
    private $db;
    
    public function __construct($database) {
        $this->db = $database;
    }
    
    public function getMessageStats() {
        $stmt = $this->db->query("
            SELECT 
                COUNT(*) as total_messages,
                COUNT(CASE WHEN DATE(created_at) = CURDATE() THEN 1 END) as today_messages,
                AVG(CASE WHEN DATE(created_at) >= DATE_SUB(CURDATE(), INTERVAL 7 DAY) THEN 1 ELSE 0 END) as avg_daily_messages,
                SUM(tokens_used) as total_tokens,
                SUM(CASE WHEN DATE(created_at) = CURDATE() THEN tokens_used ELSE 0 END) as today_tokens
            FROM chat_messages
        ");
        return $stmt->fetch();
    }
    
    public function getDailyMessageCount($days = 7) {
        $stmt = $this->db->query("
            SELECT 
                DATE(created_at) as date,
                COUNT(*) as count
            FROM chat_messages 
            WHERE created_at >= DATE_SUB(CURDATE(), INTERVAL ? DAY)
            GROUP BY DATE(created_at)
            ORDER BY date ASC
        ", [$days]);
        return $stmt->fetchAll();
    }
    
    public function getLanguageStats() {
        $stmt = $this->db->query("
            SELECT message_language, COUNT(*) as count 
            FROM chat_messages 
            WHERE message_language IS NOT NULL 
            GROUP BY message_language 
            ORDER BY count DESC
            LIMIT 10
        ");
        return $stmt->fetchAll();
    }
    
    public function getRecentMessages($limit = 10) {
        $stmt = $this->db->query("
            SELECT cm.*, u.username 
            FROM chat_messages cm
            LEFT JOIN users u ON cm.user_id = u.user_id
            ORDER BY cm.created_at DESC 
            LIMIT ?
        ", [$limit]);
        return $stmt->fetchAll();
    }
}

/**
 * Payment Management Class
 */
class PaymentManager {
    private $db;
    
    public function __construct($database) {
        $this->db = $database;
    }
    
    public function getPaymentStats() {
        $stmt = $this->db->query("
            SELECT 
                COUNT(*) as total_transactions,
                SUM(CASE WHEN payment_status = 'completed' THEN amount ELSE 0 END) as total_revenue,
                SUM(CASE WHEN payment_status = 'completed' AND DATE(created_at) >= DATE_SUB(CURDATE(), INTERVAL 30 DAY) THEN amount ELSE 0 END) as monthly_revenue,
                COUNT(CASE WHEN payment_status = 'pending' THEN 1 END) as pending_payments,
                COUNT(CASE WHEN payment_status = 'failed' THEN 1 END) as failed_payments
            FROM payments
        ");
        return $stmt->fetch();
    }
    
    public function getRecentPayments($limit = 10) {
        $stmt = $this->db->query("
            SELECT p.*, u.username 
            FROM payments p
            LEFT JOIN users u ON p.user_id = u.user_id
            ORDER BY p.created_at DESC 
            LIMIT ?
        ", [$limit]);
        return $stmt->fetchAll();
    }
}

// Initialize database connection and managers
try {
    $database = Database::getInstance();
    $db = $database->getConnection();
    
    $userManager = new UserManager($database);
    $messageManager = new MessageManager($database);
    $paymentManager = new PaymentManager($database);
    
} catch (Exception $e) {
    // If database connection fails, we'll use mock data
    $database_error = $e->getMessage();
    $db = null;
    $userManager = null;
    $messageManager = null;
    $paymentManager = null;
}

/**
 * Utility Functions
 */
function formatNumber($number) {
    if ($number >= 1000000) {
        return round($number / 1000000, 1) . 'M';
    } elseif ($number >= 1000) {
        return round($number / 1000, 1) . 'K';
    }
    return number_format($number);
}

function formatCurrency($amount) {
    return '$' . number_format($amount, 2);
}

function timeAgo($datetime) {
    $time = time() - strtotime($datetime);
    
    if ($time < 60) return 'just now';
    if ($time < 3600) return floor($time/60) . ' minutes ago';
    if ($time < 86400) return floor($time/3600) . ' hours ago';
    if ($time < 2592000) return floor($time/86400) . ' days ago';
    if ($time < 31536000) return floor($time/2592000) . ' months ago';
    return floor($time/31536000) . ' years ago';
}

/**
 * API Response Helper
 */
function sendJsonResponse($data, $status = 200) {
    http_response_code($status);
    header('Content-Type: application/json');
    echo json_encode($data);
    exit;
}

function sendError($message, $status = 400) {
    sendJsonResponse(['error' => $message, 'status' => $status], $status);
}

/**
 * Security Functions
 */
function sanitizeInput($input) {
    return htmlspecialchars(strip_tags(trim($input)), ENT_QUOTES, 'UTF-8');
}

function validateApiKey($key) {
    // Basic API key validation
    return !empty($key) && strlen($key) > 20;
}

function rateLimitCheck($user_id) {
    // Simple rate limiting - can be enhanced
    global $db;
    if (!$db) return true; // Skip if no database
    
    try {
        $stmt = $db->prepare("
            SELECT COUNT(*) as count 
            FROM chat_messages 
            WHERE user_id = ? AND created_at >= DATE_SUB(NOW(), INTERVAL 1 DAY)
        ");
        $stmt->execute([$user_id]);
        $result = $stmt->fetch();
        
        return $result['count'] < MAX_DAILY_REQUESTS;
    } catch (Exception $e) {
        return true; // Allow if check fails
    }
}

/**
 * Logging Function
 */
function logActivity($user_id, $action, $details = '') {
    global $db;
    if (!$db) return; // Skip if no database
    
    try {
        $stmt = $db->prepare("
            INSERT INTO activity_logs (user_id, action, details, created_at) 
            VALUES (?, ?, ?, NOW())
        ");
        $stmt->execute([$user_id, $action, $details]);
    } catch (Exception $e) {
        // Log to file if database fails
        error_log("Activity log failed: " . $e->getMessage());
    }
}

?>
