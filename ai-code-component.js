/**
 * AI Code Block Component
 * Professional code display with syntax highlighting, copy functionality, and dark theme
 */

class AICodeBlock {
    constructor() {
        this.codeCounter = 0;
        this.initializeStyles();
    }

    // Initialize CSS styles for code blocks
    initializeStyles() {
        if (document.getElementById('ai-code-styles')) return;

        const styles = `
            <style id="ai-code-styles">
                .ai-code-block {
                    background: #1e1e1e;
                    border-radius: 12px;
                    margin: 1.5rem 0;
                    overflow: hidden;
                    border: 1px solid #333;
                    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
                    position: relative;
                    font-family: 'Fira Code', 'Consolas', 'Monaco', monospace;
                }

                .ai-code-header {
                    background: #2d2d2d;
                    padding: 12px 16px;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    border-bottom: 1px solid #333;
                }

                .ai-code-language {
                    color: #9cdcfe;
                    font-size: 0.9rem;
                    font-weight: 600;
                    text-transform: uppercase;
                    letter-spacing: 0.5px;
                }

                .ai-code-actions {
                    display: flex;
                    gap: 8px;
                }

                .ai-code-btn {
                    background: rgba(255, 255, 255, 0.1);
                    border: 1px solid rgba(255, 255, 255, 0.2);
                    color: #fff;
                    padding: 6px 12px;
                    border-radius: 6px;
                    font-size: 0.8rem;
                    cursor: pointer;
                    transition: all 0.3s ease;
                    display: flex;
                    align-items: center;
                    gap: 6px;
                }

                .ai-code-btn:hover {
                    background: rgba(255, 255, 255, 0.2);
                    transform: translateY(-1px);
                }

                .ai-code-btn.success {
                    background: #4caf50 !important;
                    color: white !important;
                }

                .ai-code-content {
                    padding: 0;
                    background: #1e1e1e;
                    overflow-x: auto;
                }

                .ai-code-content pre {
                    margin: 0;
                    padding: 16px;
                    background: transparent;
                    font-family: inherit;
                    font-size: 14px;
                    line-height: 1.5;
                    color: #d4d4d4;
                    white-space: pre-wrap;
                    word-wrap: break-word;
                }

                /* Syntax highlighting */
                .ai-code-keyword { color: #569cd6; font-weight: bold; }
                .ai-code-string { color: #ce9178; }
                .ai-code-function { color: #dcdcaa; }
                .ai-code-number { color: #b5cea8; }
                .ai-code-operator { color: #d4d4d4; }
                .ai-code-punctuation { color: #d4d4d4; }
                .ai-code-comment { color: #6a9955; font-style: italic; }
                .ai-code-property { color: #9cdcfe; }
                .ai-code-selector { color: #d7ba7d; }
                .ai-code-tag { color: #569cd6; }
                .ai-code-attr-name { color: #9cdcfe; }
                .ai-code-attr-value { color: #ce9178; }

                @media (max-width: 768px) {
                    .ai-code-content pre {
                        font-size: 12px;
                        padding: 12px;
                    }
                    
                    .ai-code-header {
                        padding: 8px 12px;
                    }
                    
                    .ai-code-btn {
                        padding: 4px 8px;
                        font-size: 0.7rem;
                    }
                }
            </style>
        `;

        document.head.insertAdjacentHTML('beforeend', styles);
    }

    // Create a code block with syntax highlighting
    createCodeBlock(code, language = 'javascript', description = '') {
        this.codeCounter++;
        const codeId = `ai-code-${this.codeCounter}`;
        
        const highlightedCode = this.highlightSyntax(code, language);
        
        const codeBlock = `
            <div class="ai-code-block">
                <div class="ai-code-header">
                    <span class="ai-code-language">${language}</span>
                    <div class="ai-code-actions">
                        <button class="ai-code-btn" onclick="aiCodeBlock.copyCode('${codeId}', this)">
                            <i class="fas fa-copy"></i> Copy
                        </button>
                        <button class="ai-code-btn" onclick="aiCodeBlock.editCode('${codeId}')">
                            <i class="fas fa-edit"></i> Edit
                        </button>
                    </div>
                </div>
                <div class="ai-code-content">
                    <pre id="${codeId}"><code>${highlightedCode}</code></pre>
                </div>
            </div>
        `;

        return codeBlock;
    }

    // Simple syntax highlighting
    highlightSyntax(code, language) {
        let highlighted = code;

        // Escape HTML
        highlighted = highlighted
            .replace(/&/g, '&amp;')
            .replace(/</g, '&lt;')
            .replace(/>/g, '&gt;');

        switch (language.toLowerCase()) {
            case 'javascript':
            case 'js':
                highlighted = this.highlightJavaScript(highlighted);
                break;
            case 'css':
                highlighted = this.highlightCSS(highlighted);
                break;
            case 'html':
                highlighted = this.highlightHTML(highlighted);
                break;
            case 'python':
                highlighted = this.highlightPython(highlighted);
                break;
            case 'sql':
                highlighted = this.highlightSQL(highlighted);
                break;
            default:
                highlighted = this.highlightGeneric(highlighted);
        }

        return highlighted;
    }

    // JavaScript syntax highlighting
    highlightJavaScript(code) {
        const keywords = ['const', 'let', 'var', 'function', 'if', 'else', 'for', 'while', 'return', 'class', 'new', 'this', 'import', 'export', 'from', 'async', 'await', 'try', 'catch', 'finally'];
        const functions = ['console', 'document', 'window', 'localStorage', 'JSON', 'Array', 'Object', 'String', 'Number', 'Date'];

        let highlighted = code;

        // Keywords
        keywords.forEach(keyword => {
            const regex = new RegExp(`\\b${keyword}\\b`, 'g');
            highlighted = highlighted.replace(regex, `<span class="ai-code-keyword">${keyword}</span>`);
        });

        // Functions
        functions.forEach(func => {
            const regex = new RegExp(`\\b${func}\\b`, 'g');
            highlighted = highlighted.replace(regex, `<span class="ai-code-function">${func}</span>`);
        });

        // Strings
        highlighted = highlighted.replace(/(["'`])((?:\\.|(?!\1)[^\\])*?)\1/g, '<span class="ai-code-string">$1$2$1</span>');

        // Numbers
        highlighted = highlighted.replace(/\b\d+\.?\d*\b/g, '<span class="ai-code-number">$&</span>');

        // Comments
        highlighted = highlighted.replace(/\/\/.*$/gm, '<span class="ai-code-comment">$&</span>');
        highlighted = highlighted.replace(/\/\*[\s\S]*?\*\//g, '<span class="ai-code-comment">$&</span>');

        return highlighted;
    }

    // CSS syntax highlighting
    highlightCSS(code) {
        let highlighted = code;

        // Selectors
        highlighted = highlighted.replace(/^([.#]?[\w-]+)(?=\s*{)/gm, '<span class="ai-code-selector">$1</span>');

        // Properties
        highlighted = highlighted.replace(/(\w+)(?=\s*:)/g, '<span class="ai-code-property">$1</span>');

        // Values
        highlighted = highlighted.replace(/:([^;]+);/g, ': <span class="ai-code-string">$1</span>;');

        // Comments
        highlighted = highlighted.replace(/\/\*[\s\S]*?\*\//g, '<span class="ai-code-comment">$&</span>');

        return highlighted;
    }

    // HTML syntax highlighting
    highlightHTML(code) {
        let highlighted = code;

        // Tags
        highlighted = highlighted.replace(/&lt;(\/?[\w-]+)([^&]*?)&gt;/g, (match, tagName, attributes) => {
            let result = `&lt;<span class="ai-code-tag">${tagName}</span>`;
            
            // Attributes
            if (attributes) {
                result += attributes.replace(/([\w-]+)=(".*?")/g, ' <span class="ai-code-attr-name">$1</span>=<span class="ai-code-attr-value">$2</span>');
            }
            
            return result + '&gt;';
        });

        // Comments
        highlighted = highlighted.replace(/&lt;!--[\s\S]*?--&gt;/g, '<span class="ai-code-comment">$&</span>');

        return highlighted;
    }

    // Python syntax highlighting
    highlightPython(code) {
        const keywords = ['def', 'class', 'if', 'elif', 'else', 'for', 'while', 'return', 'import', 'from', 'as', 'try', 'except', 'finally', 'with', 'lambda', 'and', 'or', 'not', 'in', 'is'];
        
        let highlighted = code;

        // Keywords
        keywords.forEach(keyword => {
            const regex = new RegExp(`\\b${keyword}\\b`, 'g');
            highlighted = highlighted.replace(regex, `<span class="ai-code-keyword">${keyword}</span>`);
        });

        // Strings
        highlighted = highlighted.replace(/(["'])((?:\\.|(?!\1)[^\\])*?)\1/g, '<span class="ai-code-string">$1$2$1</span>');

        // Comments
        highlighted = highlighted.replace(/#.*$/gm, '<span class="ai-code-comment">$&</span>');

        // Numbers
        highlighted = highlighted.replace(/\b\d+\.?\d*\b/g, '<span class="ai-code-number">$&</span>');

        return highlighted;
    }

    // SQL syntax highlighting
    highlightSQL(code) {
        const keywords = ['SELECT', 'FROM', 'WHERE', 'JOIN', 'LEFT', 'RIGHT', 'INNER', 'OUTER', 'ON', 'GROUP', 'BY', 'ORDER', 'HAVING', 'INSERT', 'UPDATE', 'DELETE', 'CREATE', 'ALTER', 'DROP', 'TABLE', 'INDEX', 'VIEW'];
        
        let highlighted = code;

        // Keywords (case insensitive)
        keywords.forEach(keyword => {
            const regex = new RegExp(`\\b${keyword}\\b`, 'gi');
            highlighted = highlighted.replace(regex, `<span class="ai-code-keyword">${keyword}</span>`);
        });

        // Strings
        highlighted = highlighted.replace(/(["'])((?:\\.|(?!\1)[^\\])*?)\1/g, '<span class="ai-code-string">$1$2$1</span>');

        // Comments
        highlighted = highlighted.replace(/--.*$/gm, '<span class="ai-code-comment">$&</span>');

        return highlighted;
    }

    // Generic highlighting for unknown languages
    highlightGeneric(code) {
        let highlighted = code;

        // Strings
        highlighted = highlighted.replace(/(["'`])((?:\\.|(?!\1)[^\\])*?)\1/g, '<span class="ai-code-string">$1$2$1</span>');

        // Numbers
        highlighted = highlighted.replace(/\b\d+\.?\d*\b/g, '<span class="ai-code-number">$&</span>');

        // Comments (various styles)
        highlighted = highlighted.replace(/\/\/.*$/gm, '<span class="ai-code-comment">$&</span>');
        highlighted = highlighted.replace(/\/\*[\s\S]*?\*\//g, '<span class="ai-code-comment">$&</span>');
        highlighted = highlighted.replace(/#.*$/gm, '<span class="ai-code-comment">$&</span>');

        return highlighted;
    }

    // Copy code to clipboard
    async copyCode(codeId, button) {
        try {
            const codeElement = document.getElementById(codeId);
            const code = codeElement.textContent;
            
            await navigator.clipboard.writeText(code);
            
            const originalHTML = button.innerHTML;
            button.innerHTML = '<i class="fas fa-check"></i> Copied!';
            button.classList.add('success');
            
            setTimeout(() => {
                button.innerHTML = originalHTML;
                button.classList.remove('success');
            }, 2000);
            
        } catch (err) {
            console.error('Failed to copy code:', err);
            
            // Fallback for older browsers
            const codeElement = document.getElementById(codeId);
            const range = document.createRange();
            range.selectNode(codeElement);
            window.getSelection().removeAllRanges();
            window.getSelection().addRange(range);
            
            try {
                document.execCommand('copy');
                window.getSelection().removeAllRanges();
                
                const originalHTML = button.innerHTML;
                button.innerHTML = '<i class="fas fa-check"></i> Copied!';
                button.classList.add('success');
                
                setTimeout(() => {
                    button.innerHTML = originalHTML;
                    button.classList.remove('success');
                }, 2000);
            } catch (fallbackErr) {
                alert('Copy failed. Please select and copy manually.');
            }
        }
    }

    // Edit code - opens advanced code review modal
    editCode(codeId) {
        console.log('Opening code editor for:', codeId);

        const codeElement = document.getElementById(codeId);
        if (!codeElement) {
            console.error('Code element not found:', codeId);
            return;
        }

        const code = codeElement.textContent;
        const language = this.detectLanguageFromCode(code);

        this.openCodeReviewModal(code, language, codeId);
    }

    // Detect programming language from code content
    detectLanguageFromCode(code) {
        // JavaScript patterns
        if (/\b(function|const|let|var|=>|console\.log|document\.|window\.)\b/.test(code)) {
            return 'javascript';
        }
        // Python patterns
        if (/\b(def|import|from|print|if __name__|class)\b/.test(code)) {
            return 'python';
        }
        // HTML patterns
        if (/<\/?[a-z][\s\S]*>/i.test(code)) {
            return 'html';
        }
        // CSS patterns
        if (/[.#]?[\w-]+\s*{[\s\S]*}/.test(code)) {
            return 'css';
        }
        // SQL patterns
        if (/\b(SELECT|INSERT|UPDATE|DELETE|CREATE|ALTER|DROP)\b/i.test(code)) {
            return 'sql';
        }

        return 'javascript'; // default
    }

    // Open advanced code review modal
    openCodeReviewModal(code, language, originalCodeId) {
        // Remove existing modal if any
        const existingModal = document.getElementById('code-review-modal');
        if (existingModal) {
            existingModal.remove();
        }

        // Create modal HTML
        const modalHtml = `
            <div id="code-review-modal" class="code-review-modal">
                <div class="code-review-overlay" onclick="aiCodeBlock.closeCodeReviewModal()"></div>
                <div class="code-review-container">
                    <div class="code-review-header">
                        <h3><i class="fas fa-code"></i> Code Review & Editor</h3>
                        <div class="code-review-actions">
                            <select id="languageSelector" class="language-selector">
                                <option value="javascript" ${language === 'javascript' ? 'selected' : ''}>JavaScript</option>
                                <option value="python" ${language === 'python' ? 'selected' : ''}>Python</option>
                                <option value="html" ${language === 'html' ? 'selected' : ''}>HTML</option>
                                <option value="css" ${language === 'css' ? 'selected' : ''}>CSS</option>
                                <option value="sql" ${language === 'sql' ? 'selected' : ''}>SQL</option>
                            </select>
                            <button class="modal-btn" onclick="aiCodeBlock.copyModalCode()">
                                <i class="fas fa-copy"></i> Copy
                            </button>
                            <button class="modal-btn" onclick="aiCodeBlock.downloadCode()">
                                <i class="fas fa-download"></i> Download
                            </button>
                            <button class="modal-btn close-btn" onclick="aiCodeBlock.closeCodeReviewModal()">
                                <i class="fas fa-times"></i> Close
                            </button>
                        </div>
                    </div>

                    <div class="code-review-content">
                        <div class="code-editor-section">
                            <div class="section-header">
                                <h4><i class="fas fa-edit"></i> Code Editor</h4>
                                <div class="editor-controls">
                                    <button class="control-btn" onclick="aiCodeBlock.formatCode()">
                                        <i class="fas fa-magic"></i> Format
                                    </button>
                                    <button class="control-btn" onclick="aiCodeBlock.validateCode()">
                                        <i class="fas fa-check-circle"></i> Validate
                                    </button>
                                </div>
                            </div>
                            <textarea id="codeEditor" class="code-editor" spellcheck="false">${code}</textarea>
                            <div class="editor-info">
                                <span class="line-count">Lines: <span id="lineCount">${code.split('\n').length}</span></span>
                                <span class="char-count">Characters: <span id="charCount">${code.length}</span></span>
                                <span class="language-info">Language: <span id="currentLanguage">${language.toUpperCase()}</span></span>
                            </div>
                        </div>

                        <div class="code-preview-section">
                            <div class="section-header">
                                <h4><i class="fas fa-eye"></i> Live Preview</h4>
                                <div class="preview-controls">
                                    <button class="control-btn" onclick="aiCodeBlock.refreshPreview()">
                                        <i class="fas fa-sync"></i> Refresh
                                    </button>
                                    <button class="control-btn" onclick="aiCodeBlock.fullscreenPreview()">
                                        <i class="fas fa-expand"></i> Fullscreen
                                    </button>
                                </div>
                            </div>
                            <div id="codePreview" class="code-preview"></div>
                        </div>
                    </div>

                    <div class="code-review-footer">
                        <div class="code-analysis">
                            <h5><i class="fas fa-chart-line"></i> Code Analysis</h5>
                            <div id="codeAnalysis" class="analysis-results">
                                <div class="analysis-item">
                                    <span class="analysis-label">Complexity:</span>
                                    <span class="analysis-value" id="complexityScore">Calculating...</span>
                                </div>
                                <div class="analysis-item">
                                    <span class="analysis-label">Quality:</span>
                                    <span class="analysis-value" id="qualityScore">Analyzing...</span>
                                </div>
                                <div class="analysis-item">
                                    <span class="analysis-label">Best Practices:</span>
                                    <span class="analysis-value" id="bestPractices">Checking...</span>
                                </div>
                            </div>
                        </div>

                        <div class="action-buttons">
                            <button class="action-btn save-btn" onclick="aiCodeBlock.saveChanges('${originalCodeId}')">
                                <i class="fas fa-save"></i> Save Changes
                            </button>
                            <button class="action-btn reset-btn" onclick="aiCodeBlock.resetCode(\`${code.replace(/`/g, '\\`')}\`)">
                                <i class="fas fa-undo"></i> Reset
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Add modal to page
        document.body.insertAdjacentHTML('beforeend', modalHtml);

        // Add modal styles if not already added
        this.addModalStyles();

        // Initialize modal functionality
        this.initializeModal(code, language);

        // Show modal with animation
        setTimeout(() => {
            document.getElementById('code-review-modal').classList.add('show');
        }, 10);
    }

    // AI Response wrapper with code
    createAIResponse(message, code, language = 'javascript') {
        const codeBlock = this.createCodeBlock(code, language);
        
        return `
            <div class="ai-response">
                <div class="ai-message">
                    <i class="fas fa-robot"></i>
                    <span>${message}</span>
                </div>
                ${codeBlock}
            </div>
        `;
    }
}

// Global instance
const aiCodeBlock = new AICodeBlock();

    // Add modal styles
    addModalStyles() {
        if (document.getElementById('code-review-modal-styles')) return;

        const modalStyles = `
            <style id="code-review-modal-styles">
                .code-review-modal {
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    z-index: 10000;
                    opacity: 0;
                    visibility: hidden;
                    transition: all 0.3s ease;
                }

                .code-review-modal.show {
                    opacity: 1;
                    visibility: visible;
                }

                .code-review-overlay {
                    position: absolute;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background: rgba(0, 0, 0, 0.8);
                    backdrop-filter: blur(5px);
                }

                .code-review-container {
                    position: relative;
                    width: 95%;
                    max-width: 1400px;
                    height: 90%;
                    margin: 2.5% auto;
                    background: #1e1e1e;
                    border-radius: 12px;
                    border: 1px solid #333;
                    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
                    display: flex;
                    flex-direction: column;
                    overflow: hidden;
                }

                .code-review-header {
                    background: #2d2d2d;
                    padding: 16px 20px;
                    border-bottom: 1px solid #333;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                }

                .code-review-header h3 {
                    color: #fff;
                    margin: 0;
                    font-size: 1.2rem;
                    display: flex;
                    align-items: center;
                    gap: 10px;
                }

                .code-review-actions {
                    display: flex;
                    gap: 10px;
                    align-items: center;
                }

                .language-selector {
                    background: #333;
                    color: #fff;
                    border: 1px solid #555;
                    padding: 8px 12px;
                    border-radius: 6px;
                    font-size: 0.9rem;
                }

                .modal-btn, .control-btn, .action-btn {
                    background: rgba(255, 255, 255, 0.1);
                    border: 1px solid rgba(255, 255, 255, 0.2);
                    color: #fff;
                    padding: 8px 16px;
                    border-radius: 6px;
                    cursor: pointer;
                    font-size: 0.9rem;
                    transition: all 0.3s ease;
                    display: flex;
                    align-items: center;
                    gap: 6px;
                }

                .modal-btn:hover, .control-btn:hover, .action-btn:hover {
                    background: rgba(255, 255, 255, 0.2);
                    transform: translateY(-1px);
                }

                .close-btn {
                    background: #dc3545 !important;
                }

                .save-btn {
                    background: #28a745 !important;
                }

                .reset-btn {
                    background: #ffc107 !important;
                    color: #000 !important;
                }

                .code-review-content {
                    flex: 1;
                    display: grid;
                    grid-template-columns: 1fr 1fr;
                    gap: 1px;
                    background: #333;
                    overflow: hidden;
                }

                .code-editor-section, .code-preview-section {
                    background: #1e1e1e;
                    display: flex;
                    flex-direction: column;
                }

                .section-header {
                    background: #2a2a2a;
                    padding: 12px 16px;
                    border-bottom: 1px solid #333;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                }

                .section-header h4 {
                    color: #fff;
                    margin: 0;
                    font-size: 1rem;
                    display: flex;
                    align-items: center;
                    gap: 8px;
                }

                .editor-controls, .preview-controls {
                    display: flex;
                    gap: 8px;
                }

                .code-editor {
                    flex: 1;
                    background: #1e1e1e;
                    color: #d4d4d4;
                    border: none;
                    padding: 16px;
                    font-family: 'Fira Code', 'Consolas', 'Monaco', monospace;
                    font-size: 14px;
                    line-height: 1.5;
                    resize: none;
                    outline: none;
                    white-space: pre;
                    overflow-wrap: normal;
                    overflow-x: auto;
                }

                .editor-info {
                    background: #2a2a2a;
                    padding: 8px 16px;
                    border-top: 1px solid #333;
                    display: flex;
                    gap: 20px;
                    font-size: 0.8rem;
                    color: #999;
                }

                .code-preview {
                    flex: 1;
                    background: #1e1e1e;
                    padding: 16px;
                    overflow: auto;
                    font-family: 'Fira Code', 'Consolas', 'Monaco', monospace;
                    font-size: 14px;
                    line-height: 1.5;
                    color: #d4d4d4;
                }

                .code-review-footer {
                    background: #2d2d2d;
                    padding: 16px 20px;
                    border-top: 1px solid #333;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                }

                .code-analysis h5 {
                    color: #fff;
                    margin: 0 0 10px 0;
                    font-size: 1rem;
                    display: flex;
                    align-items: center;
                    gap: 8px;
                }

                .analysis-results {
                    display: flex;
                    gap: 20px;
                }

                .analysis-item {
                    display: flex;
                    flex-direction: column;
                    gap: 4px;
                }

                .analysis-label {
                    font-size: 0.8rem;
                    color: #999;
                }

                .analysis-value {
                    font-size: 0.9rem;
                    color: #4ecdc4;
                    font-weight: 600;
                }

                .action-buttons {
                    display: flex;
                    gap: 12px;
                }

                @media (max-width: 1024px) {
                    .code-review-container {
                        width: 98%;
                        height: 95%;
                        margin: 1% auto;
                    }

                    .code-review-content {
                        grid-template-columns: 1fr;
                        grid-template-rows: 1fr 1fr;
                    }

                    .analysis-results {
                        flex-direction: column;
                        gap: 8px;
                    }

                    .code-review-footer {
                        flex-direction: column;
                        gap: 16px;
                        align-items: flex-start;
                    }
                }
            </style>
        `;

        document.head.insertAdjacentHTML('beforeend', modalStyles);
    }

    // Initialize modal functionality
    initializeModal(code, language) {
        const editor = document.getElementById('codeEditor');
        const preview = document.getElementById('codePreview');
        const languageSelector = document.getElementById('languageSelector');

        // Update preview on editor change
        editor.addEventListener('input', () => {
            this.updateEditorInfo();
            this.updatePreview();
        });

        // Update language on selector change
        languageSelector.addEventListener('change', (e) => {
            const newLanguage = e.target.value;
            document.getElementById('currentLanguage').textContent = newLanguage.toUpperCase();
            this.updatePreview();
        });

        // Initial preview update
        this.updatePreview();
        this.updateEditorInfo();
        this.analyzeCode(code, language);
    }

    // Update editor info (line count, character count)
    updateEditorInfo() {
        const editor = document.getElementById('codeEditor');
        const code = editor.value;

        document.getElementById('lineCount').textContent = code.split('\n').length;
        document.getElementById('charCount').textContent = code.length;
    }

    // Update live preview
    updatePreview() {
        const editor = document.getElementById('codeEditor');
        const preview = document.getElementById('codePreview');
        const languageSelector = document.getElementById('languageSelector');

        const code = editor.value;
        const language = languageSelector.value;

        const highlightedCode = this.highlightSyntax(code, language);
        preview.innerHTML = `<pre><code>${highlightedCode}</code></pre>`;
    }

    // Analyze code quality and complexity
    analyzeCode(code, language) {
        setTimeout(() => {
            // Simple complexity analysis
            const lines = code.split('\n').length;
            const complexity = this.calculateComplexity(code, language);
            const quality = this.assessQuality(code, language);
            const bestPractices = this.checkBestPractices(code, language);

            document.getElementById('complexityScore').textContent = complexity;
            document.getElementById('qualityScore').textContent = quality;
            document.getElementById('bestPractices').textContent = bestPractices;
        }, 1000);
    }

    // Calculate code complexity
    calculateComplexity(code, language) {
        const lines = code.split('\n').length;
        const functions = (code.match(/function|def|class/g) || []).length;
        const conditions = (code.match(/if|else|while|for|switch/g) || []).length;

        if (lines < 20 && functions < 3 && conditions < 5) return 'Low';
        if (lines < 50 && functions < 8 && conditions < 15) return 'Medium';
        return 'High';
    }

    // Assess code quality
    assessQuality(code, language) {
        let score = 100;

        // Check for comments
        const commentRatio = (code.match(/\/\/|\/\*|\#/g) || []).length / code.split('\n').length;
        if (commentRatio < 0.1) score -= 20;

        // Check for proper indentation
        const properIndent = code.split('\n').every(line =>
            line.trim() === '' || line.match(/^(\s{2}|\s{4}|\t)/) || !line.match(/^\s/)
        );
        if (!properIndent) score -= 15;

        // Check for long lines
        const longLines = code.split('\n').filter(line => line.length > 100).length;
        if (longLines > 0) score -= 10;

        if (score >= 90) return 'Excellent';
        if (score >= 75) return 'Good';
        if (score >= 60) return 'Fair';
        return 'Needs Improvement';
    }

    // Check best practices
    checkBestPractices(code, language) {
        const issues = [];

        if (language === 'javascript') {
            if (code.includes('var ')) issues.push('Use let/const instead of var');
            if (code.includes('==') && !code.includes('===')) issues.push('Use === instead of ==');
            if (!code.includes('const') && !code.includes('let')) issues.push('Use modern variable declarations');
        }

        if (language === 'python') {
            if (!code.includes('def ') && code.length > 50) issues.push('Consider using functions');
            if (code.includes('\t') && code.includes('    ')) issues.push('Inconsistent indentation');
        }

        return issues.length === 0 ? 'All Good ✓' : `${issues.length} Issues Found`;
    }

    // Modal action methods
    copyModalCode() {
        const editor = document.getElementById('codeEditor');
        navigator.clipboard.writeText(editor.value).then(() => {
            this.showToast('Code copied to clipboard!', 'success');
        });
    }

    downloadCode() {
        const editor = document.getElementById('codeEditor');
        const languageSelector = document.getElementById('languageSelector');
        const code = editor.value;
        const language = languageSelector.value;

        const extensions = {
            javascript: 'js',
            python: 'py',
            html: 'html',
            css: 'css',
            sql: 'sql'
        };

        const filename = `code.${extensions[language] || 'txt'}`;
        const blob = new Blob([code], { type: 'text/plain' });
        const url = URL.createObjectURL(blob);

        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        a.click();

        URL.revokeObjectURL(url);
        this.showToast(`Code downloaded as ${filename}`, 'success');
    }

    formatCode() {
        const editor = document.getElementById('codeEditor');
        let code = editor.value;

        // Simple formatting (add proper indentation)
        const lines = code.split('\n');
        let indentLevel = 0;
        const formatted = lines.map(line => {
            const trimmed = line.trim();
            if (trimmed.includes('}') || trimmed.includes('</')) indentLevel = Math.max(0, indentLevel - 1);
            const result = '    '.repeat(indentLevel) + trimmed;
            if (trimmed.includes('{') || trimmed.includes('<') && !trimmed.includes('</')) indentLevel++;
            return result;
        }).join('\n');

        editor.value = formatted;
        this.updatePreview();
        this.updateEditorInfo();
        this.showToast('Code formatted!', 'success');
    }

    validateCode() {
        const editor = document.getElementById('codeEditor');
        const languageSelector = document.getElementById('languageSelector');
        const code = editor.value;
        const language = languageSelector.value;

        // Simple validation
        const errors = [];

        if (language === 'javascript') {
            // Check for basic syntax issues
            const openBraces = (code.match(/{/g) || []).length;
            const closeBraces = (code.match(/}/g) || []).length;
            if (openBraces !== closeBraces) errors.push('Mismatched braces');

            const openParens = (code.match(/\(/g) || []).length;
            const closeParens = (code.match(/\)/g) || []).length;
            if (openParens !== closeParens) errors.push('Mismatched parentheses');
        }

        if (errors.length === 0) {
            this.showToast('Code validation passed!', 'success');
        } else {
            this.showToast(`Validation errors: ${errors.join(', ')}`, 'error');
        }
    }

    refreshPreview() {
        this.updatePreview();
        this.showToast('Preview refreshed!', 'info');
    }

    fullscreenPreview() {
        const preview = document.getElementById('codePreview');
        if (preview.requestFullscreen) {
            preview.requestFullscreen();
        }
    }

    saveChanges(originalCodeId) {
        const editor = document.getElementById('codeEditor');
        const newCode = editor.value;

        // Update original code block
        const originalElement = document.getElementById(originalCodeId);
        if (originalElement) {
            const languageSelector = document.getElementById('languageSelector');
            const language = languageSelector.value;
            const highlightedCode = this.highlightSyntax(newCode, language);
            originalElement.innerHTML = `<code>${highlightedCode}</code>`;
        }

        this.showToast('Changes saved successfully!', 'success');
        this.closeCodeReviewModal();
    }

    resetCode(originalCode) {
        const editor = document.getElementById('codeEditor');
        editor.value = originalCode;
        this.updatePreview();
        this.updateEditorInfo();
        this.showToast('Code reset to original!', 'info');
    }

    closeCodeReviewModal() {
        const modal = document.getElementById('code-review-modal');
        if (modal) {
            modal.classList.remove('show');
            setTimeout(() => {
                modal.remove();
            }, 300);
        }
    }

    // Show toast notification
    showToast(message, type = 'info') {
        const toast = document.createElement('div');
        toast.className = `toast toast-${type}`;
        toast.innerHTML = `
            <i class="fas fa-${type === 'success' ? 'check' : type === 'error' ? 'exclamation' : 'info'}"></i>
            ${message}
        `;

        // Add toast styles if not already added
        if (!document.getElementById('toast-styles')) {
            const toastStyles = `
                <style id="toast-styles">
                    .toast {
                        position: fixed;
                        top: 20px;
                        right: 20px;
                        background: #333;
                        color: white;
                        padding: 12px 20px;
                        border-radius: 6px;
                        z-index: 10001;
                        display: flex;
                        align-items: center;
                        gap: 10px;
                        animation: slideIn 0.3s ease;
                    }
                    .toast-success { border-left: 4px solid #28a745; }
                    .toast-error { border-left: 4px solid #dc3545; }
                    .toast-info { border-left: 4px solid #17a2b8; }
                    @keyframes slideIn {
                        from { transform: translateX(100%); opacity: 0; }
                        to { transform: translateX(0); opacity: 1; }
                    }
                </style>
            `;
            document.head.insertAdjacentHTML('beforeend', toastStyles);
        }

        document.body.appendChild(toast);

        setTimeout(() => {
            toast.style.animation = 'slideIn 0.3s ease reverse';
            setTimeout(() => toast.remove(), 300);
        }, 3000);
    }
}

// Global instance
const aiCodeBlock = new AICodeBlock();

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AICodeBlock;
}
