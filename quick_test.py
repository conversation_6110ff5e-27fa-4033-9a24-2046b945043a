#!/usr/bin/env python3
"""
Quick test of Flask API with OpenAI
"""

import requests
import json

def test_chat():
    """Test chat endpoint"""
    print("🧪 Testing chat API with OpenAI...")
    
    try:
        response = requests.post('http://localhost:5001/api/chat', 
            json={
                "message": "Maxaad ka fikirtaa AI-ga?",
                "user_id": "test_user_123"
            },
            timeout=30
        )
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Chat API working!")
            print(f"   Source: {data.get('source', 'unknown')}")
            print(f"   Model: {data.get('model', 'unknown')}")
            print(f"   Tokens: {data.get('tokens_used', 0)}")
            print(f"   Response: {data['response'][:200]}...")
            
            if data.get('source') == 'openai':
                print("🎉 Real OpenAI API responses working!")
            else:
                print("⚠️  Still using fallback responses")
                
        else:
            print(f"❌ Chat API failed: {response.status_code}")
            print(f"   Error: {response.text}")
            
    except Exception as e:
        print(f"❌ Error: {e}")

def test_health():
    """Test health endpoint"""
    print("\n🔧 Testing health endpoint...")
    
    try:
        response = requests.get('http://localhost:5001/api/health', timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Health check passed")
            print(f"   Status: {data['status']}")
            print(f"   OpenAI Available: {data['openai_available']}")
        else:
            print(f"❌ Health check failed: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Health check error: {e}")

if __name__ == "__main__":
    print("🚀 Quick API Test")
    print("=" * 30)
    
    test_health()
    test_chat()
    
    print("\n" + "=" * 30)
    print("✅ Test completed!")
    print("\n💡 If you see 'Real OpenAI API responses working!', then:")
    print("   - Your API key is configured correctly")
    print("   - Chat will use real OpenAI responses")
    print("   - No more fallback mode messages")
    print("\n🌐 Open dashboard: http://localhost:5001")
