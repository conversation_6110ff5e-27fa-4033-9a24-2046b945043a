// Tincada AI - Login System
class LoginSystem {
    constructor() {
        this.currentUser = null;
        this.profileImageData = null;
        this.init();
    }

    init() {
        this.bindEvents();
        this.checkExistingLogin();
    }

    bindEvents() {
        // Form submission
        document.getElementById('loginForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleLogin();
        });

        // Profile image upload
        document.getElementById('profileImage').addEventListener('change', (e) => {
            this.handleImageUpload(e.target.files[0]);
        });

        // Password toggle
        document.getElementById('passwordToggle').addEventListener('click', () => {
            this.togglePassword();
        });

        // Phone number formatting
        document.getElementById('phoneNumber').addEventListener('input', (e) => {
            this.formatPhoneNumber(e.target);
        });

        // Register link
        document.getElementById('registerLink').addEventListener('click', (e) => {
            e.preventDefault();
            this.toggleRegisterMode();
        });
    }

    handleImageUpload(file) {
        if (!file) return;

        // Validate file type
        if (!file.type.startsWith('image/')) {
            this.showError('Fadlan dooro sawir kaliya (JPG, PNG, GIF)');
            return;
        }

        // Validate file size (max 5MB)
        if (file.size > 5 * 1024 * 1024) {
            this.showError('Sawirka waa ka weyn yahay 5MB. Fadlan dooro sawir yar');
            return;
        }

        const reader = new FileReader();
        reader.onload = (e) => {
            this.profileImageData = e.target.result;
            this.updateProfilePreview(e.target.result);
        };
        reader.readAsDataURL(file);
    }

    updateProfilePreview(imageSrc) {
        const preview = document.getElementById('profilePreview');
        preview.innerHTML = `<img src="${imageSrc}" alt="Profile">`;
    }

    formatPhoneNumber(input) {
        let value = input.value.replace(/\D/g, '');
        
        // Add Somalia country code if not present
        if (value.length > 0 && !value.startsWith('252')) {
            if (value.startsWith('0')) {
                value = '252' + value.substring(1);
            } else if (value.length === 9) {
                value = '252' + value;
            }
        }

        // Format: +252 61 234 5678
        if (value.length >= 3) {
            let formatted = '+252';
            if (value.length > 3) {
                formatted += ' ' + value.substring(3, 5);
            }
            if (value.length > 5) {
                formatted += ' ' + value.substring(5, 8);
            }
            if (value.length > 8) {
                formatted += ' ' + value.substring(8, 12);
            }
            input.value = formatted;
        }
    }

    togglePassword() {
        const passwordInput = document.getElementById('password');
        const toggleIcon = document.getElementById('passwordToggle');
        
        if (passwordInput.type === 'password') {
            passwordInput.type = 'text';
            toggleIcon.className = 'fas fa-eye-slash password-toggle';
        } else {
            passwordInput.type = 'password';
            toggleIcon.className = 'fas fa-eye password-toggle';
        }
    }

    validateForm() {
        const fullName = document.getElementById('fullName').value.trim();
        const phoneNumber = document.getElementById('phoneNumber').value.trim();
        const password = document.getElementById('password').value;

        if (!fullName) {
            this.showError('Fadlan geli magacaaga buuxa');
            return false;
        }

        if (fullName.length < 2) {
            this.showError('Magaca waa in uu ka kooban yahay ugu yaraan 2 xaraf');
            return false;
        }

        if (!phoneNumber) {
            this.showError('Fadlan geli lambarka telefoonkaaga');
            return false;
        }

        if (!phoneNumber.match(/^\+252\s\d{2}\s\d{3}\s\d{4}$/)) {
            this.showError('Lambarka telefoonka ma sax aha. Tusaale: +252 61 234 5678');
            return false;
        }

        if (!password) {
            this.showError('Fadlan geli furaha sirta ah');
            return false;
        }

        if (password.length < 6) {
            this.showError('Furaha sirta ah waa in uu ka kooban yahay ugu yaraan 6 xaraf');
            return false;
        }

        if (!this.profileImageData) {
            this.showError('Fadlan dooro sawirkaaga');
            return false;
        }

        return true;
    }

    async handleLogin() {
        if (!this.validateForm()) return;

        // Check login attempts
        const attemptCheck = authManager.trackLoginAttempt(false);
        if (!attemptCheck.allowed) {
            const nextAttempt = attemptCheck.nextAttemptTime;
            const waitTime = Math.ceil((nextAttempt - new Date()) / (1000 * 60));
            this.showError(`Aad bay u badan tahay isku dayga. Fadlan sug ${waitTime} daqiiqo.`);
            return;
        }

        const loginBtn = document.getElementById('loginBtn');
        loginBtn.disabled = true;
        loginBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Soo galaya...';

        try {
            // Simulate API call delay
            await new Promise(resolve => setTimeout(resolve, 1500));

            const userData = {
                fullName: document.getElementById('fullName').value.trim(),
                phoneNumber: document.getElementById('phoneNumber').value.trim(),
                password: document.getElementById('password').value,
                profileImage: this.profileImageData,
                loginTime: new Date().toISOString(),
                userId: authManager.generateUserId()
            };

            // Validate user data
            const validation = authManager.validateUserData(userData);
            if (!validation.isValid) {
                this.showError(validation.errors[0]);
                authManager.trackLoginAttempt(false);
                return;
            }

            // Login using AuthManager
            const loginSuccess = authManager.login(userData);
            if (loginSuccess) {
                authManager.trackLoginAttempt(true);
                this.showSuccess('Guul! Waa la soo galay...');

                // Redirect to dashboard after success message
                setTimeout(() => {
                    window.location.href = 'dashboard.html';
                }, 1500);
            } else {
                throw new Error('Login failed');
            }

        } catch (error) {
            console.error('Login error:', error);
            authManager.trackLoginAttempt(false);
            this.showError('Khalad ayaa dhacay. Fadlan mar kale isku day.');
        } finally {
            loginBtn.disabled = false;
            loginBtn.innerHTML = '<i class="fas fa-sign-in-alt"></i> Soo Gal';
        }
    }

    generateUserId() {
        return 'user_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    checkExistingLogin() {
        if (authManager.isAuthenticated() && authManager.isSessionValid()) {
            // User is already logged in with valid session, redirect to dashboard
            window.location.href = 'dashboard.html';
        }
    }

    toggleRegisterMode() {
        const form = document.getElementById('loginForm');
        const title = document.querySelector('.logo-subtitle');
        const button = document.getElementById('loginBtn');
        const footer = document.querySelector('.form-footer');

        if (title.textContent === 'Soo gal akoonkaaga') {
            // Switch to register mode
            title.textContent = 'Samee akoon cusub';
            button.innerHTML = '<i class="fas fa-user-plus"></i> Samee Akoon';
            footer.innerHTML = '<p>Akoon ma leedahay? <a href="#" id="loginLink">Soo gal</a></p>';
            
            // Re-bind the login link
            document.getElementById('loginLink').addEventListener('click', (e) => {
                e.preventDefault();
                this.toggleRegisterMode();
            });
        } else {
            // Switch to login mode
            title.textContent = 'Soo gal akoonkaaga';
            button.innerHTML = '<i class="fas fa-sign-in-alt"></i> Soo Gal';
            footer.innerHTML = '<p>Akoon ma lihid? <a href="#" id="registerLink">Samee akoon cusub</a></p>';
            
            // Re-bind the register link
            document.getElementById('registerLink').addEventListener('click', (e) => {
                e.preventDefault();
                this.toggleRegisterMode();
            });
        }
    }

    showError(message) {
        const errorDiv = document.getElementById('errorMessage');
        const successDiv = document.getElementById('successMessage');
        
        successDiv.style.display = 'none';
        errorDiv.textContent = message;
        errorDiv.style.display = 'block';
        
        // Auto hide after 5 seconds
        setTimeout(() => {
            errorDiv.style.display = 'none';
        }, 5000);
    }

    showSuccess(message) {
        const errorDiv = document.getElementById('errorMessage');
        const successDiv = document.getElementById('successMessage');
        
        errorDiv.style.display = 'none';
        successDiv.textContent = message;
        successDiv.style.display = 'block';
        
        // Auto hide after 3 seconds
        setTimeout(() => {
            successDiv.style.display = 'none';
        }, 3000);
    }
}

// Initialize login system when page loads
document.addEventListener('DOMContentLoaded', () => {
    new LoginSystem();
});
