<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test API Connection</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background: #1a1a1a;
            color: white;
        }
        .container {
            background: #2a2a2a;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.3);
        }
        input, button {
            width: 100%;
            padding: 15px;
            margin: 10px 0;
            border: 1px solid #444;
            border-radius: 5px;
            background: #333;
            color: white;
            font-size: 16px;
        }
        button {
            background: #007bff;
            cursor: pointer;
        }
        button:hover {
            background: #0056b3;
        }
        .response {
            background: #333;
            padding: 15px;
            border-radius: 5px;
            margin-top: 20px;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .success { background: #28a745; }
        .error { background: #dc3545; }
        .loading { background: #ffc107; color: black; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Test API Connection</h1>
        <p>Test OpenAI API connection oo hubinno in AI-gu jawaabi karo</p>
        
        <input type="text" id="testMessage" placeholder="Geli su'aal (e.g., 'Hello, how are you?')" value="Hello, test API connection">
        <button onclick="testAPI()">🚀 Test API</button>
        
        <div id="status"></div>
        <div id="response" class="response" style="display: none;"></div>
    </div>

    <script>
        const API_KEY = '********************************************************************************************************************************************************************';
        
        function showStatus(message, type) {
            const status = document.getElementById('status');
            status.className = `status ${type}`;
            status.textContent = message;
        }
        
        function showResponse(text) {
            const response = document.getElementById('response');
            response.style.display = 'block';
            response.textContent = text;
        }
        
        async function testAPI() {
            const message = document.getElementById('testMessage').value.trim();
            if (!message) {
                showStatus('❌ Fadlan geli su\'aal', 'error');
                return;
            }
            
            showStatus('⏳ Testing API connection...', 'loading');
            document.getElementById('response').style.display = 'none';
            
            try {
                // Test 1: Direct OpenAI API
                console.log('🔄 Testing direct OpenAI API...');
                
                const response = await fetch('https://api.openai.com/v1/chat/completions', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${API_KEY}`
                    },
                    body: JSON.stringify({
                        model: 'gpt-4o-mini',
                        messages: [
                            {
                                role: 'system',
                                content: 'You are a helpful AI assistant. Respond in the same language as the user\'s question.'
                            },
                            {
                                role: 'user',
                                content: message
                            }
                        ],
                        max_tokens: 500,
                        temperature: 0.7
                    })
                });
                
                if (!response.ok) {
                    throw new Error(`API Error: ${response.status} - ${response.statusText}`);
                }
                
                const data = await response.json();
                
                if (data.choices && data.choices[0] && data.choices[0].message) {
                    const aiResponse = data.choices[0].message.content;
                    showStatus('✅ API connection successful!', 'success');
                    showResponse(`AI Response:\n\n${aiResponse}\n\n---\nModel: ${data.model}\nTokens used: ${data.usage?.total_tokens || 'N/A'}`);
                } else {
                    throw new Error('Invalid response format from API');
                }
                
            } catch (error) {
                console.error('❌ API Test Error:', error);
                showStatus(`❌ API Error: ${error.message}`, 'error');
                showResponse(`Error Details:\n${error.message}\n\nPlease check:\n1. API key validity\n2. Internet connection\n3. OpenAI service status`);
            }
        }
        
        // Test on page load
        window.addEventListener('load', () => {
            showStatus('🔧 Ready to test API connection', 'loading');
        });
        
        // Allow Enter key to submit
        document.getElementById('testMessage').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                testAPI();
            }
        });
    </script>
</body>
</html>
