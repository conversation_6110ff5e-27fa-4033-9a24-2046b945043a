<!DOCTYPE html>
<html lang="so">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OAuth Test - Tincada AI</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
            padding: 2rem;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 3rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        h1 {
            text-align: center;
            font-size: 2.5rem;
            margin-bottom: 1rem;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .test-section {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .test-section h2 {
            color: #4ecdc4;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .oauth-buttons {
            display: flex;
            flex-direction: column;
            gap: 1rem;
            max-width: 400px;
            margin: 2rem auto;
        }

        .google-btn, .apple-btn {
            padding: 15px;
            border-radius: 10px;
            font-size: 1rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 12px;
            border: none;
        }

        .google-btn {
            background: #ffffff;
            color: #333;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .google-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
        }

        .apple-btn {
            background: #000000;
            color: #ffffff;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
        }

        .apple-btn:hover {
            transform: translateY(-2px);
            background: #1a1a1a;
        }

        .status-display {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 10px;
            padding: 1.5rem;
            margin-top: 2rem;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.5rem 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .status-item:last-child {
            border-bottom: none;
        }

        .status-label {
            color: #cccccc;
            font-weight: 500;
        }

        .status-value {
            color: #4ecdc4;
            font-family: monospace;
        }

        .user-info {
            background: rgba(76, 175, 80, 0.2);
            border: 1px solid rgba(76, 175, 80, 0.3);
            border-radius: 10px;
            padding: 1.5rem;
            margin-top: 1rem;
            display: none;
        }

        .user-info.show {
            display: block;
        }

        .user-avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            margin-right: 1rem;
            border: 2px solid #4caf50;
        }

        .user-details {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .logout-btn {
            background: linear-gradient(45deg, #ff6b6b, #ee5a52);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .logout-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(255, 107, 107, 0.4);
        }

        .log-display {
            background: rgba(0, 0, 0, 0.5);
            border-radius: 10px;
            padding: 1rem;
            margin-top: 1rem;
            max-height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 0.9rem;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .log-entry {
            margin-bottom: 0.5rem;
            padding: 0.25rem;
            border-radius: 4px;
        }

        .log-success {
            background: rgba(76, 175, 80, 0.2);
            color: #4caf50;
        }

        .log-error {
            background: rgba(244, 67, 54, 0.2);
            color: #f44336;
        }

        .log-info {
            background: rgba(33, 150, 243, 0.2);
            color: #2196f3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 OAuth Testing</h1>
        
        <div class="test-section">
            <h2>🧪 Social Login Test</h2>
            <p>Test real Google iyo Apple OAuth integration:</p>
            
            <div class="oauth-buttons">
                <button class="google-btn" id="testGoogleBtn">
                    <i class="fab fa-google"></i>
                    <span>Test Google Sign In</span>
                </button>
                
                <button class="apple-btn" id="testAppleBtn">
                    <i class="fab fa-apple"></i>
                    <span>Test Apple Sign In</span>
                </button>
            </div>
        </div>
        
        <div class="test-section">
            <h2>📊 OAuth Status</h2>
            <div class="status-display">
                <div class="status-item">
                    <span class="status-label">Google SDK:</span>
                    <span class="status-value" id="googleStatus">Loading...</span>
                </div>
                <div class="status-item">
                    <span class="status-label">Apple SDK:</span>
                    <span class="status-value" id="appleStatus">Loading...</span>
                </div>
                <div class="status-item">
                    <span class="status-label">Client ID:</span>
                    <span class="status-value">************-shjvho7kcqn6vpjk1scrijfr88cg4g1n.apps.googleusercontent.com</span>
                </div>
                <div class="status-item">
                    <span class="status-label">Current User:</span>
                    <span class="status-value" id="currentUserStatus">None</span>
                </div>
            </div>
            
            <div class="user-info" id="userInfo">
                <h3>👤 Logged In User</h3>
                <div class="user-details" id="userDetails">
                    <!-- User info will be populated here -->
                </div>
                <button class="logout-btn" onclick="testLogout()">
                    <i class="fas fa-sign-out-alt"></i> Sign Out
                </button>
            </div>
        </div>
        
        <div class="test-section">
            <h2>📝 Activity Log</h2>
            <div class="log-display" id="logDisplay">
                <div class="log-entry log-info">🚀 OAuth test page loaded</div>
            </div>
        </div>
    </div>

    <!-- Google Sign In SDK -->
    <script src="https://accounts.google.com/gsi/client" async defer></script>
    
    <!-- Apple Sign In SDK -->
    <script type="text/javascript" src="https://appleid.cdn-apple.com/appleauth/static/jsapi/appleid/1/en_US/appleid.auth.js"></script>
    
    <script>
        // Logging function
        function addLog(message, type = 'info') {
            const logDisplay = document.getElementById('logDisplay');
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry log-${type}`;
            logEntry.textContent = `${new Date().toLocaleTimeString()} - ${message}`;
            logDisplay.appendChild(logEntry);
            logDisplay.scrollTop = logDisplay.scrollHeight;
        }

        // Check SDK status
        function checkSDKStatus() {
            // Check Google SDK
            if (typeof google !== 'undefined' && google.accounts) {
                document.getElementById('googleStatus').textContent = '✅ Loaded';
                addLog('Google SDK loaded successfully', 'success');
            } else {
                document.getElementById('googleStatus').textContent = '❌ Not loaded';
                addLog('Google SDK not loaded', 'error');
            }

            // Check Apple SDK
            if (typeof AppleID !== 'undefined') {
                document.getElementById('appleStatus').textContent = '✅ Loaded';
                addLog('Apple SDK loaded successfully', 'success');
            } else {
                document.getElementById('appleStatus').textContent = '❌ Not loaded';
                addLog('Apple SDK not loaded', 'error');
            }
        }

        // Initialize Google OAuth
        function initGoogle() {
            if (typeof google !== 'undefined' && google.accounts) {
                google.accounts.id.initialize({
                    client_id: '************-shjvho7kcqn6vpjk1scrijfr88cg4g1n.apps.googleusercontent.com',
                    callback: handleGoogleResponse
                });
                addLog('Google OAuth initialized', 'success');
            }
        }

        // Handle Google response
        function handleGoogleResponse(response) {
            try {
                const payload = JSON.parse(atob(response.credential.split('.')[1]));
                addLog(`Google login successful: ${payload.name}`, 'success');
                
                displayUserInfo({
                    name: payload.name,
                    email: payload.email,
                    picture: payload.picture,
                    provider: 'Google'
                });
                
            } catch (error) {
                addLog(`Google login error: ${error.message}`, 'error');
            }
        }

        // Test Google Sign In
        document.getElementById('testGoogleBtn').addEventListener('click', function() {
            addLog('Testing Google Sign In...', 'info');
            this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Testing...';
            
            if (typeof google !== 'undefined' && google.accounts) {
                google.accounts.id.prompt();
            } else {
                addLog('Google SDK not available', 'error');
                this.innerHTML = '<i class="fab fa-google"></i> Test Google Sign In';
            }
        });

        // Test Apple Sign In
        document.getElementById('testAppleBtn').addEventListener('click', function() {
            addLog('Testing Apple Sign In...', 'info');
            this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Testing...';
            
            if (typeof AppleID !== 'undefined') {
                AppleID.auth.signIn().then((response) => {
                    const { authorization, user } = response;
                    const userName = user?.name ? `${user.name.firstName} ${user.name.lastName}` : 'Apple User';
                    
                    addLog(`Apple login successful: ${userName}`, 'success');
                    
                    displayUserInfo({
                        name: userName,
                        email: user?.email || '<EMAIL>',
                        provider: 'Apple'
                    });
                    
                }).catch((error) => {
                    addLog(`Apple login error: ${error.message}`, 'error');
                }).finally(() => {
                    this.innerHTML = '<i class="fab fa-apple"></i> Test Apple Sign In';
                });
            } else {
                addLog('Apple SDK not available', 'error');
                this.innerHTML = '<i class="fab fa-apple"></i> Test Apple Sign In';
            }
        });

        // Display user info
        function displayUserInfo(user) {
            const userInfo = document.getElementById('userInfo');
            const userDetails = document.getElementById('userDetails');
            const currentUserStatus = document.getElementById('currentUserStatus');
            
            userDetails.innerHTML = `
                ${user.picture ? `<img src="${user.picture}" alt="Avatar" class="user-avatar">` : ''}
                <div>
                    <h4>${user.name}</h4>
                    <p>${user.email}</p>
                    <small>Provider: ${user.provider}</small>
                </div>
            `;
            
            currentUserStatus.textContent = user.name;
            userInfo.classList.add('show');
            
            // Store user info
            localStorage.setItem('tincada_user', JSON.stringify(user));
        }

        // Test logout
        function testLogout() {
            addLog('User signed out', 'info');
            document.getElementById('userInfo').classList.remove('show');
            document.getElementById('currentUserStatus').textContent = 'None';
            localStorage.removeItem('tincada_user');
        }

        // Check for existing user
        function checkExistingUser() {
            const existingUser = localStorage.getItem('tincada_user');
            if (existingUser) {
                try {
                    const user = JSON.parse(existingUser);
                    displayUserInfo(user);
                    addLog(`Existing user found: ${user.name}`, 'info');
                } catch (error) {
                    addLog('Error loading existing user', 'error');
                }
            }
        }

        // Initialize when page loads
        window.addEventListener('load', function() {
            addLog('Initializing OAuth test...', 'info');
            
            setTimeout(() => {
                checkSDKStatus();
                initGoogle();
                checkExistingUser();
            }, 1000);
            
            // Check SDK status every 2 seconds
            setInterval(checkSDKStatus, 2000);
        });
    </script>
</body>
</html>
