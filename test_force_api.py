#!/usr/bin/env python3
"""
Test forcing OpenAI API usage with different approaches
"""

from openai import OpenAI
import requests
import json

def test_all_approaches():
    """Test all OpenAI API approaches"""
    
    api_key = "********************************************************************************************************************************************************************"
    
    print("🧪 Testing All OpenAI API Approaches")
    print("=" * 60)
    
    # Approach 1: Standard OpenAI Client with gpt-4o-mini
    print("\n1️⃣ Testing Standard OpenAI Client (gpt-4o-mini)...")
    try:
        client = OpenAI(api_key=api_key)
        response = client.chat.completions.create(
            model='gpt-4o-mini',
            messages=[
                {"role": "user", "content": "Say hello in one word"}
            ],
            max_tokens=50
        )
        print(f"✅ Success: {response.choices[0].message.content}")
    except Exception as e:
        print(f"❌ Failed: {e}")
    
    # Approach 2: Direct HTTP Request
    print("\n2️⃣ Testing Direct HTTP Request...")
    try:
        headers = {
            'Authorization': f'Bearer {api_key}',
            'Content-Type': 'application/json'
        }
        
        data = {
            'model': 'gpt-4o-mini',
            'messages': [{"role": "user", "content": "Say hello in one word"}],
            'max_tokens': 50
        }
        
        response = requests.post(
            'https://api.openai.com/v1/chat/completions',
            headers=headers,
            json=data,
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Success: {result['choices'][0]['message']['content']}")
        else:
            print(f"❌ HTTP Error: {response.status_code} - {response.text}")
    except Exception as e:
        print(f"❌ Failed: {e}")
    
    # Approach 3: Try gpt-3.5-turbo
    print("\n3️⃣ Testing Alternative Model (gpt-3.5-turbo)...")
    try:
        client = OpenAI(api_key=api_key)
        response = client.chat.completions.create(
            model='gpt-3.5-turbo',
            messages=[
                {"role": "user", "content": "Say hello in one word"}
            ],
            max_tokens=50
        )
        print(f"✅ Success: {response.choices[0].message.content}")
    except Exception as e:
        print(f"❌ Failed: {e}")
    
    # Approach 4: Try different organization/project
    print("\n4️⃣ Testing with minimal request...")
    try:
        client = OpenAI(api_key=api_key)
        response = client.chat.completions.create(
            model='gpt-4o-mini',
            messages=[{"role": "user", "content": "Hi"}],
            max_tokens=10,
            temperature=0
        )
        print(f"✅ Success: {response.choices[0].message.content}")
    except Exception as e:
        print(f"❌ Failed: {e}")

def test_tincada_forced_api():
    """Test Tincada AI with forced API usage"""
    
    print("\n🧪 Testing Tincada AI Forced API")
    print("=" * 60)
    
    try:
        response = requests.post("http://localhost:5001/api/chat", json={
            "message": "Hello, what is 2+2?",
            "user_id": "test_user",
            "chat_history": []
        })
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Tincada Success: {data.get('response', 'No response')[:100]}...")
            print(f"📊 Source: {data.get('source', 'unknown')}")
        else:
            print(f"❌ Tincada Error: {response.status_code}")
            print(f"Response: {response.text}")
            
    except Exception as e:
        print(f"❌ Tincada Request failed: {e}")

if __name__ == "__main__":
    test_all_approaches()
    test_tincada_forced_api()
    
    print("\n" + "=" * 60)
    print("📋 If any approach succeeds, we can use that one!")
    print("📋 If all fail, API key needs proper permissions.")
