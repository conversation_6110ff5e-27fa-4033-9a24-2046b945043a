<!DOCTYPE html>
<html lang="so">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Updated AI Tools Demo - Tincada AI</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
            padding: 2rem;
            margin: 0;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 3rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        h1 {
            text-align: center;
            font-size: 2.5rem;
            margin-bottom: 1rem;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .subtitle {
            text-align: center;
            font-size: 1.2rem;
            margin-bottom: 3rem;
            opacity: 0.9;
        }
        
        .tools-preview {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            border-left: 4px solid #4ecdc4;
        }
        
        .tools-preview h2 {
            color: #4ecdc4;
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .tools-grid-demo {
            display: flex;
            flex-direction: column;
            gap: 1rem;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 12px;
            padding: 1.5rem;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .tool-item-demo {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 1rem;
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .tool-item-demo:hover {
            background: rgba(255, 255, 255, 0.1);
            border-color: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
        }
        
        .tool-item-demo::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 4px;
            height: 100%;
            background: linear-gradient(45deg, #10a37f, #0d8f6f);
            transform: scaleY(0);
            transition: transform 0.3s ease;
        }
        
        .tool-item-demo:hover::before {
            transform: scaleY(1);
        }
        
        .tool-icon-demo {
            width: 50px;
            height: 50px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            flex-shrink: 0;
            transition: all 0.3s ease;
        }
        
        .tool-item-demo:hover .tool-icon-demo {
            background: rgba(16, 163, 127, 0.2);
            transform: scale(1.1);
        }
        
        .tool-content-demo {
            flex: 1;
        }
        
        .tool-content-demo h4 {
            color: #ffffff;
            font-size: 1rem;
            margin-bottom: 0.25rem;
            font-weight: 600;
        }
        
        .tool-content-demo p {
            color: #cccccc;
            font-size: 0.8rem;
            margin: 0;
            line-height: 1.3;
        }
        
        .next-button-demo {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 12px;
            padding: 2rem;
            margin: 2rem 0;
            text-align: center;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .next-btn {
            background: linear-gradient(45deg, #10a37f, #0d8f6f);
            color: white;
            border: none;
            border-radius: 8px;
            padding: 12px 24px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(16, 163, 127, 0.3);
        }
        
        .next-btn:hover {
            background: linear-gradient(45deg, #0d8f6f, #0a7a5e);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(16, 163, 127, 0.4);
        }
        
        .image-generation-demo {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border-radius: 12px;
            padding: 2rem;
            margin: 2rem 0;
            text-align: center;
        }
        
        .image-placeholder {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            padding: 3rem;
            border: 2px dashed rgba(255, 255, 255, 0.3);
            margin: 1rem 0;
        }
        
        .image-placeholder-icon {
            font-size: 4rem;
            margin-bottom: 1rem;
        }
        
        .image-actions {
            display: flex;
            gap: 10px;
            justify-content: center;
            flex-wrap: wrap;
            margin-top: 1rem;
        }
        
        .image-btn {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.9rem;
            transition: all 0.3s ease;
        }
        
        .image-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-1px);
        }
        
        .success {
            background: rgba(76, 175, 80, 0.2);
            border: 1px solid rgba(76, 175, 80, 0.3);
            border-radius: 10px;
            padding: 1.5rem;
            margin: 2rem 0;
            border-left: 4px solid #4caf50;
            text-align: center;
        }
        
        .demo-buttons {
            display: flex;
            gap: 20px;
            justify-content: center;
            margin-top: 3rem;
            flex-wrap: wrap;
        }
        
        .demo-btn {
            background: linear-gradient(45deg, #4ecdc4, #44a08d);
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 25px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 10px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(78, 205, 196, 0.3);
        }
        
        .demo-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(78, 205, 196, 0.4);
        }
        
        .demo-btn.secondary {
            background: linear-gradient(45deg, #ff6b6b, #ee5a52);
            box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
        }
        
        .demo-btn.secondary:hover {
            box-shadow: 0 6px 20px rgba(255, 107, 107, 0.4);
        }
        
        @media (max-width: 768px) {
            body { padding: 1rem; }
            .container { padding: 2rem 1rem; }
            h1 { font-size: 2rem; }
            .demo-buttons { flex-direction: column; align-items: center; }
            .image-actions { flex-direction: column; align-items: center; }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🛠️ Updated AI Tools</h1>
        <p class="subtitle">ChatGPT-style design oo leh emoji icons iyo "Next" buttons</p>
        
        <div class="tools-preview">
            <h2>🎨 New Tools Design</h2>
            <p>Tools waxay hadda u eegayaan sida ChatGPT oo leh emoji icons iyo clean design:</p>
            
            <div class="tools-grid-demo">
                <div class="tool-item-demo">
                    <div class="tool-icon-demo">💡</div>
                    <div class="tool-content-demo">
                        <h4>Think longer</h4>
                        <p>Extended reasoning iyo deep analysis for complex problems</p>
                    </div>
                </div>
                
                <div class="tool-item-demo">
                    <div class="tool-icon-demo">🔍</div>
                    <div class="tool-content-demo">
                        <h4>Deep research</h4>
                        <p>Comprehensive research oo leh multiple sources iyo citations</p>
                    </div>
                </div>
                
                <div class="tool-item-demo">
                    <div class="tool-icon-demo">🎨</div>
                    <div class="tool-content-demo">
                        <h4>Create image</h4>
                        <p>AI-powered image generation from text descriptions</p>
                    </div>
                </div>
                
                <div class="tool-item-demo">
                    <div class="tool-icon-demo">🌐</div>
                    <div class="tool-content-demo">
                        <h4>Web search</h4>
                        <p>Real-time internet search oo leh current information</p>
                    </div>
                </div>
                
                <div class="tool-item-demo">
                    <div class="tool-icon-demo">✏️</div>
                    <div class="tool-content-demo">
                        <h4>Canvas</h4>
                        <p>Interactive workspace for visual collaboration iyo editing</p>
                    </div>
                </div>
                
                <div class="tool-item-demo">
                    <div class="tool-icon-demo">🛠️</div>
                    <div class="tool-content-demo">
                        <h4>Tools</h4>
                        <p>Close tools menu and return to chat</p>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="next-button-demo">
            <h3 style="color: #ffffff; margin-bottom: 1rem;">➡️ Next Button</h3>
            <p style="color: #cccccc; margin-bottom: 1.5rem;">Dhammaan tools-yada waxay hadda isticmaalaan "Next" button instead of specific action text:</p>
            <button class="next-btn">
                ➡️ Next
            </button>
        </div>
        
        <div class="image-generation-demo">
            <h3 style="color: white; margin-bottom: 1rem;">🎨 Enhanced Image Generation</h3>
            <p style="color: rgba(255,255,255,0.8); margin-bottom: 1rem;">Create Image tool wuxuu hadda soo saaraa professional image preview:</p>
            
            <div class="image-placeholder">
                <div class="image-placeholder-icon">🎨</div>
                <h4 style="color: white; margin-bottom: 0.5rem;">AI-Generated Image</h4>
                <p style="color: rgba(255,255,255,0.8); margin: 0;">Beautiful landscape with mountains...</p>
                <div class="image-actions">
                    <button class="image-btn">📥 Download</button>
                    <button class="image-btn">🔄 Regenerate</button>
                    <button class="image-btn">✏️ Edit</button>
                </div>
            </div>
        </div>
        
        <div class="success">
            <h3>🎉 AI Tools Updated Successfully!</h3>
            <p><strong>New Design:</strong> ChatGPT-style interface oo leh emoji icons</p>
            <p><strong>Consistent Buttons:</strong> "Next" button dhammaan tools-yada</p>
            <p><strong>Enhanced Image Tool:</strong> Professional preview oo leh action buttons</p>
            <p><strong>Tools Menu:</strong> Added Tools option to close menu</p>
        </div>
        
        <div class="demo-buttons">
            <a href="dashboard.html" class="demo-btn">
                🛠️ Test Updated Tools
            </a>
            <a href="ai-tools-demo.html" class="demo-btn secondary">
                📊 Original Tools Demo
            </a>
            <a href="features-demo.html" class="demo-btn secondary">
                🚀 All Features
            </a>
        </div>
    </div>
</body>
</html>
