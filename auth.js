// Tincada AI - Authentication Utilities
class AuthManager {
    constructor() {
        this.storageKeys = {
            isLoggedIn: 'tincadaAI_isLoggedIn',
            currentUser: 'tincadaAI_currentUser',
            loginAttempts: 'tincadaAI_loginAttempts',
            lastLoginTime: 'tincadaAI_lastLoginTime'
        };
    }

    // Check if user is authenticated
    isAuthenticated() {
        const isLoggedIn = localStorage.getItem(this.storageKeys.isLoggedIn);
        const userData = localStorage.getItem(this.storageKeys.currentUser);
        
        return isLoggedIn === 'true' && userData !== null;
    }

    // Get current user data
    getCurrentUser() {
        const userData = localStorage.getItem(this.storageKeys.currentUser);
        return userData ? JSON.parse(userData) : null;
    }

    // Login user
    login(userData) {
        try {
            // Add login timestamp
            userData.lastLogin = new Date().toISOString();
            
            // Save to localStorage
            localStorage.setItem(this.storageKeys.isLoggedIn, 'true');
            localStorage.setItem(this.storageKeys.currentUser, JSON.stringify(userData));
            localStorage.setItem(this.storageKeys.lastLoginTime, userData.lastLogin);
            
            // Clear login attempts
            localStorage.removeItem(this.storageKeys.loginAttempts);
            
            return true;
        } catch (error) {
            console.error('Login error:', error);
            return false;
        }
    }

    // Logout user
    logout() {
        // Clear authentication data
        localStorage.removeItem(this.storageKeys.isLoggedIn);
        localStorage.removeItem(this.storageKeys.currentUser);
        localStorage.removeItem(this.storageKeys.lastLoginTime);
        
        // Keep chat history and other app data
        // Only remove auth-related data
        
        return true;
    }

    // Track login attempts (for security)
    trackLoginAttempt(success = false) {
        const attempts = this.getLoginAttempts();
        const now = Date.now();
        
        if (success) {
            // Clear attempts on successful login
            localStorage.removeItem(this.storageKeys.loginAttempts);
            return { allowed: true, remaining: 0 };
        }
        
        // Add failed attempt
        attempts.push(now);
        
        // Keep only attempts from last 15 minutes
        const fifteenMinutesAgo = now - (15 * 60 * 1000);
        const recentAttempts = attempts.filter(time => time > fifteenMinutesAgo);
        
        localStorage.setItem(this.storageKeys.loginAttempts, JSON.stringify(recentAttempts));
        
        // Allow max 5 attempts per 15 minutes
        const maxAttempts = 5;
        const remaining = maxAttempts - recentAttempts.length;
        
        return {
            allowed: recentAttempts.length < maxAttempts,
            remaining: Math.max(0, remaining),
            nextAttemptTime: recentAttempts.length >= maxAttempts ? 
                new Date(recentAttempts[0] + (15 * 60 * 1000)) : null
        };
    }

    getLoginAttempts() {
        const attempts = localStorage.getItem(this.storageKeys.loginAttempts);
        return attempts ? JSON.parse(attempts) : [];
    }

    // Validate user data
    validateUserData(userData) {
        const errors = [];
        
        if (!userData.fullName || userData.fullName.trim().length < 2) {
            errors.push('Magaca waa in uu ka kooban yahay ugu yaraan 2 xaraf');
        }
        
        if (!userData.phoneNumber || !this.validatePhoneNumber(userData.phoneNumber)) {
            errors.push('Lambarka telefoonka ma sax aha');
        }
        
        if (!userData.password || userData.password.length < 6) {
            errors.push('Furaha sirta ah waa in uu ka kooban yahay ugu yaraan 6 xaraf');
        }
        
        if (!userData.profileImage) {
            errors.push('Sawirka profile-ka waa lagama maarmaan');
        }
        
        return {
            isValid: errors.length === 0,
            errors: errors
        };
    }

    validatePhoneNumber(phoneNumber) {
        // Somalia phone number format: +252 XX XXX XXXX
        const somaliaPhoneRegex = /^\+252\s\d{2}\s\d{3}\s\d{4}$/;
        return somaliaPhoneRegex.test(phoneNumber);
    }

    // Generate secure user ID
    generateUserId() {
        const timestamp = Date.now();
        const random = Math.random().toString(36).substr(2, 9);
        return `user_${timestamp}_${random}`;
    }

    // Check session validity (optional - for enhanced security)
    isSessionValid() {
        const lastLoginTime = localStorage.getItem(this.storageKeys.lastLoginTime);
        if (!lastLoginTime) return false;
        
        const lastLogin = new Date(lastLoginTime);
        const now = new Date();
        const hoursSinceLogin = (now - lastLogin) / (1000 * 60 * 60);
        
        // Session expires after 24 hours
        return hoursSinceLogin < 24;
    }

    // Refresh session
    refreshSession() {
        if (this.isAuthenticated()) {
            const userData = this.getCurrentUser();
            if (userData) {
                userData.lastLogin = new Date().toISOString();
                localStorage.setItem(this.storageKeys.currentUser, JSON.stringify(userData));
                localStorage.setItem(this.storageKeys.lastLoginTime, userData.lastLogin);
                return true;
            }
        }
        return false;
    }

    // Get user display info
    getUserDisplayInfo() {
        const user = this.getCurrentUser();
        if (!user) return null;
        
        return {
            fullName: user.fullName,
            firstName: user.fullName.split(' ')[0],
            phoneNumber: user.phoneNumber,
            profileImage: user.profileImage,
            userId: user.userId,
            lastLogin: user.lastLogin
        };
    }

    // Security: Clear sensitive data on page unload
    setupSecurityCleanup() {
        window.addEventListener('beforeunload', () => {
            // Could implement additional security measures here
            // For now, we'll just refresh the session
            this.refreshSession();
        });
    }

    // Export user data (for backup/transfer)
    exportUserData() {
        const user = this.getCurrentUser();
        if (!user) return null;
        
        // Remove sensitive data from export
        const exportData = {
            fullName: user.fullName,
            phoneNumber: user.phoneNumber,
            profileImage: user.profileImage,
            userId: user.userId,
            loginTime: user.loginTime
            // Note: password is not included for security
        };
        
        return exportData;
    }

    // Import user data (for restore)
    importUserData(userData, password) {
        if (!userData || !password) return false;
        
        // Add password and login
        userData.password = password;
        
        const validation = this.validateUserData(userData);
        if (!validation.isValid) {
            return { success: false, errors: validation.errors };
        }
        
        return { success: this.login(userData), errors: [] };
    }
}

// Global auth manager instance
const authManager = new AuthManager();

// Auto-setup security cleanup
document.addEventListener('DOMContentLoaded', () => {
    authManager.setupSecurityCleanup();
});
