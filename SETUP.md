# 🚀 Tincada AI Setup Guide

## Quick Start

### 1. Install Dependencies
```bash
pip install -r requirements.txt
```

### 2. Set Up Environment Variables
```bash
# Copy the example environment file
cp .env.example .env

# Edit .env file and add your OpenAI API key
OPENAI_API_KEY=your-actual-api-key-here
```

### 3. Run the Server
```bash
python flask_server.py
```

### 4. Open Dashboard
Visit: http://localhost:5000

---

## 🔑 Getting OpenAI API Key

1. **Visit OpenAI Platform**: https://platform.openai.com
2. **Sign Up/Login**: Create account or login
3. **Go to API Keys**: Navigate to API section
4. **Create New Key**: Generate a new secret key
5. **Copy Key**: Save it securely
6. **Add to .env**: Paste in your .env file

⚠️ **Security Warning**: Never commit your API key to version control!

---

## 🛠️ Features Available

### ✅ Working Features
- **Chat Interface**: Real-time AI chat
- **AI Tools**: 5 professional tools
- **Help System**: Comprehensive help
- **Customization**: Profile & themes
- **Payment System**: Subscription plans
- **Responsive Design**: Mobile-friendly

### 🔧 AI Tools
1. **💡 Think Longer**: Deep analysis
2. **🔍 Deep Research**: Comprehensive research
3. **🎨 Create Image**: AI image generation (preview)
4. **🌐 Web Search**: Internet search (simulated)
5. **✏️ Canvas**: Interactive workspace

---

## 🔄 Fallback Mode

If OpenAI API is not configured, the system runs in fallback mode:
- ✅ All UI features work
- ✅ Simulated AI responses
- ✅ Tool previews available
- ⚠️ Limited AI functionality

---

## 📁 Project Structure

```
tincada-ai/
├── dashboard.html          # Main interface
├── dashboard.css          # Styling
├── dashboard.js           # Frontend logic
├── flask_server.py        # Backend API
├── server.py             # Simple HTTP server
├── requirements.txt       # Python dependencies
├── .env.example          # Environment template
├── SETUP.md              # This file
└── demo files/           # Demo pages
    ├── features-demo.html
    ├── ai-tools-demo.html
    ├── help-customize-demo.html
    └── updated-tools-demo.html
```

---

## 🚨 Troubleshooting

### Common Issues

**1. OpenAI API Errors**
```bash
# Check API key is set
echo $OPENAI_API_KEY

# Verify key format (starts with sk-)
# Check OpenAI account has credits
```

**2. Module Not Found**
```bash
# Install missing packages
pip install flask flask-cors openai python-dotenv
```

**3. Port Already in Use**
```bash
# Kill process on port 5000
lsof -ti:5000 | xargs kill -9

# Or use different port
python flask_server.py --port 5001
```

**4. CORS Issues**
- Flask-CORS is configured
- Check browser console for errors
- Ensure proper headers

---

## 🔒 Security Best Practices

1. **API Key Security**
   - Never commit API keys
   - Use environment variables
   - Rotate keys regularly

2. **Rate Limiting**
   - 60 requests per hour per user
   - Automatic rate limiting enabled
   - Monitor usage

3. **Input Validation**
   - All inputs are validated
   - XSS protection enabled
   - SQL injection prevention

---

## 🌐 Deployment

### Local Development
```bash
python flask_server.py
```

### Production (Example with Gunicorn)
```bash
pip install gunicorn
gunicorn -w 4 -b 0.0.0.0:5000 flask_server:app
```

### Environment Variables for Production
```bash
export FLASK_ENV=production
export OPENAI_API_KEY=your-key
export SECRET_KEY=your-secret-key
```

---

## 📊 API Endpoints

### Chat API
```bash
POST /api/chat
{
  "message": "Hello AI",
  "user_id": "user123"
}
```

### Tools API
```bash
POST /api/tools/think-longer
{
  "prompt": "Analyze this problem",
  "depth": "comprehensive",
  "user_id": "user123"
}
```

### Health Check
```bash
GET /api/health
```

---

## 🎯 Next Steps

1. **Configure OpenAI API** for full functionality
2. **Test all features** in the dashboard
3. **Customize styling** in dashboard.css
4. **Add more tools** in flask_server.py
5. **Deploy to production** server

---

## 📞 Support

If you encounter issues:
1. Check this setup guide
2. Review error logs
3. Test with fallback mode
4. Verify API configuration

**Happy coding with Tincada AI!** 🎉
