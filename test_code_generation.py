import requests
import json

def test_code_generation():
    """Test AI code generation capability"""
    
    test_cases = [
        {
            "name": "JavaScript Function",
            "message": "Fadlan ii samee JavaScript function oo calculate gareeysa area of circle"
        },
        {
            "name": "Python Script", 
            "message": "Samee Python script oo file-ka aqriyo"
        },
        {
            "name": "General Question",
            "message": "Maxaad sameen kartaa?"
        }
    ]
    
    print("🧪 Testing Tincada AI Code Generation...")
    print("=" * 50)
    
    for i, test in enumerate(test_cases, 1):
        print(f"\n{i}️⃣ Testing: {test['name']}")
        print(f"📤 Message: {test['message']}")
        
        try:
            data = {
                "message": test['message'],
                "user_id": "test_user",
                "chat_history": []
            }
            
            response = requests.post(
                'http://localhost:5001/api/chat',
                json=data,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ Response received")
                print(f"📊 Source: {result['source']}")
                print(f"🔢 Tokens used: {result['tokens_used']}")
                print(f"📝 Response preview: {result['response'][:150]}...")
                
                # Check if response contains code
                if any(keyword in result['response'].lower() for keyword in ['function', 'def ', 'const ', 'let ', 'var ', '```']):
                    print("💻 ✅ Contains code!")
                else:
                    print("💬 Regular text response")
                    
            else:
                print(f"❌ Failed: {response.status_code}")
                print(f"Error: {response.text}")
                
        except Exception as e:
            print(f"❌ Error: {e}")
        
        print("-" * 30)

if __name__ == "__main__":
    test_code_generation()
