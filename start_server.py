#!/usr/bin/env python3
"""
Simple server starter for Tincada AI
"""

import os
import sys
import subprocess
import time

def check_dependencies():
    """Check if required packages are installed"""
    required_packages = ['flask', 'flask_cors', 'openai', 'dotenv']
    missing = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            if package == 'dotenv':
                try:
                    __import__('python_dotenv')
                except ImportError:
                    missing.append('python-dotenv')
            else:
                missing.append(package)
    
    if missing:
        print(f"❌ Missing packages: {', '.join(missing)}")
        print("   Install with: pip install " + " ".join(missing))
        return False
    
    print("✅ All dependencies installed")
    return True

def check_env_file():
    """Check if .env file exists and has API key"""
    if not os.path.exists('.env'):
        print("❌ .env file not found")
        print("   Create it with: cp .env.example .env")
        print("   Then add your OpenAI API key")
        return False
    
    try:
        with open('.env', 'r') as f:
            content = f.read()
            if 'OPENAI_API_KEY=' in content and 'sk-' in content:
                print("✅ .env file configured with API key")
                return True
            else:
                print("⚠️  .env file exists but no API key found")
                print("   Add: OPENAI_API_KEY=your-api-key-here")
                return True  # Still allow server to run in fallback mode
    except Exception as e:
        print(f"❌ Error reading .env file: {e}")
        return False

def start_server():
    """Start the Flask server"""
    print("🚀 Starting Tincada AI Server...")
    print("=" * 50)
    
    # Check dependencies
    if not check_dependencies():
        return False
    
    # Check environment
    check_env_file()
    
    print("\n🔄 Launching Flask server...")
    print("📱 Dashboard will be available at: http://localhost:5001")
    print("🔧 API health check: http://localhost:5001/api/health")
    print("\n⚠️  Press Ctrl+C to stop the server")
    print("=" * 50)
    
    try:
        # Start the server
        subprocess.run([sys.executable, 'app.py'], check=True)
    except KeyboardInterrupt:
        print("\n\n🛑 Server stopped by user")
    except subprocess.CalledProcessError as e:
        print(f"\n❌ Server failed to start: {e}")
        return False
    except FileNotFoundError:
        print("❌ app.py not found in current directory")
        return False
    
    return True

if __name__ == "__main__":
    print("🎯 Tincada AI Server Starter")
    print("=" * 50)
    
    if start_server():
        print("✅ Server startup completed")
    else:
        print("❌ Server startup failed")
        print("\n💡 Troubleshooting:")
        print("   1. Make sure you're in the correct directory")
        print("   2. Install dependencies: pip install -r requirements.txt")
        print("   3. Configure .env file with OpenAI API key")
        print("   4. Check app.py exists")
