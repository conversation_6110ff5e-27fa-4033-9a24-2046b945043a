<!DOCTYPE html>
<html lang="so">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tincada AI - Instant Image Generator</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
            display: grid;
            grid-template-columns: 1fr 1fr;
            min-height: 80vh;
        }

        .input-panel {
            padding: 30px;
            background: #f8f9fa;
            display: flex;
            flex-direction: column;
        }

        .preview-panel {
            padding: 30px;
            background: white;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
        }

        .header h1 {
            color: #333;
            font-size: 2em;
            margin-bottom: 10px;
        }

        .prompt-input {
            width: 100%;
            padding: 15px;
            border: 2px solid #ddd;
            border-radius: 10px;
            font-size: 16px;
            margin-bottom: 20px;
            outline: none;
            transition: border-color 0.3s;
            resize: vertical;
            min-height: 100px;
        }

        .prompt-input:focus {
            border-color: #4ECDC4;
        }

        .generate-btn {
            padding: 15px 30px;
            background: linear-gradient(45deg, #FF6B6B, #4ECDC4);
            color: white;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 18px;
            font-weight: bold;
            transition: all 0.3s;
            margin-bottom: 20px;
        }

        .generate-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .generate-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
        }

        .quick-prompts {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .quick-prompt-btn {
            padding: 12px 15px;
            background: white;
            border: 2px solid #e0e0e0;
            border-radius: 10px;
            cursor: pointer;
            text-align: left;
            transition: all 0.3s;
            font-size: 14px;
        }

        .quick-prompt-btn:hover {
            border-color: #4ECDC4;
            background: #f0f9ff;
        }

        .preview-area {
            width: 100%;
            max-width: 500px;
            aspect-ratio: 1;
            border: 3px dashed #ddd;
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #f8f9fa;
            position: relative;
            overflow: hidden;
        }

        .preview-image {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 12px;
            opacity: 0;
            transition: opacity 0.5s ease;
        }

        .preview-image.loaded {
            opacity: 1;
        }

        .placeholder-text {
            color: #999;
            font-size: 18px;
            text-align: center;
            padding: 20px;
        }

        .loading-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.9);
            display: none;
            align-items: center;
            justify-content: center;
            flex-direction: column;
            border-radius: 12px;
        }

        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #4ECDC4;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            animation: spin 1s linear infinite;
            margin-bottom: 15px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .image-info {
            margin-top: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 10px;
            width: 100%;
            max-width: 500px;
            display: none;
        }

        .download-btn {
            margin-top: 15px;
            padding: 10px 20px;
            background: #28a745;
            color: white;
            border: none;
            border-radius: 20px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
        }

        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 25px;
            border-radius: 10px;
            color: white;
            font-weight: bold;
            z-index: 1000;
            transform: translateX(400px);
            transition: transform 0.3s ease;
        }

        .notification.show {
            transform: translateX(0);
        }

        .notification.success {
            background: linear-gradient(45deg, #28a745, #20c997);
        }

        .notification.error {
            background: linear-gradient(45deg, #dc3545, #fd7e14);
        }

        .notification.info {
            background: linear-gradient(45deg, #17a2b8, #6f42c1);
        }

        @media (max-width: 768px) {
            .container {
                grid-template-columns: 1fr;
                grid-template-rows: auto auto;
            }
            
            .preview-area {
                max-width: 300px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="input-panel">
            <div class="header">
                <h1>🎨 Instant AI Images</h1>
                <p>Qor waxa aad rabto, sawirka si toos ah ayuu u muuqan doonaa</p>
            </div>

            <textarea 
                id="promptInput" 
                class="prompt-input" 
                placeholder="Tusaale: A gray tabby cat hugging an otter with an orange scarf..."
                rows="4"></textarea>

            <button id="generateBtn" class="generate-btn" onclick="generateImage()">
                🎨 Samee Sawir
            </button>

            <div class="quick-prompts">
                <h3 style="margin-bottom: 10px;">💡 Quick Ideas:</h3>
                <button class="quick-prompt-btn" onclick="setPrompt('A gray tabby cat hugging an otter with an orange scarf')">
                    🐱 Cat & Otter with Orange Scarf
                </button>
                <button class="quick-prompt-btn" onclick="setPrompt('A beautiful sunset over mountains with a lake, photorealistic')">
                    🌅 Sunset Over Mountains
                </button>
                <button class="quick-prompt-btn" onclick="setPrompt('A futuristic city with flying cars and neon lights')">
                    🏙️ Futuristic City
                </button>
                <button class="quick-prompt-btn" onclick="setPrompt('A peaceful garden with colorful flowers and butterflies')">
                    🌸 Peaceful Garden
                </button>
                <button class="quick-prompt-btn" onclick="setPrompt('A majestic lion in African savanna at golden hour')">
                    🦁 Lion in Savanna
                </button>
            </div>
        </div>

        <div class="preview-panel">
            <div class="preview-area" id="previewArea">
                <div class="placeholder-text" id="placeholderText">
                    🎨 Sawirkaaga halkan ayuu ka muuqan doonaa<br>
                    <small>Qor prompt-ka oo riix "Samee Sawir"</small>
                </div>
                
                <img id="previewImage" class="preview-image" alt="Generated Image">
                
                <div class="loading-overlay" id="loadingOverlay">
                    <div class="spinner"></div>
                    <p>🎨 Sawirka waa la sameeynayaa...</p>
                </div>
            </div>

            <div class="image-info" id="imageInfo">
                <p><strong>Prompt:</strong> <span id="usedPrompt"></span></p>
                <p><strong>Generated:</strong> <span id="generatedTime"></span></p>
                <a id="downloadLink" class="download-btn" download>📥 Download</a>
            </div>
        </div>
    </div>

    <script>
        function setPrompt(prompt) {
            document.getElementById('promptInput').value = prompt;
            // Auto-generate when quick prompt is selected
            setTimeout(() => generateImage(), 500);
        }

        async function generateImage() {
            const prompt = document.getElementById('promptInput').value.trim();
            
            if (!prompt) {
                showNotification('❌ Fadlan qor waxa aad rabto inaad sawir ka sameyso!', 'error');
                return;
            }

            const loadingOverlay = document.getElementById('loadingOverlay');
            const previewImage = document.getElementById('previewImage');
            const placeholderText = document.getElementById('placeholderText');
            const generateBtn = document.getElementById('generateBtn');
            const imageInfo = document.getElementById('imageInfo');

            // Show loading
            loadingOverlay.style.display = 'flex';
            placeholderText.style.display = 'none';
            generateBtn.disabled = true;
            generateBtn.textContent = '🎨 Sameeynaya...';
            imageInfo.style.display = 'none';

            showNotification('🎨 Sawir sameeynta waa bilaabmay...', 'info');

            try {
                const response = await fetch('/api/generate-image', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        prompt: prompt,
                        size: '1024x1024',
                        user_id: 'instant_generator_user'
                    })
                });

                const data = await response.json();

                if (data.success) {
                    // Load image
                    previewImage.src = data.image_url;
                    
                    previewImage.onload = function() {
                        // Hide loading and show image
                        loadingOverlay.style.display = 'none';
                        previewImage.classList.add('loaded');
                        
                        // Show image info
                        document.getElementById('usedPrompt').textContent = data.prompt;
                        document.getElementById('generatedTime').textContent = new Date(data.timestamp).toLocaleString();
                        document.getElementById('downloadLink').href = data.image_url;
                        document.getElementById('downloadLink').download = data.filename;
                        imageInfo.style.display = 'block';
                        
                        // Success notification
                        if (data.mock_mode) {
                            showNotification('🎨 Demo sawir la sameeyay! Real API key u baahan tahay sawiro dhabta ah.', 'info');
                        } else {
                            showNotification('🎉 Sawirka waa la sameeyay!', 'success');
                        }
                    };
                    
                } else {
                    loadingOverlay.style.display = 'none';
                    placeholderText.style.display = 'block';
                    showNotification('❌ Khalad: ' + data.error, 'error');
                }

            } catch (error) {
                loadingOverlay.style.display = 'none';
                placeholderText.style.display = 'block';
                showNotification('❌ Khalad ayaa dhacay: ' + error.message, 'error');
            } finally {
                generateBtn.disabled = false;
                generateBtn.textContent = '🎨 Samee Sawir';
            }
        }

        function showNotification(message, type) {
            const existing = document.querySelector('.notification');
            if (existing) existing.remove();

            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.textContent = message;
            
            document.body.appendChild(notification);
            
            setTimeout(() => notification.classList.add('show'), 100);
            
            setTimeout(() => {
                notification.classList.remove('show');
                setTimeout(() => notification.remove(), 300);
            }, 5000);
        }

        // Auto-generate on Enter key
        document.getElementById('promptInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                generateImage();
            }
        });
    </script>
</body>
</html>
