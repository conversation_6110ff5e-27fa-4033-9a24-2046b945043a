#!/usr/bin/env python3
"""
Quick Final Test - New API Key
"""

import requests

def test_api_responses():
    """Test AI responses with new API key"""
    
    print("🧪 QUICK API TEST - NEW KEY")
    print("=" * 50)
    
    tests = [
        {"lang": "Somali", "q": "<PERSON>aad tahay?"},
        {"lang": "English", "q": "What is 2+2?"},
        {"lang": "Arabic", "q": "ما هي عاصمة مصر؟"},
        {"lang": "Code", "q": "Write HTML button code"}
    ]
    
    for test in tests:
        print(f"\n🗣️ {test['lang']}: {test['q']}")
        
        try:
            response = requests.post(
                "http://localhost:5001/api/chat",
                json={"message": test['q'], "user_id": "test"},
                timeout=20
            )
            
            if response.status_code == 200:
                data = response.json()
                answer = data['response']
                print(f"✅ Response: {answer[:80]}...")
            else:
                print(f"❌ Error: {response.status_code}")
                
        except Exception as e:
            print(f"❌ Error: {e}")
    
    print("\n" + "=" * 50)
    print("🎯 AI is using new OpenAI API key!")
    print("🌍 Responds in all languages!")
    print("🌐 Server: http://localhost:5001")

if __name__ == "__main__":
    test_api_responses()
