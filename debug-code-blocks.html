<!DOCTYPE html>
<html lang="so">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Code Blocks - Tincada AI</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 2rem;
            background: #f0f0f0;
            line-height: 1.6;
        }
        .debug-container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 3rem;
            padding: 2rem;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #f9f9f9;
        }
        .test-btn {
            background: #4ecdc4;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px;
            font-size: 1rem;
        }
        .test-btn:hover {
            background: #44a08d;
        }
        .result {
            margin-top: 2rem;
            padding: 1rem;
            border: 1px solid #ccc;
            border-radius: 5px;
            background: white;
            min-height: 100px;
        }
        .inline-code {
            background: rgba(0, 0, 0, 0.1);
            padding: 2px 6px;
            border-radius: 4px;
            font-family: 'Consolas', 'Monaco', monospace;
            font-size: 0.9em;
        }
        .debug-info {
            background: #e3f2fd;
            padding: 1rem;
            border-radius: 5px;
            margin: 1rem 0;
            font-family: monospace;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <div class="debug-container">
        <h1>🐛 Debug AI Code Blocks</h1>
        <p>Testing AI code component integration and formatting</p>
        
        <div class="test-section">
            <h2>1. Test AI Code Component Directly</h2>
            <button class="test-btn" onclick="testDirectCodeBlock()">Test Direct Code Block</button>
            <div id="directResult" class="result"></div>
        </div>
        
        <div class="test-section">
            <h2>2. Test formatAIMessage Function</h2>
            <button class="test-btn" onclick="testFormatFunction()">Test Format Function</button>
            <div id="formatResult" class="result"></div>
        </div>
        
        <div class="test-section">
            <h2>3. Test Triple Backticks Detection</h2>
            <button class="test-btn" onclick="testTripleBackticks()">Test Backticks</button>
            <div id="backticksResult" class="result"></div>
        </div>
        
        <div class="test-section">
            <h2>4. Test Dashboard Integration</h2>
            <button class="test-btn" onclick="testDashboardIntegration()">Test Dashboard</button>
            <div id="dashboardResult" class="result"></div>
        </div>
        
        <div class="debug-info" id="debugInfo">
            Debug info will appear here...
        </div>
    </div>

    <script src="ai-code-component.js"></script>
    <script>
        function updateDebugInfo(message) {
            const debugInfo = document.getElementById('debugInfo');
            debugInfo.innerHTML += `<div>${new Date().toLocaleTimeString()}: ${message}</div>`;
        }

        function testDirectCodeBlock() {
            updateDebugInfo('Testing direct AI code component...');
            
            try {
                // Check if aiCodeBlock exists
                if (typeof aiCodeBlock === 'undefined') {
                    throw new Error('aiCodeBlock is not defined');
                }
                
                const testCode = `function hello() {
    console.log("Hello World!");
    return "Success";
}`;
                
                const result = aiCodeBlock.createCodeBlock(testCode, 'javascript');
                document.getElementById('directResult').innerHTML = result;
                
                updateDebugInfo('✅ Direct code block test successful');
                
            } catch (error) {
                updateDebugInfo('❌ Direct code block test failed: ' + error.message);
                document.getElementById('directResult').innerHTML = `<p style="color: red;">Error: ${error.message}</p>`;
            }
        }

        function testFormatFunction() {
            updateDebugInfo('Testing formatAIMessage function...');
            
            try {
                // Create a mock dashboard object with formatAIMessage
                const mockDashboard = {
                    escapeHtml: function(text) {
                        const div = document.createElement('div');
                        div.textContent = text;
                        return div.innerHTML.replace(/\n/g, '<br>');
                    },
                    
                    formatAIMessage: function(text) {
                        let formatted = text;

                        // FIRST: Format code blocks with AI Code Component - BEFORE escaping HTML
                        formatted = formatted.replace(/```(\w+)?\n([\s\S]*?)```/g, (match, language, code) => {
                            const lang = language || 'javascript';
                            console.log('🎨 Creating code block for language:', lang);
                            // Use AI Code Block component for professional code display
                            return aiCodeBlock.createCodeBlock(code.trim(), lang);
                        });

                        // SECOND: Escape HTML for non-code content
                        formatted = this.escapeHtml(formatted);

                        // Format inline code (after HTML escape)
                        formatted = formatted.replace(/`([^`]+)`/g, '<code class="inline-code">$1</code>');

                        return formatted;
                    }
                };
                
                const testText = `Halkan waa JavaScript function:

\`\`\`javascript
function greet(name) {
    return \`Hello, \${name}!\`;
}

console.log(greet("Ahmed"));
\`\`\`

Function-kan wuxuu sameeyaa greeting message.`;
                
                const result = mockDashboard.formatAIMessage(testText);
                document.getElementById('formatResult').innerHTML = result;
                
                updateDebugInfo('✅ Format function test successful');
                
            } catch (error) {
                updateDebugInfo('❌ Format function test failed: ' + error.message);
                document.getElementById('formatResult').innerHTML = `<p style="color: red;">Error: ${error.message}</p>`;
            }
        }

        function testTripleBackticks() {
            updateDebugInfo('Testing triple backticks detection...');
            
            const testText = `Here is some code:

\`\`\`python
def calculate_sum(a, b):
    """Calculate sum of two numbers"""
    result = a + b
    print(f"Sum of {a} and {b} is {result}")
    return result

# Usage
total = calculate_sum(5, 3)
\`\`\`

This function adds two numbers.`;
            
            // Test regex pattern
            const codeBlockRegex = /```(\w+)?\n([\s\S]*?)```/g;
            const matches = [...testText.matchAll(codeBlockRegex)];
            
            updateDebugInfo(`Found ${matches.length} code blocks`);
            
            if (matches.length > 0) {
                const match = matches[0];
                const language = match[1] || 'javascript';
                const code = match[2];
                
                updateDebugInfo(`Language: ${language}, Code length: ${code.length}`);
                
                const result = aiCodeBlock.createCodeBlock(code.trim(), language);
                document.getElementById('backticksResult').innerHTML = result;
                
                updateDebugInfo('✅ Triple backticks test successful');
            } else {
                updateDebugInfo('❌ No code blocks found');
                document.getElementById('backticksResult').innerHTML = '<p>No code blocks detected</p>';
            }
        }

        function testDashboardIntegration() {
            updateDebugInfo('Testing dashboard integration...');
            
            // Check if dashboard object exists
            if (typeof window.dashboard !== 'undefined') {
                updateDebugInfo('✅ Dashboard object found');
                
                const testMessage = `Samee JavaScript function:

\`\`\`javascript
const processData = async (data) => {
    try {
        const result = await fetch('/api/process', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(data)
        });
        
        return await result.json();
    } catch (error) {
        console.error('Processing failed:', error);
        throw error;
    }
};
\`\`\`

Function-kan wuxuu process gareeyaa data.`;
                
                try {
                    const formatted = window.dashboard.formatAIMessage(testMessage);
                    document.getElementById('dashboardResult').innerHTML = formatted;
                    updateDebugInfo('✅ Dashboard integration test successful');
                } catch (error) {
                    updateDebugInfo('❌ Dashboard formatting failed: ' + error.message);
                    document.getElementById('dashboardResult').innerHTML = `<p style="color: red;">Error: ${error.message}</p>`;
                }
            } else {
                updateDebugInfo('❌ Dashboard object not found');
                document.getElementById('dashboardResult').innerHTML = '<p style="color: orange;">Dashboard not loaded. Open this page from dashboard.html</p>';
            }
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            updateDebugInfo('🐛 Debug page loaded');
            
            // Check if AI code component is loaded
            if (typeof aiCodeBlock !== 'undefined') {
                updateDebugInfo('✅ AI Code Component loaded successfully');
            } else {
                updateDebugInfo('❌ AI Code Component not loaded');
            }
            
            // Auto-run direct test
            setTimeout(testDirectCodeBlock, 1000);
        });
    </script>
</body>
</html>
