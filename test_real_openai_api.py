#!/usr/bin/env python3
"""
Test that Tincada AI is now using real OpenAI API
"""

import requests
import json

def test_real_openai_usage():
    """Test that AI is using real OpenAI API responses"""
    
    base_url = "http://localhost:5001"
    
    tests = [
        {
            "question": "What is 2+2? Give a brief answer.",
            "expected_source": "openai",
            "description": "Simple math question"
        },
        {
            "question": "What is artificial intelligence?",
            "expected_source": "openai", 
            "description": "Complex AI question"
        },
        {
            "question": "Salam alaykum! Maxaa ka dhigan AI?",
            "expected_source": "openai",
            "description": "Somali language question"
        },
        {
            "question": "Write a Python function to calculate fibonacci numbers",
            "expected_source": "openai",
            "description": "Programming request"
        }
    ]
    
    print("🧪 Testing Real OpenAI API Usage")
    print("=" * 60)
    
    success_count = 0
    total_tests = len(tests)
    
    for i, test in enumerate(tests, 1):
        print(f"\n📝 Test {i}: {test['description']}")
        print(f"❓ Question: {test['question']}")
        print("-" * 50)
        
        try:
            response = requests.post(f"{base_url}/api/chat", json={
                "message": test['question'],
                "user_id": "test_user",
                "chat_history": []
            })
            
            if response.status_code == 200:
                data = response.json()
                ai_response = data.get('response', 'No response')
                source = data.get('source', 'unknown')
                tokens = data.get('tokens_used', 0)
                
                print(f"📊 Source: {source}")
                print(f"🔢 Tokens used: {tokens}")
                print(f"💬 Response: {ai_response[:150]}...")
                
                if source == 'openai':
                    print("✅ SUCCESS: Using real OpenAI API!")
                    success_count += 1
                elif source == 'mock_fallback':
                    print("⚠️ FALLBACK: API failed, using mock")
                else:
                    print(f"❓ Unknown source: {source}")
                    
            else:
                print(f"❌ API Error: {response.status_code}")
                
        except Exception as e:
            print(f"❌ Request failed: {e}")
    
    print("\n" + "=" * 60)
    print("📊 FINAL RESULTS:")
    print(f"✅ Real OpenAI API: {success_count}/{total_tests}")
    print(f"❌ Mock/Failed: {total_tests - success_count}/{total_tests}")
    print(f"📈 Success Rate: {(success_count/total_tests)*100:.1f}%")
    
    if success_count == total_tests:
        print("🎉 PERFECT! AI is using real OpenAI API for all responses!")
        print("✅ AI now provides accurate, real-time information from OpenAI")
    elif success_count > 0:
        print("⚠️ PARTIAL SUCCESS: Some requests use real API, others fallback")
    else:
        print("❌ FAILED: AI is not using real OpenAI API")
    
    return success_count == total_tests

if __name__ == "__main__":
    success = test_real_openai_usage()
    
    print("\n" + "=" * 60)
    if success:
        print("🎯 AI is now using real OpenAI API for accurate responses!")
        print("✅ Every response comes from OpenAI's knowledge base")
    else:
        print("🔧 Some issues detected. Check API key permissions.")
        print("ℹ️ AI will fallback to professional mock responses when needed")
