/* Dashboard CSS - ChatGPT Style */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', sans-serif;
    background: #212121;
    color: #ffffff;
    overflow: hidden;
}

/* Dashboard Container */
.dashboard-container {
    display: flex;
    height: 100vh;
    width: 100vw;
}

/* Sidebar */
.sidebar {
    width: 260px;
    background: #171717;
    border-right: 1px solid #2f2f2f;
    display: flex;
    flex-direction: column;
    transition: all 0.3s ease;
    position: relative;
    z-index: 1000;
}

.sidebar.collapsed {
    width: 60px;
}

/* Sidebar Header */
.sidebar-header {
    padding: 16px;
    border-bottom: 1px solid #2f2f2f;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.logo {
    display: flex;
    align-items: center;
    gap: 10px;
    color: #ffffff;
    font-weight: 600;
    font-size: 1.1rem;
}

.logo i {
    font-size: 1.5rem;
    color: #10a37f;
}

.logo-img {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid #10a37f;
}

.sidebar-toggle {
    background: none;
    border: none;
    color: #8e8ea0;
    cursor: pointer;
    padding: 8px;
    border-radius: 6px;
    transition: all 0.2s ease;
}

.sidebar-toggle:hover {
    background: #2f2f2f;
    color: #ffffff;
}

/* New Chat Button */
.new-chat-section {
    padding: 16px;
}

.new-chat-btn {
    width: 100%;
    background: none;
    border: 1px solid #2f2f2f;
    color: #ffffff;
    padding: 12px 16px;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 10px;
    font-family: inherit;
    font-size: 0.9rem;
    transition: all 0.2s ease;
}

.new-chat-btn:hover {
    background: #2f2f2f;
}

.new-chat-btn i {
    font-size: 1rem;
}

/* Search Section */
.search-section {
    padding: 0 16px 16px;
}

.search-box {
    position: relative;
    display: flex;
    align-items: center;
}

.search-box i {
    position: absolute;
    left: 12px;
    color: #8e8ea0;
    font-size: 0.9rem;
}

.search-box input {
    width: 100%;
    background: #2f2f2f;
    border: none;
    color: #ffffff;
    padding: 10px 12px 10px 35px;
    border-radius: 6px;
    font-family: inherit;
    font-size: 0.9rem;
}

.search-box input:focus {
    outline: none;
    background: #3f3f3f;
}

.search-box input::placeholder {
    color: #8e8ea0;
}

/* Navigation Menu */
.nav-menu {
    padding: 0 16px;
    border-bottom: 1px solid #2f2f2f;
    margin-bottom: 16px;
}

.nav-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 10px 12px;
    border-radius: 6px;
    cursor: pointer;
    color: #8e8ea0;
    font-size: 0.9rem;
    margin-bottom: 4px;
    transition: all 0.2s ease;
    position: relative;
}

.nav-item:hover {
    background: #2f2f2f;
    color: #ffffff;
}

.nav-item.active {
    background: #2f2f2f;
    color: #ffffff;
}

.nav-item i {
    font-size: 1rem;
    width: 16px;
}

.coming-soon-badge {
    position: absolute;
    top: 8px;
    right: 8px;
    background: linear-gradient(45deg, #ff6b6b, #ee5a52);
    color: white;
    font-size: 0.6rem;
    font-weight: 600;
    padding: 2px 6px;
    border-radius: 10px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: 0 2px 8px rgba(255, 107, 107, 0.3);
    animation: comingSoonPulse 2s infinite;
}

@keyframes comingSoonPulse {
    0% {
        transform: scale(1);
        box-shadow: 0 2px 8px rgba(255, 107, 107, 0.3);
    }
    50% {
        transform: scale(1.05);
        box-shadow: 0 4px 12px rgba(255, 107, 107, 0.5);
    }
    100% {
        transform: scale(1);
        box-shadow: 0 2px 8px rgba(255, 107, 107, 0.3);
    }
}

/* Chat History */
.chat-history-section {
    flex: 1;
    overflow-y: auto;
    padding: 0 16px;
}

.history-header {
    margin-bottom: 8px;
    margin-top: 16px;
}

.history-header:first-child {
    margin-top: 0;
}

.history-header h3 {
    color: #8e8ea0;
    font-size: 0.8rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.chat-list {
    margin-bottom: 16px;
}

.chat-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    border-radius: 6px;
    cursor: pointer;
    color: #8e8ea0;
    font-size: 0.9rem;
    margin-bottom: 2px;
    transition: all 0.2s ease;
    position: relative;
}

.chat-item:hover {
    background: #2f2f2f;
    color: #ffffff;
}

.chat-item.active {
    background: #10a37f;
    color: #ffffff;
}

.chat-item i {
    font-size: 0.8rem;
    width: 12px;
}

.chat-item-text {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.chat-item-menu {
    opacity: 0;
    transition: opacity 0.2s ease;
}

.chat-item:hover .chat-item-menu {
    opacity: 1;
}

/* User Profile Section */
.user-profile-section {
    padding: 16px;
    border-top: 1px solid #2f2f2f;
    position: relative;
}

.user-profile {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 8px 12px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.user-profile:hover {
    background: #2f2f2f;
}

.user-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: #10a37f;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    flex-shrink: 0;
}

.user-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 50%;
}

.user-avatar i {
    color: #ffffff;
    font-size: 0.9rem;
}

.user-info {
    flex: 1;
    min-width: 0;
}

.user-name {
    color: #ffffff;
    font-size: 0.9rem;
    font-weight: 500;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.user-status {
    color: #8e8ea0;
    font-size: 0.8rem;
}

.profile-dropdown-btn {
    background: none;
    border: none;
    color: #8e8ea0;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.profile-dropdown-btn:hover {
    background: #3f3f3f;
    color: #ffffff;
}

/* User Dropdown */
.user-dropdown {
    position: absolute;
    bottom: 100%;
    left: 16px;
    right: 16px;
    background: #2f2f2f;
    border: 1px solid #3f3f3f;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    padding: 8px 0;
    display: none;
    z-index: 1001;
}

.user-dropdown.show {
    display: block;
    animation: slideUp 0.2s ease;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.dropdown-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 10px 16px;
    color: #ffffff;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.dropdown-item:hover {
    background: #3f3f3f;
}

.dropdown-item i {
    width: 16px;
    font-size: 0.9rem;
    color: #8e8ea0;
}

.dropdown-item:hover i {
    color: #ffffff;
}

.dropdown-item:last-child {
    margin-left: auto;
}

.dropdown-divider {
    height: 1px;
    background: #3f3f3f;
    margin: 8px 0;
}

/* Main Content */
.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: #212121;
    position: relative;
}

/* API Status Banner */
.api-status-banner {
    background: linear-gradient(135deg, #f59e0b, #d97706);
    color: white;
    padding: 12px 24px;
    border-bottom: 1px solid #92400e;
    animation: slideDown 0.3s ease;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-100%);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.banner-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    max-width: 1200px;
    margin: 0 auto;
    gap: 12px;
}

.banner-content i {
    font-size: 1.1rem;
    margin-right: 8px;
}

.banner-close {
    background: none;
    border: none;
    color: white;
    cursor: pointer;
    padding: 4px 8px;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.banner-close:hover {
    background: rgba(255, 255, 255, 0.2);
}

/* Main Header */
.main-header {
    padding: 16px 24px;
    border-bottom: 1px solid #2f2f2f;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.header-left {
    display: flex;
    align-items: center;
    gap: 16px;
}

.mobile-menu-btn {
    display: none;
    background: none;
    border: none;
    color: #8e8ea0;
    cursor: pointer;
    padding: 8px;
    border-radius: 6px;
    transition: all 0.2s ease;
}

.mobile-menu-btn:hover {
    background: #2f2f2f;
    color: #ffffff;
}

.header-left h1 {
    color: #ffffff;
    font-size: 1.2rem;
    font-weight: 600;
}

.header-logo {
    width: 28px;
    height: 28px;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid #10a37f;
    margin-right: 8px;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 16px;
}

.message-counter {
    display: flex;
    align-items: center;
    gap: 8px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    padding: 8px 16px;
    color: #ffffff;
    font-size: 0.9rem;
    font-weight: 500;
}

.message-counter i {
    color: #10a37f;
    font-size: 1rem;
}

.message-counter.warning {
    background: rgba(255, 167, 38, 0.2);
    border-color: rgba(255, 167, 38, 0.3);
    color: #ffa726;
}

.message-counter.danger {
    background: rgba(239, 68, 68, 0.2);
    border-color: rgba(239, 68, 68, 0.3);
    color: #ef4444;
}

.upgrade-btn {
    background: linear-gradient(135deg, #10a37f, #0d8f6f);
    border: none;
    color: #ffffff;
    padding: 8px 16px;
    border-radius: 6px;
    cursor: pointer;
    font-family: inherit;
    font-size: 0.9rem;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.2s ease;
}

.upgrade-btn:hover {
    background: linear-gradient(135deg, #0d8f6f, #0a7a5e);
    transform: translateY(-1px);
}

.home-dashboard-btn {
    background: linear-gradient(135deg, #3b82f6, #2563eb);
    border: none;
    color: #ffffff;
    padding: 8px 16px;
    border-radius: 8px;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 6px;
    transition: all 0.2s ease;
    white-space: nowrap;
}

.home-dashboard-btn:hover {
    background: linear-gradient(135deg, #2563eb, #1d4ed8);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.home-dashboard-btn i {
    font-size: 0.875rem;
}

/* Welcome Screen */
.welcome-screen {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 24px;
}

.welcome-content {
    max-width: 768px;
    width: 100%;
    text-align: center;
}

#welcomeTitle {
    color: #ffffff;
    font-size: 2rem;
    font-weight: 600;
    margin-bottom: 32px;
}

/* Chat Input Container */
.chat-input-container {
    position: relative;
    max-width: 100%;
}

.chat-input-wrapper {
    background: #2f2f2f;
    border: 1px solid #3f3f3f;
    border-radius: 24px;
    padding: 12px 16px;
    display: flex;
    align-items: flex-end;
    gap: 8px;
    transition: all 0.2s ease;
}

.chat-input-wrapper:focus-within {
    border-color: #10a37f;
    box-shadow: 0 0 0 2px rgba(16, 163, 127, 0.2);
}

.chat-input-wrapper textarea {
    flex: 1;
    background: none;
    border: none;
    color: #ffffff;
    font-family: inherit;
    font-size: 1rem;
    resize: none;
    outline: none;
    min-height: 24px;
    max-height: 120px;
    line-height: 1.5;
}

.chat-input-wrapper textarea::placeholder {
    color: #8e8ea0;
}

.input-actions {
    display: flex;
    align-items: center;
    gap: 4px;
}

.action-btn {
    background: none;
    border: none;
    color: #8e8ea0;
    cursor: pointer;
    padding: 8px;
    border-radius: 6px;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 0.9rem;
}

.action-btn:hover {
    background: #3f3f3f;
    color: #ffffff;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(255, 255, 255, 0.2);
}

.action-btn:active {
    transform: translateY(0px);
    box-shadow: 0 2px 8px rgba(255, 255, 255, 0.1);
}

.action-btn:last-child {
    background: #10a37f;
    color: #ffffff;
}

.action-btn:last-child:hover {
    background: #0d8f6f;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(16, 163, 127, 0.4);
}

/* Special styling for tools button */
.action-btn.tools-btn {
    background: linear-gradient(45deg, #10a37f, #0d8f6f) !important;
    color: white !important;
    position: relative;
    overflow: hidden;
    border: 2px solid rgba(16, 163, 127, 0.3);
}

.action-btn.tools-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    transition: left 0.6s ease;
}

.action-btn.tools-btn:hover::before {
    left: 100%;
}

.action-btn.tools-btn:hover {
    background: linear-gradient(45deg, #0d8f6f, #0a7a5e) !important;
    transform: translateY(-3px) scale(1.08);
    box-shadow: 0 8px 30px rgba(16, 163, 127, 0.7);
    border-color: rgba(16, 163, 127, 0.6);
    animation: toolsGlowPulse 1s ease infinite alternate;
}

.action-btn.tools-btn:active {
    transform: translateY(-1px) scale(1.05);
    box-shadow: 0 6px 20px rgba(16, 163, 127, 0.8);
}

@keyframes toolsGlowPulse {
    0% {
        box-shadow: 0 8px 30px rgba(16, 163, 127, 0.7);
        filter: brightness(1);
    }
    100% {
        box-shadow: 0 12px 40px rgba(16, 163, 127, 0.9);
        filter: brightness(1.1);
    }
}

/* Enhanced tools button with special effects */
.tools-btn {
    position: relative;
    background: linear-gradient(45deg, #10a37f, #0d8f6f) !important;
    color: white !important;
    border: 2px solid rgba(16, 163, 127, 0.3) !important;
    overflow: hidden;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.tools-btn::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: all 0.6s ease;
}

.tools-btn:hover::after {
    width: 200px;
    height: 200px;
}

.tools-btn:hover {
    background: linear-gradient(45deg, #0d8f6f, #0a7a5e) !important;
    transform: translateY(-4px) scale(1.1);
    box-shadow:
        0 10px 35px rgba(16, 163, 127, 0.8),
        0 0 20px rgba(16, 163, 127, 0.5),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    border-color: rgba(16, 163, 127, 0.8) !important;
    animation: toolsGlowPulse 1.5s ease infinite alternate;
}

.tools-btn:active {
    transform: translateY(-2px) scale(1.08);
    box-shadow:
        0 8px 25px rgba(16, 163, 127, 0.9),
        0 0 15px rgba(16, 163, 127, 0.6);
}

/* Tools button icon animation */
.tools-btn i {
    transition: all 0.3s ease;
}

.tools-btn:hover i {
    transform: rotate(15deg) scale(1.1);
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
}

/* Tools button text (if present) */
.tools-btn span {
    font-weight: 600;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;
}

.tools-btn:hover span {
    text-shadow: 0 0 8px rgba(255, 255, 255, 0.4);
}

.tools-btn {
    padding: 6px 12px;
    border-radius: 12px;
    border: 1px solid #3f3f3f;
}

/* Chat Interface */
.chat-interface {
    flex: 1;
    display: flex;
    flex-direction: column;
    position: relative;
    overflow: hidden;
}

.chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: 24px;
    padding-bottom: 120px; /* Space for fixed input */
    scroll-behavior: smooth;
}

.chat-input-container {
    position: fixed;
    bottom: 0;
    left: 260px; /* Sidebar width */
    right: 0;
    background: #212121;
    border-top: 1px solid #2f2f2f;
    padding: 20px 24px;
    z-index: 100;
}

/* Scrollbar Styling */
::-webkit-scrollbar {
    width: 6px;
}

::-webkit-scrollbar-track {
    background: transparent;
}

::-webkit-scrollbar-thumb {
    background: #3f3f3f;
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: #4f4f4f;
}

/* Modal Styles */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2000;
    animation: fadeIn 0.2s ease;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.modal-content {
    background: #2f2f2f;
    border-radius: 12px;
    width: 90%;
    max-width: 600px;
    max-height: 80vh;
    overflow: hidden;
    animation: slideIn 0.3s ease;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-20px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.modal-header {
    padding: 20px 24px;
    border-bottom: 1px solid #3f3f3f;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.modal-header h2 {
    color: #ffffff;
    font-size: 1.3rem;
    font-weight: 600;
}

.modal-close {
    background: none;
    border: none;
    color: #8e8ea0;
    cursor: pointer;
    padding: 8px;
    border-radius: 6px;
    transition: all 0.2s ease;
}

.modal-close:hover {
    background: #3f3f3f;
    color: #ffffff;
}

.modal-body {
    padding: 24px;
    overflow-y: auto;
    max-height: calc(80vh - 80px);
}

/* Payment Modal Styles */
.payment-modal {
    max-width: 900px;
}

.payment-content {
    text-align: center;
}

.limit-reached {
    background: rgba(255, 167, 38, 0.1);
    border: 1px solid rgba(255, 167, 38, 0.3);
    border-radius: 12px;
    padding: 2rem;
    margin-bottom: 2rem;
}

.limit-reached i {
    font-size: 3rem;
    color: #ffa726;
    margin-bottom: 1rem;
}

.limit-reached h3 {
    color: #ffffff;
    font-size: 1.5rem;
    margin-bottom: 1rem;
}

.limit-reached p {
    color: #cccccc;
    font-size: 1rem;
    line-height: 1.6;
}

.pricing-plans {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.plan-card {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 16px;
    padding: 2rem;
    position: relative;
    transition: all 0.3s ease;
}

.plan-card:hover {
    transform: translateY(-5px);
    border-color: rgba(255, 255, 255, 0.2);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.plan-card.recommended {
    border-color: #10a37f;
    background: rgba(16, 163, 127, 0.1);
}

.plan-badge {
    position: absolute;
    top: -10px;
    left: 50%;
    transform: translateX(-50%);
    background: linear-gradient(135deg, #10a37f, #0d8f6f);
    color: white;
    padding: 5px 15px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
}

.plan-card h4 {
    color: #ffffff;
    font-size: 1.3rem;
    margin-bottom: 1rem;
    font-weight: 600;
}

.plan-price {
    color: #10a37f;
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
}

.plan-price span {
    font-size: 1rem;
    color: #8e8ea0;
    font-weight: 400;
}

.plan-features {
    list-style: none;
    padding: 0;
    margin-bottom: 2rem;
    text-align: left;
}

.plan-features li {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 0.5rem 0;
    color: #cccccc;
    font-size: 0.9rem;
}

.plan-features .fas.fa-check {
    color: #10a37f;
}

.plan-features .fas.fa-times {
    color: #8e8ea0;
}

.plan-btn {
    width: 100%;
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-family: inherit;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    transition: all 0.3s ease;
}

.premium-btn {
    background: linear-gradient(135deg, #10a37f, #0d8f6f);
    color: white;
}

.premium-btn:hover {
    background: linear-gradient(135deg, #0d8f6f, #0a7a5e);
    transform: translateY(-2px);
}

.basic-btn {
    background: linear-gradient(135deg, #3b82f6, #2563eb);
    color: white;
}

.basic-btn:hover {
    background: linear-gradient(135deg, #2563eb, #1d4ed8);
    transform: translateY(-2px);
}

.daily-btn {
    background: linear-gradient(135deg, #f59e0b, #d97706);
    color: white;
}

.daily-btn:hover {
    background: linear-gradient(135deg, #d97706, #b45309);
    transform: translateY(-2px);
}

.payment-footer {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding-top: 1.5rem;
    color: #8e8ea0;
    font-size: 0.9rem;
}

.payment-footer p {
    margin: 0.5rem 0;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.payment-footer i {
    color: #10a37f;
}

/* Payment Form Modal Styles */
.payment-form-modal {
    max-width: 600px;
}

.payment-form-content {
    max-height: 70vh;
    overflow-y: auto;
}

.plan-summary {
    background: rgba(16, 163, 127, 0.1);
    border: 1px solid rgba(16, 163, 127, 0.3);
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    text-align: center;
}

.selected-plan h3 {
    color: #10a37f;
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
}

.plan-price-large {
    font-size: 2rem;
    font-weight: 700;
    color: #ffffff;
    margin-bottom: 1rem;
}

.plan-benefits {
    list-style: none;
    padding: 0;
    margin: 0;
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    justify-content: center;
}

.plan-benefits li {
    background: rgba(255, 255, 255, 0.1);
    padding: 5px 12px;
    border-radius: 15px;
    font-size: 0.8rem;
    color: #cccccc;
}

.payment-form {
    background: rgba(255, 255, 255, 0.02);
    border-radius: 12px;
    padding: 0;
}

.form-section {
    padding: 1.5rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.form-section:last-child {
    border-bottom: none;
}

.form-section h4 {
    color: #ffffff;
    font-size: 1.1rem;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 8px;
}

.form-section h4 i {
    color: #10a37f;
}

.form-group {
    margin-bottom: 1rem;
    position: relative;
}

.form-group label {
    display: block;
    color: #cccccc;
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
    font-weight: 500;
}

.form-group input,
.form-group select {
    width: 100%;
    padding: 12px 16px;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    color: #ffffff;
    font-family: inherit;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.form-group input:focus,
.form-group select:focus {
    outline: none;
    border-color: #10a37f;
    background: rgba(255, 255, 255, 0.08);
    box-shadow: 0 0 0 3px rgba(16, 163, 127, 0.1);
}

.form-group input::placeholder {
    color: #8e8ea0;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.card-icons {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    display: flex;
    gap: 5px;
    margin-top: 1.5rem;
}

.card-icons i {
    font-size: 1.2rem;
    color: #8e8ea0;
}

.payment-security {
    text-align: center;
}

.security-badges {
    display: flex;
    justify-content: center;
    gap: 2rem;
    flex-wrap: wrap;
}

.security-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 5px;
    color: #8e8ea0;
    font-size: 0.8rem;
}

.security-item i {
    font-size: 1.5rem;
    color: #10a37f;
}

.form-actions {
    display: flex;
    gap: 1rem;
    justify-content: space-between;
    padding: 1.5rem;
    background: rgba(255, 255, 255, 0.02);
    border-radius: 0 0 12px 12px;
}

.btn-primary,
.btn-secondary {
    flex: 1;
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-family: inherit;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    transition: all 0.3s ease;
}

.btn-primary {
    background: linear-gradient(135deg, #10a37f, #0d8f6f);
    color: white;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #0d8f6f, #0a7a5e);
    transform: translateY(-2px);
}

.btn-secondary {
    background: rgba(255, 255, 255, 0.1);
    color: #cccccc;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.btn-secondary:hover {
    background: rgba(255, 255, 255, 0.15);
    color: #ffffff;
}

/* Typing Indicator */
.typing-indicator {
    position: fixed;
    bottom: 120px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 1000;
    animation: slideUp 0.3s ease;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateX(-50%) translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateX(-50%) translateY(0);
    }
}

.typing-animation {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 12px 16px;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 18px;
    margin-bottom: 5px;
}

.typing-dot {
    width: 8px;
    height: 8px;
    background: #10a37f;
    border-radius: 50%;
    animation: typingPulse 1.4s ease-in-out infinite;
}

.typing-dot:nth-child(1) {
    animation-delay: 0s;
}

.typing-dot:nth-child(2) {
    animation-delay: 0.2s;
}

.typing-dot:nth-child(3) {
    animation-delay: 0.4s;
}

@keyframes typingPulse {
    0%, 60%, 100% {
        transform: scale(0.8);
        opacity: 0.5;
    }
    30% {
        transform: scale(1.2);
        opacity: 1;
    }
}

.typing-text {
    font-size: 0.8rem;
    color: rgba(255, 255, 255, 0.6);
    font-style: italic;
    text-align: center;
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.95), rgba(33, 33, 33, 0.95));
    backdrop-filter: blur(10px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 3000;
}

.loading-spinner {
    text-align: center;
    color: #ffffff;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 3rem 2rem;
    backdrop-filter: blur(20px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    animation: fadeInUp 0.5s ease;
    min-width: 300px;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.loading-icon-container {
    position: relative;
    width: 100px;
    height: 100px;
    margin: 0 auto 2rem;
}

.loading-icon {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    object-fit: cover;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    box-shadow: 0 8px 32px rgba(16, 163, 127, 0.3);
    border: 3px solid #10a37f;
}

.loading-ring {
    position: absolute;
    top: 0;
    left: 0;
    width: 100px;
    height: 100px;
    border: 3px solid transparent;
    border-top: 3px solid #10a37f;
    border-right: 3px solid #10a37f;
    border-radius: 50%;
    animation: spin 2s linear infinite;
}

.loading-ring:nth-child(2) {
    width: 90px;
    height: 90px;
    top: 5px;
    left: 5px;
    border-top: 2px solid #4ecdc4;
    border-right: 2px solid #4ecdc4;
    animation: spin 1.5s linear infinite reverse;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-text {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    background: linear-gradient(45deg, #10a37f, #4ecdc4);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.loading-subtitle {
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.7);
    margin-bottom: 1.5rem;
}

.loading-dots {
    display: flex;
    justify-content: center;
    gap: 8px;
}

.loading-dot {
    width: 8px;
    height: 8px;
    background: #10a37f;
    border-radius: 50%;
    animation: pulse 1.5s ease-in-out infinite;
}

.loading-dot:nth-child(1) {
    animation-delay: 0s;
}

.loading-dot:nth-child(2) {
    animation-delay: 0.2s;
}

.loading-dot:nth-child(3) {
    animation-delay: 0.4s;
}

@keyframes pulse {
    0%, 80%, 100% {
        transform: scale(0.8);
        opacity: 0.5;
    }
    40% {
        transform: scale(1.2);
        opacity: 1;
    }
}

/* Toast Notifications */
.toast-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 4000;
}

.toast {
    background: #2f2f2f;
    border: 1px solid #3f3f3f;
    border-radius: 8px;
    padding: 12px 16px;
    margin-bottom: 8px;
    color: #ffffff;
    min-width: 300px;
    animation: slideInRight 0.3s ease;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(100%);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.toast.success {
    border-left: 4px solid #10a37f;
}

.toast.error {
    border-left: 4px solid #ef4444;
}

.toast.warning {
    border-left: 4px solid #f59e0b;
}

.toast.info {
    border-left: 4px solid #3b82f6;
}

/* Message Styles */
.message {
    display: flex;
    gap: 12px;
    margin-bottom: 24px;
    animation: messageSlideIn 0.3s ease;
}

@keyframes messageSlideIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.message.user {
    flex-direction: row-reverse;
}

.message-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    overflow: hidden;
}

.message.user .message-avatar {
    background: #10a37f;
}

.message.ai .message-avatar {
    background: #ef4444;
}

.message-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 50%;
}

.ai-avatar {
    border: 2px solid #10a37f !important;
    box-shadow: 0 2px 8px rgba(16, 163, 127, 0.3);
}

/* Code Block Styling */
.code-block {
    background: #1a1a1a;
    border: 1px solid #333;
    border-radius: 8px;
    margin: 12px 0;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.code-header {
    background: #2d2d2d;
    padding: 8px 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #333;
}

.code-language {
    color: #10a37f;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.code-actions {
    display: flex;
    gap: 8px;
}

.code-btn {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: #ffffff;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.7rem;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 4px;
    transition: all 0.2s ease;
}

.code-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.3);
}

.code-content {
    padding: 16px;
    margin: 0;
    background: #1a1a1a;
    color: #ffffff;
    font-family: 'Courier New', 'Monaco', 'Menlo', monospace;
    font-size: 14px;
    line-height: 1.5;
    overflow-x: auto;
    white-space: pre-wrap;
}

.code-content code {
    background: none;
    padding: 0;
    border-radius: 0;
    color: inherit;
    font-family: inherit;
}

/* Inline Code */
.inline-code {
    background: rgba(255, 255, 255, 0.1);
    color: #10a37f;
    padding: 2px 6px;
    border-radius: 4px;
    font-family: 'Courier New', 'Monaco', 'Menlo', monospace;
    font-size: 0.9em;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Enhanced Message Text Styling */
.message-text {
    line-height: 1.6;
}

.message-text ul {
    margin: 8px 0;
    padding-left: 20px;
}

.message-text li {
    margin: 4px 0;
    color: #cccccc;
}

.message-text strong {
    color: #ffffff;
    font-weight: 600;
}

.message-text em {
    color: #10a37f;
    font-style: italic;
}

/* Edit Actions */
.edit-actions {
    margin-top: 8px;
    display: flex;
    gap: 8px;
}

.save-btn {
    background: #10a37f !important;
}

.save-btn:hover {
    background: #0d8f6f !important;
}

.cancel-btn {
    background: #ef4444 !important;
}

.cancel-btn:hover {
    background: #dc2626 !important;
}

/* Chat Item Actions */
.chat-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 16px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    margin-bottom: 4px;
    position: relative;
}

.chat-content {
    flex: 1;
    min-width: 0;
}

.chat-actions {
    opacity: 0;
    transition: opacity 0.2s ease;
}

.chat-item:hover .chat-actions {
    opacity: 1;
}

.chat-action-btn {
    background: none;
    border: none;
    color: rgba(255, 255, 255, 0.6);
    padding: 8px;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.chat-action-btn:hover {
    background: rgba(255, 255, 255, 0.1);
    color: #ffffff;
}

/* Context Menu */
.context-menu {
    position: fixed;
    background: rgba(33, 33, 33, 0.95);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    padding: 8px 0;
    min-width: 180px;
    z-index: 2000;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    animation: contextMenuFadeIn 0.2s ease;
}

@keyframes contextMenuFadeIn {
    from {
        opacity: 0;
        transform: scale(0.95) translateY(-10px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

.context-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 10px 16px;
    color: #ffffff;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 0.9rem;
}

.context-item:hover {
    background: rgba(255, 255, 255, 0.1);
}

.context-item.danger {
    color: #ef4444;
}

.context-item.danger:hover {
    background: rgba(239, 68, 68, 0.1);
}

.context-divider {
    height: 1px;
    background: rgba(255, 255, 255, 0.1);
    margin: 8px 0;
}

/* Modal Enhancements */
.modal-footer {
    display: flex;
    gap: 12px;
    justify-content: flex-end;
    padding: 1.5rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    background: rgba(255, 255, 255, 0.02);
}

.form-group {
    margin-bottom: 1rem;
}

.form-group label {
    display: block;
    color: #cccccc;
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
    font-weight: 500;
}

.form-group input {
    width: 100%;
    padding: 12px 16px;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    color: #ffffff;
    font-family: inherit;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.form-group input:focus {
    outline: none;
    border-color: #10a37f;
    background: rgba(255, 255, 255, 0.08);
    box-shadow: 0 0 0 3px rgba(16, 163, 127, 0.1);
}

.form-group input::placeholder {
    color: #8e8ea0;
}

.message-avatar i {
    color: #ffffff;
    font-size: 1rem;
}

.message-content {
    background: #2f2f2f;
    border-radius: 12px;
    padding: 12px 16px;
    max-width: 70%;
    border: 1px solid #3f3f3f;
}

.message.user .message-content {
    background: #10a37f;
    border-color: #0d8f6f;
}

.message-text {
    color: #ffffff;
    line-height: 1.6;
    word-wrap: break-word;
}

.message-time {
    font-size: 0.8rem;
    color: #8e8ea0;
    margin-top: 8px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .sidebar {
        position: fixed;
        left: -260px;
        height: 100vh;
        z-index: 1001;
    }

    .sidebar.open {
        left: 0;
    }

    .main-content {
        width: 100%;
    }

    .mobile-menu-btn {
        display: block;
    }

    .welcome-content {
        padding: 16px;
    }

    #welcomeTitle {
        font-size: 1.5rem;
    }

    .modal-content {
        width: 95%;
        margin: 20px;
    }

    .toast {
        min-width: 280px;
        margin-right: 10px;
    }

    .message-content {
        max-width: 85%;
    }

    /* Fixed input for mobile */
    .chat-input-container {
        left: 0;
        padding: 15px;
    }

    .chat-messages {
        padding: 15px;
        padding-bottom: 100px;
    }

    /* Payment form mobile styles */
    .payment-form-modal {
        max-width: 95%;
        margin: 10px;
    }

    .form-row {
        grid-template-columns: 1fr;
    }

    .security-badges {
        gap: 1rem;
    }

    .form-actions {
        flex-direction: column;
    }

    .plan-benefits {
        flex-direction: column;
        align-items: center;
    }
}

/* Coming Soon Modal */
.coming-soon-modal {
    max-width: 500px;
    text-align: center;
}

.coming-soon-content {
    padding: 2rem 1rem;
}

.coming-soon-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(45deg, #ff6b6b, #ee5a52);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    font-size: 2rem;
    color: white;
    animation: rocketBounce 2s infinite;
}

@keyframes rocketBounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

.coming-soon-content h3 {
    color: #ffffff;
    font-size: 1.5rem;
    margin-bottom: 1rem;
}

.coming-soon-content p {
    color: #cccccc;
    font-size: 1rem;
    line-height: 1.6;
    margin-bottom: 2rem;
}

.feature-highlights {
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin-bottom: 2rem;
    text-align: left;
}

.highlight-item {
    display: flex;
    align-items: center;
    gap: 12px;
    color: #cccccc;
    font-size: 0.9rem;
}

.highlight-item i {
    color: #10a37f;
    font-size: 1rem;
}

.coming-soon-cta {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    padding: 1.5rem;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.coming-soon-cta p {
    color: #ffffff;
    font-weight: 600;
    margin-bottom: 1rem;
}

.coming-soon-cta .btn-primary {
    background: linear-gradient(45deg, #10a37f, #0d8f6f);
    border: none;
    padding: 12px 24px;
    border-radius: 8px;
    color: white;
    font-weight: 600;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    margin: 0 auto;
    transition: all 0.3s ease;
}

.coming-soon-cta .btn-primary:hover {
    background: linear-gradient(45deg, #0d8f6f, #0a7a5e);
    transform: translateY(-2px);
}

/* Help Modal Styles */
.help-modal {
    max-width: 800px;
    max-height: 80vh;
}

.help-content {
    display: flex;
    flex-direction: column;
    height: 100%;
}

.help-tabs {
    display: flex;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    margin-bottom: 2rem;
    overflow-x: auto;
}

.help-tab {
    background: none;
    border: none;
    color: rgba(255, 255, 255, 0.6);
    padding: 12px 20px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    white-space: nowrap;
    border-bottom: 2px solid transparent;
}

.help-tab:hover {
    color: #ffffff;
    background: rgba(255, 255, 255, 0.05);
}

.help-tab.active {
    color: #10a37f;
    border-bottom-color: #10a37f;
}

.help-tab-content {
    flex: 1;
    overflow-y: auto;
    max-height: 500px;
}

.help-section {
    display: none;
}

.help-section.active {
    display: block;
}

.help-section h3 {
    color: #ffffff;
    font-size: 1.3rem;
    margin-bottom: 1.5rem;
}

.help-steps {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.help-step {
    display: flex;
    gap: 1rem;
    align-items: flex-start;
}

.step-number {
    width: 30px;
    height: 30px;
    background: linear-gradient(45deg, #10a37f, #0d8f6f);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 0.9rem;
    flex-shrink: 0;
}

.step-content h4 {
    color: #ffffff;
    margin-bottom: 0.5rem;
}

.step-content p {
    color: #cccccc;
    line-height: 1.5;
}

.feature-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
}

.feature-item {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    padding: 1.5rem;
    text-align: center;
}

.feature-item i {
    font-size: 2rem;
    color: #10a37f;
    margin-bottom: 1rem;
}

.feature-item h4 {
    color: #ffffff;
    margin-bottom: 0.5rem;
}

.feature-item p {
    color: #cccccc;
    font-size: 0.9rem;
}

.shortcuts-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.shortcut-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
}

.shortcut-item kbd {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 4px;
    padding: 4px 8px;
    font-family: monospace;
    font-size: 0.8rem;
    color: #ffffff;
}

.shortcut-item span {
    color: #cccccc;
}

.faq-list {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.faq-item {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    padding: 1.5rem;
}

.faq-item h4 {
    color: #ffffff;
    margin-bottom: 0.5rem;
}

.faq-item p {
    color: #cccccc;
    line-height: 1.5;
}

.contact-options {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.contact-item {
    display: flex;
    gap: 1rem;
    align-items: flex-start;
    padding: 1.5rem;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 8px;
}

.contact-item i {
    font-size: 1.5rem;
    color: #10a37f;
    margin-top: 0.25rem;
}

.contact-item h4 {
    color: #ffffff;
    margin-bottom: 0.5rem;
}

.contact-item p {
    color: #cccccc;
    margin-bottom: 0.5rem;
}

.contact-item small {
    color: rgba(255, 255, 255, 0.6);
    font-size: 0.8rem;
}

/* Customize Modal Styles */
.customize-modal {
    max-width: 700px;
    max-height: 80vh;
}

.customize-content {
    max-height: 500px;
    overflow-y: auto;
}

.customize-section {
    margin-bottom: 2rem;
    padding-bottom: 2rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.customize-section:last-child {
    border-bottom: none;
}

.customize-section h3 {
    color: #ffffff;
    font-size: 1.2rem;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 10px;
}

.customize-section h3 i {
    color: #10a37f;
}

.profile-settings {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1.5rem;
}

.profile-picture-section {
    display: flex;
    align-items: center;
    gap: 2rem;
}

.current-avatar {
    position: relative;
    width: 80px;
    height: 80px;
    border-radius: 50%;
    overflow: hidden;
    cursor: pointer;
    transition: all 0.3s ease;
}

.current-avatar:hover {
    transform: scale(1.05);
}

.current-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.avatar-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.current-avatar:hover .avatar-overlay {
    opacity: 1;
}

.avatar-overlay i {
    color: white;
    font-size: 1.5rem;
}

.avatar-controls h4 {
    color: #ffffff;
    margin-bottom: 0.5rem;
}

.avatar-controls p {
    color: #cccccc;
    font-size: 0.9rem;
    margin-bottom: 1rem;
}

.avatar-controls {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.theme-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 1rem;
}

.theme-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.05);
    border: 2px solid transparent;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.theme-item:hover {
    background: rgba(255, 255, 255, 0.1);
}

.theme-item.active {
    border-color: #10a37f;
    background: rgba(16, 163, 127, 0.1);
}

.theme-preview {
    width: 60px;
    height: 40px;
    border-radius: 4px;
    position: relative;
    overflow: hidden;
}

.dark-theme {
    background: linear-gradient(45deg, #1a1a1a, #2d2d2d);
}

.light-theme {
    background: linear-gradient(45deg, #ffffff, #f5f5f5);
}

.auto-theme {
    background: linear-gradient(45deg, #1a1a1a 50%, #ffffff 50%);
}

.theme-item span {
    color: #cccccc;
    font-size: 0.9rem;
}

.theme-item.active span {
    color: #10a37f;
    font-weight: 600;
}

.preferences-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.preference-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 8px;
}

.preference-info h4 {
    color: #ffffff;
    margin-bottom: 0.25rem;
}

.preference-info p {
    color: #cccccc;
    font-size: 0.9rem;
}

.toggle-switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
}

.toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.2);
    transition: 0.4s;
    border-radius: 24px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: 0.4s;
    border-radius: 50%;
}

input:checked + .slider {
    background-color: #10a37f;
}

input:checked + .slider:before {
    transform: translateX(26px);
}

/* AI Tools Styles */
.tools-btn {
    background: linear-gradient(45deg, #10a37f, #0d8f6f);
    color: white;
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(16, 163, 127, 0.3);
}

.tools-btn:hover {
    background: linear-gradient(45deg, #0d8f6f, #0a7a5e);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(16, 163, 127, 0.6);
    animation: toolsGlow 0.3s ease;
}

.tools-btn:active {
    transform: translateY(0px);
    box-shadow: 0 4px 15px rgba(16, 163, 127, 0.8);
}

@keyframes toolsGlow {
    0% {
        box-shadow: 0 2px 8px rgba(16, 163, 127, 0.3);
    }
    50% {
        box-shadow: 0 8px 30px rgba(16, 163, 127, 0.8);
        transform: translateY(-3px) scale(1.05);
    }
    100% {
        box-shadow: 0 8px 25px rgba(16, 163, 127, 0.6);
        transform: translateY(-2px);
    }
}

.ai-tools-modal {
    max-width: 600px;
    max-height: 80vh;
}

.tools-grid {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-bottom: 2rem;
}

.tool-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1.5rem;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.tool-item:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.tool-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(45deg, #10a37f, #0d8f6f);
    transform: scaleY(0);
    transition: transform 0.3s ease;
}

.tool-item:hover::before {
    transform: scaleY(1);
}

.tool-icon {
    width: 50px;
    height: 50px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    flex-shrink: 0;
    transition: all 0.3s ease;
}

.tool-content {
    flex: 1;
}

.tool-content h3 {
    color: #ffffff;
    font-size: 1.1rem;
    margin-bottom: 0.5rem;
    font-weight: 600;
}

.tool-content p {
    color: #cccccc;
    font-size: 0.9rem;
    line-height: 1.4;
    margin: 0;
}

.tool-arrow {
    color: rgba(255, 255, 255, 0.4);
    font-size: 1rem;
    transition: all 0.3s ease;
}

.tool-item:hover .tool-icon {
    background: rgba(16, 163, 127, 0.2);
    transform: scale(1.1);
}

.tool-item:hover .tool-arrow {
    color: #10a37f;
    transform: translateX(5px);
}

.tools-footer {
    text-align: center;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.tools-footer p {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.9rem;
    margin: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.tools-footer i {
    color: #10a37f;
}

/* Tool Usage Modal */
.tool-usage-modal {
    max-width: 700px;
    max-height: 80vh;
}

.tool-usage-content {
    max-height: 400px;
    overflow-y: auto;
    padding: 1rem 0;
}

.tool-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.tool-form-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.tool-form-group label {
    color: #cccccc;
    font-size: 0.9rem;
    font-weight: 500;
}

.tool-form-group input,
.tool-form-group textarea,
.tool-form-group select {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    color: #ffffff;
    padding: 12px 16px;
    font-family: inherit;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.tool-form-group input:focus,
.tool-form-group textarea:focus,
.tool-form-group select:focus {
    outline: none;
    border-color: #10a37f;
    background: rgba(255, 255, 255, 0.08);
    box-shadow: 0 0 0 3px rgba(16, 163, 127, 0.1);
}

.tool-form-group textarea {
    min-height: 100px;
    resize: vertical;
}

.tool-preview {
    background: rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    padding: 1rem;
    margin-top: 1rem;
}

.tool-preview h4 {
    color: #10a37f;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.tool-preview p {
    color: #cccccc;
    font-size: 0.9rem;
    line-height: 1.5;
    margin: 0;
}

/* Code Edit Modal */
.code-edit-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    z-index: 10000;
    backdrop-filter: blur(5px);
}

.code-edit-modal.active {
    display: flex;
    align-items: center;
    justify-content: center;
}

.code-edit-content {
    background: #1e1e1e;
    border-radius: 12px;
    padding: 0;
    width: 90%;
    max-width: 900px;
    max-height: 85vh;
    overflow: hidden;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
    border: 1px solid #333;
}

.code-edit-header {
    background: linear-gradient(135deg, #2d2d2d 0%, #3a3a3a 100%);
    padding: 15px 20px;
    border-bottom: 1px solid #333;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.code-edit-title {
    color: #fff;
    font-size: 16px;
    font-weight: 600;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.code-edit-title i {
    color: #007bff;
}

.code-edit-close {
    background: none;
    border: none;
    color: #999;
    font-size: 20px;
    cursor: pointer;
    padding: 8px;
    border-radius: 6px;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
}

.code-edit-close:hover {
    color: #fff;
    background: #444;
    transform: scale(1.1);
}

.code-edit-body {
    padding: 20px;
    max-height: 65vh;
    overflow-y: auto;
}

.code-preview-section {
    margin-bottom: 20px;
}

.code-preview-label {
    color: #ccc;
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.code-preview-label i {
    color: #28a745;
}

.code-preview-container {
    background: #2d2d2d;
    border: 1px solid #444;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 15px;
    position: relative;
}

.code-preview-content {
    font-family: 'Fira Code', monospace;
    font-size: 14px;
    line-height: 1.6;
    color: #f8f8f2;
    white-space: pre-wrap;
    word-wrap: break-word;
    max-height: 200px;
    overflow-y: auto;
}

.code-edit-textarea {
    width: 100%;
    height: 300px;
    background: #1e1e1e;
    border: 1px solid #333;
    border-radius: 8px;
    padding: 15px;
    color: #fff;
    font-family: 'Fira Code', monospace;
    font-size: 14px;
    line-height: 1.5;
    resize: vertical;
    outline: none;
    transition: all 0.2s ease;
}

.code-edit-textarea:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.code-edit-actions {
    padding: 15px 20px;
    border-top: 1px solid #333;
    display: flex;
    gap: 10px;
    justify-content: space-between;
    align-items: center;
}

.code-edit-actions-left {
    display: flex;
    gap: 10px;
}

.code-edit-actions-right {
    display: flex;
    gap: 10px;
}

.code-edit-btn {
    padding: 10px 16px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 6px;
}

.code-edit-btn.primary {
    background: #007bff;
    color: white;
}

.code-edit-btn.primary:hover {
    background: #0056b3;
    transform: translateY(-1px);
}

.code-edit-btn.secondary {
    background: #6c757d;
    color: white;
}

.code-edit-btn.secondary:hover {
    background: #545b62;
    transform: translateY(-1px);
}

.code-edit-btn.success {
    background: #28a745;
    color: white;
}

.code-edit-btn.success:hover {
    background: #1e7e34;
    transform: translateY(-1px);
}

.code-edit-btn.info {
    background: #17a2b8;
    color: white;
}

.code-edit-btn.info:hover {
    background: #117a8b;
    transform: translateY(-1px);
}
