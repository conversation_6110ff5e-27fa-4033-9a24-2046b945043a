<?php
/**
 * Tincada AI API Handler
 * PHP script to handle API requests and store data in database
 */

require_once 'config.php';

/**
 * Chat API Handler
 */
class ChatHandler {
    private $db;
    
    public function __construct() {
        $this->db = Database::getInstance();
    }
    
    /**
     * Handle chat request and store in database
     */
    public function handleChatRequest($user_id, $message, $conversation_id = null) {
        try {
            // Create user if not exists
            createUser($user_id);
            updateUserActivity($user_id);
            
            // Create conversation if not provided
            if (!$conversation_id) {
                $conversation_id = generateConversationId();
                $this->createConversation($user_id, $conversation_id);
            }
            
            // Store user message
            $this->storeMessage($conversation_id, $user_id, 'user', $message);
            
            // Send request to Tincada AI API
            $ai_response = $this->sendToTincadaAPI($message, $user_id);
            
            if ($ai_response) {
                // Store AI response
                $this->storeMessage(
                    $conversation_id, 
                    $user_id, 
                    'ai', 
                    $ai_response['response'],
                    $ai_response['tokens_used'] ?? 0,
                    $ai_response['source'] ?? 'openai'
                );
                
                // Update user statistics
                $this->updateUserStats($user_id, $ai_response['tokens_used'] ?? 0);
                
                // Log API usage
                $this->logAPIUsage($user_id, 'chat', $ai_response['tokens_used'] ?? 0, true);
                
                return [
                    'success' => true,
                    'conversation_id' => $conversation_id,
                    'response' => $ai_response['response'],
                    'tokens_used' => $ai_response['tokens_used'] ?? 0,
                    'source' => $ai_response['source'] ?? 'openai'
                ];
            } else {
                $this->logAPIUsage($user_id, 'chat', 0, false, 'API request failed');
                return [
                    'success' => false,
                    'error' => 'Failed to get AI response'
                ];
            }
            
        } catch (Exception $e) {
            logActivity('error', 'Chat handler error: ' . $e->getMessage(), $user_id);
            return [
                'success' => false,
                'error' => 'Internal server error'
            ];
        }
    }
    
    /**
     * Send request to Tincada AI API
     */
    private function sendToTincadaAPI($message, $user_id) {
        $url = TINCADA_API_URL . '/chat';
        $data = [
            'message' => $message,
            'user_id' => $user_id,
            'chat_history' => $this->getChatHistory($user_id)
        ];
        
        $options = [
            'http' => [
                'header' => "Content-type: application/json\r\n",
                'method' => 'POST',
                'content' => json_encode($data),
                'timeout' => API_TIMEOUT
            ]
        ];
        
        $context = stream_context_create($options);
        $result = file_get_contents($url, false, $context);
        
        if ($result === FALSE) {
            return null;
        }
        
        return json_decode($result, true);
    }
    
    /**
     * Get recent chat history for context
     */
    private function getChatHistory($user_id, $limit = 10) {
        $sql = "SELECT message_type, message_content 
                FROM chat_messages 
                WHERE user_id = ? 
                ORDER BY created_at DESC 
                LIMIT ?";
        
        $messages = $this->db->fetchAll($sql, [$user_id, $limit]);
        
        // Reverse to get chronological order
        return array_reverse($messages);
    }
    
    /**
     * Create new conversation
     */
    private function createConversation($user_id, $conversation_id, $title = 'New Conversation') {
        $sql = "INSERT INTO conversations (user_id, conversation_id, title) VALUES (?, ?, ?)";
        return $this->db->execute($sql, [$user_id, $conversation_id, $title]);
    }
    
    /**
     * Store message in database
     */
    private function storeMessage($conversation_id, $user_id, $type, $content, $tokens = 0, $source = 'openai') {
        $sql = "INSERT INTO chat_messages (conversation_id, user_id, message_type, message_content, tokens_used, response_source) 
                VALUES (?, ?, ?, ?, ?, ?)";
        
        return $this->db->execute($sql, [
            $conversation_id, 
            $user_id, 
            $type, 
            $content, 
            $tokens, 
            $source
        ]);
    }
    
    /**
     * Update user statistics
     */
    private function updateUserStats($user_id, $tokens_used) {
        $sql = "UPDATE users 
                SET total_messages = total_messages + 1, 
                    total_tokens_used = total_tokens_used + ? 
                WHERE user_id = ?";
        
        return $this->db->execute($sql, [$tokens_used, $user_id]);
    }
    
    /**
     * Log API usage
     */
    private function logAPIUsage($user_id, $api_type, $tokens_used, $success, $error_message = null) {
        $sql = "INSERT INTO api_usage (user_id, api_type, tokens_used, success, error_message) 
                VALUES (?, ?, ?, ?, ?)";
        
        return $this->db->execute($sql, [
            $user_id, 
            $api_type, 
            $tokens_used, 
            $success ? 1 : 0, 
            $error_message
        ]);
    }
}

// Handle API requests
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        jsonResponse(['error' => 'Invalid JSON input'], 400);
    }
    
    $action = $input['action'] ?? '';
    
    switch ($action) {
        case 'chat':
            $user_id = $input['user_id'] ?? generateUserId();
            $message = sanitizeInput($input['message'] ?? '');
            $conversation_id = $input['conversation_id'] ?? null;
            
            if (empty($message)) {
                jsonResponse(['error' => 'Message is required'], 400);
            }
            
            if (strlen($message) > MAX_MESSAGE_LENGTH) {
                jsonResponse(['error' => 'Message too long'], 400);
            }
            
            $chatHandler = new ChatHandler();
            $result = $chatHandler->handleChatRequest($user_id, $message, $conversation_id);
            
            jsonResponse($result);
            break;
            
        default:
            jsonResponse(['error' => 'Invalid action'], 400);
    }
} else {
    jsonResponse(['error' => 'Only POST requests allowed'], 405);
}
?>
