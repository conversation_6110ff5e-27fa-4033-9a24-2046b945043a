/**
 * OAuth Configuration for Tincada AI
 * Social login setup for Google and Apple
 */

// OAuth Configuration
const OAUTH_CONFIG = {
    google: {
        clientId: '627716371527-shjvho7kcqn6vpjk1scrijfr88cg4g1n.apps.googleusercontent.com',
        scope: 'profile email',
        redirectUri: window.location.origin + '/auth/google/callback'
    },
    apple: {
        clientId: 'com.tincada.ai', // Replace with your Apple Service ID
        scope: 'name email',
        redirectUri: window.location.origin + '/auth/apple/callback'
    }
};

// Google OAuth Helper Functions
class GoogleOAuth {
    static initialize() {
        return new Promise((resolve, reject) => {
            if (typeof google !== 'undefined' && google.accounts) {
                google.accounts.id.initialize({
                    client_id: OAUTH_CONFIG.google.clientId,
                    callback: this.handleSignIn,
                    auto_select: false,
                    cancel_on_tap_outside: false
                });
                
                console.log('✅ Google OAuth initialized');
                resolve(true);
            } else {
                reject(new Error('Google SDK not loaded'));
            }
        });
    }
    
    static handleSignIn(response) {
        try {
            // Decode JWT token
            const payload = JSON.parse(atob(response.credential.split('.')[1]));
            
            const userInfo = {
                id: payload.sub,
                name: payload.name,
                email: payload.email,
                picture: payload.picture,
                provider: 'google',
                loginTime: new Date().toISOString(),
                token: response.credential
            };
            
            // Store user info
            localStorage.setItem('tincada_user', JSON.stringify(userInfo));
            
            // Trigger custom event
            window.dispatchEvent(new CustomEvent('socialLoginSuccess', {
                detail: { provider: 'google', user: userInfo }
            }));
            
            return userInfo;
            
        } catch (error) {
            console.error('Error processing Google Sign In:', error);
            throw error;
        }
    }
    
    static signIn() {
        return new Promise((resolve, reject) => {
            if (typeof google !== 'undefined' && google.accounts) {
                google.accounts.id.prompt((notification) => {
                    if (notification.isNotDisplayed() || notification.isSkippedMoment()) {
                        // Show popup as fallback
                        google.accounts.id.renderButton(
                            document.createElement('div'),
                            { theme: 'outline', size: 'large' }
                        );
                    }
                });
                resolve(true);
            } else {
                reject(new Error('Google Sign In not available'));
            }
        });
    }
}

// Apple OAuth Helper Functions
class AppleOAuth {
    static initialize() {
        return new Promise((resolve, reject) => {
            if (typeof AppleID !== 'undefined') {
                AppleID.auth.init({
                    clientId: OAUTH_CONFIG.apple.clientId,
                    scope: OAUTH_CONFIG.apple.scope,
                    redirectURI: OAUTH_CONFIG.apple.redirectUri,
                    state: 'tincada-login-' + Date.now(),
                    usePopup: true
                });
                
                console.log('✅ Apple OAuth initialized');
                resolve(true);
            } else {
                reject(new Error('Apple SDK not loaded'));
            }
        });
    }
    
    static signIn() {
        return new Promise((resolve, reject) => {
            if (typeof AppleID !== 'undefined') {
                AppleID.auth.signIn().then((response) => {
                    const { authorization, user } = response;
                    
                    const userInfo = {
                        id: authorization.code,
                        name: user?.name ? `${user.name.firstName} ${user.name.lastName}` : 'Apple User',
                        email: user?.email || '<EMAIL>',
                        provider: 'apple',
                        loginTime: new Date().toISOString(),
                        token: authorization.code
                    };
                    
                    // Store user info
                    localStorage.setItem('tincada_user', JSON.stringify(userInfo));
                    
                    // Trigger custom event
                    window.dispatchEvent(new CustomEvent('socialLoginSuccess', {
                        detail: { provider: 'apple', user: userInfo }
                    }));
                    
                    resolve(userInfo);
                    
                }).catch(reject);
            } else {
                reject(new Error('Apple Sign In not available'));
            }
        });
    }
}

// Social Login Manager
class SocialLoginManager {
    constructor() {
        this.providers = {
            google: GoogleOAuth,
            apple: AppleOAuth
        };
        
        this.setupEventListeners();
    }
    
    async initialize() {
        console.log('🚀 Initializing social login providers...');
        
        // Initialize Google
        try {
            await this.providers.google.initialize();
        } catch (error) {
            console.warn('Google OAuth initialization failed:', error);
        }
        
        // Initialize Apple
        try {
            await this.providers.apple.initialize();
        } catch (error) {
            console.warn('Apple OAuth initialization failed:', error);
        }
    }
    
    async signIn(provider) {
        if (!this.providers[provider]) {
            throw new Error(`Provider ${provider} not supported`);
        }
        
        try {
            const result = await this.providers[provider].signIn();
            console.log(`✅ ${provider} sign in successful:`, result);
            return result;
        } catch (error) {
            console.error(`❌ ${provider} sign in failed:`, error);
            throw error;
        }
    }
    
    signOut() {
        // Clear stored user data
        localStorage.removeItem('tincada_user');
        
        // Trigger sign out event
        window.dispatchEvent(new CustomEvent('socialLoginSignOut'));
        
        console.log('✅ User signed out');
    }
    
    getCurrentUser() {
        try {
            const userData = localStorage.getItem('tincada_user');
            return userData ? JSON.parse(userData) : null;
        } catch (error) {
            console.error('Error getting current user:', error);
            return null;
        }
    }
    
    setupEventListeners() {
        // Listen for successful login
        window.addEventListener('socialLoginSuccess', (event) => {
            const { provider, user } = event.detail;
            console.log(`🎉 Social login success: ${provider}`, user);
            
            // Show success message
            this.showMessage(`Ku soo dhaweyn ${user.name}! Ku guulaysatay ${provider} Sign In`, 'success');
            
            // Redirect to dashboard after delay
            setTimeout(() => {
                window.location.href = 'dashboard.html';
            }, 2000);
        });
        
        // Listen for sign out
        window.addEventListener('socialLoginSignOut', () => {
            console.log('🚪 User signed out');
            window.location.href = 'login.html';
        });
    }
    
    showMessage(message, type = 'info') {
        // Create or update message element
        let messageElement = document.getElementById('socialLoginMessage');
        if (!messageElement) {
            messageElement = document.createElement('div');
            messageElement.id = 'socialLoginMessage';
            messageElement.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 15px 20px;
                border-radius: 10px;
                color: white;
                font-weight: 500;
                z-index: 10000;
                max-width: 300px;
                box-shadow: 0 4px 15px rgba(0,0,0,0.2);
                transition: all 0.3s ease;
            `;
            document.body.appendChild(messageElement);
        }
        
        // Set message and style based on type
        messageElement.textContent = message;
        messageElement.style.background = type === 'success' ? 
            'linear-gradient(45deg, #4caf50, #45a049)' : 
            'linear-gradient(45deg, #f44336, #d32f2f)';
        
        messageElement.style.display = 'block';
        
        // Hide after 4 seconds
        setTimeout(() => {
            messageElement.style.display = 'none';
        }, 4000);
    }
}

// Initialize social login manager
const socialLogin = new SocialLoginManager();

// Auto-initialize when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        setTimeout(() => socialLogin.initialize(), 1000);
    });
} else {
    setTimeout(() => socialLogin.initialize(), 1000);
}

// Export for global use
window.SocialLogin = socialLogin;
window.GoogleOAuth = GoogleOAuth;
window.AppleOAuth = AppleOAuth;
