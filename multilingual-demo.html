<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Multilingual AI Demo - Tincada AI</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
            padding: 2rem;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 3rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        h1 {
            text-align: center;
            font-size: 2.5rem;
            margin-bottom: 1rem;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .subtitle {
            text-align: center;
            font-size: 1.2rem;
            margin-bottom: 3rem;
            opacity: 0.9;
        }

        .languages-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-bottom: 3rem;
        }

        .language-card {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 2rem;
            transition: all 0.3s ease;
            text-align: center;
            cursor: pointer;
        }

        .language-card:hover {
            transform: translateY(-5px);
            border-color: rgba(255, 255, 255, 0.2);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);
        }

        .language-flag {
            font-size: 3rem;
            margin-bottom: 1rem;
        }

        .language-card h3 {
            color: #ffffff;
            margin-bottom: 0.5rem;
        }

        .language-card p {
            color: #cccccc;
            font-size: 0.9rem;
            margin-bottom: 1rem;
        }

        .test-btn {
            background: linear-gradient(45deg, #4ecdc4, #44a08d);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
            width: 100%;
        }

        .test-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(78, 205, 196, 0.4);
        }

        .chat-demo {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            border-left: 4px solid #4ecdc4;
        }

        .chat-demo h2 {
            color: #4ecdc4;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .chat-messages {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            padding: 1.5rem;
            max-height: 400px;
            overflow-y: auto;
            margin-bottom: 1rem;
        }

        .message {
            margin-bottom: 1rem;
            padding: 1rem;
            border-radius: 10px;
            animation: fadeIn 0.5s ease;
        }

        .message.user {
            background: rgba(78, 205, 196, 0.2);
            border-left: 4px solid #4ecdc4;
            margin-left: 2rem;
        }

        .message.ai {
            background: rgba(255, 255, 255, 0.1);
            border-left: 4px solid #ff6b6b;
            margin-right: 2rem;
        }

        .message-header {
            font-weight: 600;
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .message-content {
            line-height: 1.6;
        }

        .input-section {
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        .language-select {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            color: white;
            padding: 10px;
            font-size: 0.9rem;
        }

        .message-input {
            flex: 1;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            color: white;
            padding: 12px 15px;
            font-family: inherit;
            font-size: 1rem;
        }

        .message-input:focus {
            outline: none;
            border-color: #4ecdc4;
            box-shadow: 0 0 0 3px rgba(78, 205, 196, 0.2);
        }

        .send-btn {
            background: linear-gradient(45deg, #4ecdc4, #44a08d);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .send-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(78, 205, 196, 0.4);
        }

        .send-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .typing-indicator {
            display: none;
            align-items: center;
            gap: 8px;
            color: #4ecdc4;
            font-style: italic;
            margin: 1rem 0;
        }

        .typing-dots {
            display: flex;
            gap: 4px;
        }

        .typing-dot {
            width: 8px;
            height: 8px;
            background: #4ecdc4;
            border-radius: 50%;
            animation: typingDot 1.4s infinite ease-in-out;
        }

        .typing-dot:nth-child(1) { animation-delay: -0.32s; }
        .typing-dot:nth-child(2) { animation-delay: -0.16s; }

        @keyframes typingDot {
            0%, 80%, 100% { transform: scale(0); }
            40% { transform: scale(1); }
        }

        @media (max-width: 768px) {
            body { padding: 1rem; }
            .container { padding: 2rem 1rem; }
            h1 { font-size: 2rem; }
            .languages-grid { grid-template-columns: 1fr; }
            .input-section { flex-direction: column; }
            .language-select, .message-input { width: 100%; }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌍 Multilingual AI Assistant</h1>
        <p class="subtitle">Tincada AI oo ku hadla luqado kala duwan - AI that speaks multiple languages</p>
        
        <div class="languages-grid">
            <div class="language-card" onclick="testLanguage('somali')">
                <div class="language-flag">🇸🇴</div>
                <h3>Af-Soomaali</h3>
                <p>Native Somali language support</p>
                <button class="test-btn">Test Somali</button>
            </div>
            
            <div class="language-card" onclick="testLanguage('arabic')">
                <div class="language-flag">🇸🇦</div>
                <h3>العربية</h3>
                <p>Arabic language support</p>
                <button class="test-btn">Test Arabic</button>
            </div>
            
            <div class="language-card" onclick="testLanguage('english')">
                <div class="language-flag">🇺🇸</div>
                <h3>English</h3>
                <p>English language support</p>
                <button class="test-btn">Test English</button>
            </div>
            
            <div class="language-card" onclick="testLanguage('french')">
                <div class="language-flag">🇫🇷</div>
                <h3>Français</h3>
                <p>French language support</p>
                <button class="test-btn">Test French</button>
            </div>
            
            <div class="language-card" onclick="testLanguage('spanish')">
                <div class="language-flag">🇪🇸</div>
                <h3>Español</h3>
                <p>Spanish language support</p>
                <button class="test-btn">Test Spanish</button>
            </div>
            
            <div class="language-card" onclick="testLanguage('german')">
                <div class="language-flag">🇩🇪</div>
                <h3>Deutsch</h3>
                <p>German language support</p>
                <button class="test-btn">Test German</button>
            </div>
        </div>
        
        <div class="chat-demo">
            <h2>💬 Live Chat Demo</h2>
            <p>Test real-time multilingual conversations with Tincada AI:</p>
            
            <div class="chat-messages" id="chatMessages">
                <div class="message ai">
                    <div class="message-header">
                        <i class="fas fa-robot"></i>
                        Tincada AI
                    </div>
                    <div class="message-content">
                        Hello! I'm Tincada AI. I can communicate in multiple languages. Try sending me a message in any language!
                        <br><br>
                        مرحبا! أنا Tincada AI. يمكنني التواصل بلغات متعددة.
                        <br><br>
                        Salut! Je suis Tincada AI. Je peux communiquer en plusieurs langues.
                        <br><br>
                        Waxaan ahay Tincada AI. Waxaan ku hadli karaa luqado kala duwan.
                    </div>
                </div>
            </div>
            
            <div class="typing-indicator" id="typingIndicator">
                <i class="fas fa-robot"></i>
                <span>Tincada AI is typing</span>
                <div class="typing-dots">
                    <div class="typing-dot"></div>
                    <div class="typing-dot"></div>
                    <div class="typing-dot"></div>
                </div>
            </div>
            
            <div class="input-section">
                <select class="language-select" id="languageSelect">
                    <option value="auto">Auto-detect</option>
                    <option value="somali">Somali</option>
                    <option value="arabic">Arabic</option>
                    <option value="english">English</option>
                    <option value="french">French</option>
                    <option value="spanish">Spanish</option>
                    <option value="german">German</option>
                </select>
                
                <input type="text" class="message-input" id="messageInput" 
                       placeholder="Type your message in any language..." 
                       onkeypress="handleKeyPress(event)">
                
                <button class="send-btn" id="sendBtn" onclick="sendMessage()">
                    <i class="fas fa-paper-plane"></i>
                </button>
            </div>
        </div>
    </div>

    <script>
        // Sample messages for different languages
        const sampleMessages = {
            somali: "Maxaad ka fikirtaa AI-ga?",
            arabic: "ما رأيك في الذكاء الاصطناعي؟",
            english: "What do you think about artificial intelligence?",
            french: "Que pensez-vous de l'intelligence artificielle?",
            spanish: "¿Qué piensas sobre la inteligencia artificial?",
            german: "Was denkst du über künstliche Intelligenz?"
        };

        function testLanguage(language) {
            const message = sampleMessages[language];
            document.getElementById('messageInput').value = message;
            document.getElementById('languageSelect').value = language;
            sendMessage();
        }

        function handleKeyPress(event) {
            if (event.key === 'Enter') {
                sendMessage();
            }
        }

        async function sendMessage() {
            const messageInput = document.getElementById('messageInput');
            const sendBtn = document.getElementById('sendBtn');
            const message = messageInput.value.trim();
            
            if (!message) return;
            
            // Add user message
            addMessage('user', message);
            
            // Clear input and disable button
            messageInput.value = '';
            sendBtn.disabled = true;
            
            // Show typing indicator
            showTypingIndicator();
            
            try {
                // Send to API
                const response = await fetch('http://localhost:5001/api/chat', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        message: message,
                        user_id: 'multilingual_demo_user'
                    })
                });
                
                const data = await response.json();
                
                // Hide typing indicator
                hideTypingIndicator();
                
                if (data.response) {
                    addMessage('ai', data.response);
                } else {
                    addMessage('ai', 'Sorry, I encountered an error. Please try again.');
                }
                
            } catch (error) {
                console.error('Error:', error);
                hideTypingIndicator();
                addMessage('ai', 'Connection error. Please check if the server is running.');
            } finally {
                sendBtn.disabled = false;
            }
        }

        function addMessage(sender, content) {
            const chatMessages = document.getElementById('chatMessages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${sender}`;
            
            const icon = sender === 'user' ? 'fas fa-user' : 'fas fa-robot';
            const name = sender === 'user' ? 'You' : 'Tincada AI';
            
            messageDiv.innerHTML = `
                <div class="message-header">
                    <i class="${icon}"></i>
                    ${name}
                </div>
                <div class="message-content">${content}</div>
            `;
            
            chatMessages.appendChild(messageDiv);
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        function showTypingIndicator() {
            document.getElementById('typingIndicator').style.display = 'flex';
        }

        function hideTypingIndicator() {
            document.getElementById('typingIndicator').style.display = 'none';
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🌍 Multilingual demo loaded');
            
            // Test server connection
            fetch('http://localhost:5001/api/health')
                .then(response => response.json())
                .then(data => {
                    console.log('✅ Server connected:', data);
                })
                .catch(error => {
                    console.log('⚠️ Server not connected:', error);
                    addMessage('ai', '⚠️ Server not connected. Please start the Flask server with: python flask_server.py');
                });
        });
    </script>
</body>
</html>
