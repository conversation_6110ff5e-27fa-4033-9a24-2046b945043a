<!DOCTYPE html>
<html lang="so">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Test - Tincada AI</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: #212121;
            color: white;
            padding: 20px;
            max-width: 800px;
            margin: 0 auto;
        }
        .test-container {
            background: #2f2f2f;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        .test-input {
            width: 100%;
            padding: 10px;
            background: #171717;
            border: 1px solid #3f3f3f;
            color: white;
            border-radius: 5px;
            margin-bottom: 10px;
        }
        .test-btn {
            background: #10a37f;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin-right: 10px;
        }
        .test-btn:hover {
            background: #0d8f6f;
        }
        .result {
            background: #171717;
            padding: 15px;
            border-radius: 5px;
            margin-top: 10px;
            border-left: 4px solid #10a37f;
        }
        .error {
            border-left-color: #ef4444;
            background: #2d1b1b;
        }
        .loading {
            color: #10a37f;
        }
    </style>
</head>
<body>
    <h1>🧪 Tincada AI - API Test</h1>
    
    <div class="test-container">
        <h3>Test API Keys</h3>
        <p>Halkan waxaad ku test gareyn kartaa API keys-yada si aad u hubiso kuwa shaqeeya.</p>
        
        <input type="text" id="testMessage" class="test-input" placeholder="Qor fariin test ah..." value="Salam, sidee tahay?">
        
        <button class="test-btn" onclick="testPrimaryAPI()">Test Primary API</button>
        <button class="test-btn" onclick="testFallbackAPI()">Test Fallback API</button>
        <button class="test-btn" onclick="testBothAPIs()">Test Both APIs</button>
        
        <div id="result"></div>
    </div>

    <div class="test-container">
        <h3>API Keys Info</h3>
        <p><strong>Primary API:</strong> ********************************************************************************************************************************************************************</p>
        <p><strong>Fallback API:</strong> sk-1d9fea924a444e58b28769de9662ef20</p>
    </div>

    <script>
        const primaryApiKey = '********************************************************************************************************************************************************************';
        const fallbackApiKey = 'sk-1d9fea924a444e58b28769de9662ef20';
        const apiUrl = 'https://api.openai.com/v1/chat/completions';

        function showResult(message, isError = false) {
            const resultDiv = document.getElementById('result');
            resultDiv.className = isError ? 'result error' : 'result';
            resultDiv.innerHTML = message;
        }

        function showLoading() {
            showResult('<div class="loading">⏳ Testing API...</div>');
        }

        async function testAPI(apiKey, keyName) {
            const message = document.getElementById('testMessage').value || 'Salam, sidee tahay?';
            
            try {
                const response = await fetch(apiUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${apiKey}`
                    },
                    body: JSON.stringify({
                        model: 'gpt-4o-mini',
                        messages: [
                            {
                                role: 'system',
                                content: 'Waxaad tahay Tincada AI. Ku jawaab af Soomaali oo gaaban.'
                            },
                            {
                                role: 'user',
                                content: message
                            }
                        ],
                        max_tokens: 150,
                        temperature: 0.7
                    })
                });

                if (!response.ok) {
                    const errorText = await response.text();
                    throw new Error(`${response.status}: ${errorText}`);
                }

                const data = await response.json();
                const aiResponse = data.choices[0].message.content;
                
                return {
                    success: true,
                    response: aiResponse,
                    keyName: keyName
                };
                
            } catch (error) {
                return {
                    success: false,
                    error: error.message,
                    keyName: keyName
                };
            }
        }

        async function testPrimaryAPI() {
            showLoading();
            const result = await testAPI(primaryApiKey, 'Primary API');
            
            if (result.success) {
                showResult(`
                    <h4>✅ ${result.keyName} - SUCCESS!</h4>
                    <p><strong>AI Response:</strong></p>
                    <p>${result.response}</p>
                `);
            } else {
                showResult(`
                    <h4>❌ ${result.keyName} - FAILED!</h4>
                    <p><strong>Error:</strong> ${result.error}</p>
                `, true);
            }
        }

        async function testFallbackAPI() {
            showLoading();
            const result = await testAPI(fallbackApiKey, 'Fallback API');
            
            if (result.success) {
                showResult(`
                    <h4>✅ ${result.keyName} - SUCCESS!</h4>
                    <p><strong>AI Response:</strong></p>
                    <p>${result.response}</p>
                `);
            } else {
                showResult(`
                    <h4>❌ ${result.keyName} - FAILED!</h4>
                    <p><strong>Error:</strong> ${result.error}</p>
                `, true);
            }
        }

        async function testBothAPIs() {
            showLoading();
            
            const primaryResult = await testAPI(primaryApiKey, 'Primary API');
            const fallbackResult = await testAPI(fallbackApiKey, 'Fallback API');
            
            let resultHTML = '<h4>🔍 Both APIs Test Results:</h4>';
            
            // Primary API result
            if (primaryResult.success) {
                resultHTML += `
                    <div style="margin: 10px 0; padding: 10px; background: #1a4d3a; border-radius: 5px;">
                        <strong>✅ Primary API - SUCCESS!</strong><br>
                        <em>Response:</em> ${primaryResult.response.substring(0, 100)}...
                    </div>
                `;
            } else {
                resultHTML += `
                    <div style="margin: 10px 0; padding: 10px; background: #4d1a1a; border-radius: 5px;">
                        <strong>❌ Primary API - FAILED!</strong><br>
                        <em>Error:</em> ${primaryResult.error}
                    </div>
                `;
            }
            
            // Fallback API result
            if (fallbackResult.success) {
                resultHTML += `
                    <div style="margin: 10px 0; padding: 10px; background: #1a4d3a; border-radius: 5px;">
                        <strong>✅ Fallback API - SUCCESS!</strong><br>
                        <em>Response:</em> ${fallbackResult.response.substring(0, 100)}...
                    </div>
                `;
            } else {
                resultHTML += `
                    <div style="margin: 10px 0; padding: 10px; background: #4d1a1a; border-radius: 5px;">
                        <strong>❌ Fallback API - FAILED!</strong><br>
                        <em>Error:</em> ${fallbackResult.error}
                    </div>
                `;
            }
            
            // Recommendation
            if (primaryResult.success) {
                resultHTML += '<p><strong>💡 Recommendation:</strong> Primary API waa shaqaynayaa - isticmaal.</p>';
            } else if (fallbackResult.success) {
                resultHTML += '<p><strong>💡 Recommendation:</strong> Primary API ma shaqaynayo, laakiin Fallback API waa shaqaynayaa.</p>';
            } else {
                resultHTML += '<p><strong>⚠️ Warning:</strong> Labada API key ba ma shaqaynayaan. Hubi API keys-yada.</p>';
            }
            
            showResult(resultHTML);
        }
    </script>
</body>
</html>
