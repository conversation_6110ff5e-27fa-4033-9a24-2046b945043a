#!/usr/bin/env python3
"""
Tincada AI Image Generator
<PERSON><PERSON><PERSON> sawiro adoo isticmaalaya OpenAI DALL-E
"""

import requests
import base64
import json
import os
from datetime import datetime

# API Key
OPENAI_API_KEY = "********************************************************************************************************************************************************************"

def generate_image(prompt, filename=None, size="1024x1024"):
    """
    Samee sawir adoo isticmaalaya OpenAI DALL-E API
    
    Args:
        prompt (str): Qoraalka sawirka (description)
        filename (str): Magaca file-ka (optional)
        size (str): Cabbirka sawirka (1024x1024, 1024x1792, 1792x1024)
    
    Returns:
        str: Magaca file-ka la keydiyay
    """
    
    print(f"🎨 Sameeynaya sawir: {prompt}")
    print(f"📏 Cabbir: {size}")
    
    try:
        # Prepare API request
        headers = {
            'Authorization': f'Bearer {OPENAI_API_KEY}',
            'Content-Type': 'application/json'
        }
        
        data = {
            'model': 'dall-e-3',  # Using latest DALL-E model
            'prompt': prompt,
            'n': 1,
            'size': size,
            'response_format': 'b64_json'
        }
        
        print("📡 La xiriiraya OpenAI API...")
        
        # Make API request
        response = requests.post(
            'https://api.openai.com/v1/images/generations',
            headers=headers,
            json=data,
            timeout=60
        )
        
        if response.status_code == 200:
            result = response.json()
            image_b64 = result['data'][0]['b64_json']
            
            # Generate filename if not provided
            if not filename:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"generated_image_{timestamp}.png"
            
            # Ensure filename has .png extension
            if not filename.endswith('.png'):
                filename += '.png'
            
            # Save image
            with open(filename, "wb") as f:
                f.write(base64.b64decode(image_b64))
            
            print(f"✅ Sawirka waa la keydiyay: {filename}")
            print(f"📁 File size: {os.path.getsize(filename)} bytes")
            
            return filename
            
        else:
            print(f"❌ API Error: {response.status_code}")
            print(f"Error details: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ Khalad: {e}")
        return None

def generate_multiple_images(prompts, size="1024x1024"):
    """
    Samee sawiro badan
    
    Args:
        prompts (list): Liiska qoraallada sawirrada
        size (str): Cabbirka sawirka
    
    Returns:
        list: Liiska magacyada files-yada la keydiyay
    """
    
    generated_files = []
    
    for i, prompt in enumerate(prompts, 1):
        print(f"\n🎨 Sawir {i}/{len(prompts)}")
        filename = f"image_{i}_{datetime.now().strftime('%H%M%S')}.png"
        
        result = generate_image(prompt, filename, size)
        if result:
            generated_files.append(result)
        
        # Small delay between requests
        import time
        time.sleep(2)
    
    return generated_files

def main():
    """Main function for testing"""
    
    print("🎨 Tincada AI Image Generator")
    print("=" * 50)
    
    # Test cases
    test_prompts = [
        "A gray tabby cat hugging an otter with an orange scarf",
        "A beautiful sunset over mountains with a lake",
        "A futuristic city with flying cars and tall buildings",
        "A peaceful garden with colorful flowers and butterflies"
    ]
    
    print("🧪 Testing image generation...")
    
    # Generate single image (your original example)
    print("\n1️⃣ Generating your original image...")
    result1 = generate_image(
        "A gray tabby cat hugging an otter with an orange scarf",
        "otter.png"
    )
    
    # Generate additional test images
    print("\n2️⃣ Generating additional test images...")
    results = generate_multiple_images(test_prompts[1:], "1024x1024")
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 Generation Summary:")
    
    all_results = [result1] + results if result1 else results
    successful = len([r for r in all_results if r])
    
    print(f"✅ Successful: {successful}/{len(test_prompts)}")
    print(f"📁 Generated files:")
    
    for result in all_results:
        if result:
            print(f"   - {result}")
    
    if successful > 0:
        print("\n🎉 Image generation completed successfully!")
    else:
        print("\n❌ No images were generated. Check API key and connection.")

if __name__ == "__main__":
    main()
