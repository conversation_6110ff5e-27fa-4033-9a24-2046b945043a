#!/usr/bin/env python3
"""
Test New OpenAI API Key for Image Generation
"""

from openai import OpenAI
import base64
import os
from datetime import datetime

# New API Key
API_KEY = "********************************************************************************************************************************************************************"

# Initialize OpenAI client
client = OpenAI(api_key=API_KEY)

def test_api_key():
    """Test if API key works"""
    try:
        # Simple API test
        response = client.models.list()
        print("✅ API Key is valid!")
        return True
    except Exception as e:
        print(f"❌ API Key test failed: {e}")
        return False

def generate_image_test(prompt, filename=None):
    """Generate image using new API key"""
    
    print(f"🎨 Sameeynaya sawir: {prompt}")
    
    try:
        # Generate image
        response = client.images.generate(
            model="dall-e-3",
            prompt=prompt,
            size="1024x1024",
            quality="standard",
            n=1,
            response_format="b64_json"
        )
        
        # Get base64 image
        image_b64 = response.data[0].b64_json
        
        # Generate filename if not provided
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"test_image_{timestamp}.png"
        
        # Save image
        with open(filename, "wb") as f:
            f.write(base64.b64decode(image_b64))
        
        print(f"✅ Sawirka waa la keydiyay: {filename}")
        print(f"📁 File size: {os.path.getsize(filename)} bytes")
        
        return filename
        
    except Exception as e:
        print(f"❌ Image generation failed: {e}")
        return None

def main():
    """Main test function"""
    
    print("🧪 Testing New OpenAI API Key")
    print("=" * 50)
    
    # Test 1: API Key validation
    print("\n1️⃣ Testing API Key...")
    if not test_api_key():
        print("❌ API Key is invalid. Cannot proceed.")
        return
    
    # Test 2: Image generation
    print("\n2️⃣ Testing Image Generation...")
    
    # Your original prompt
    prompt = "A gray tabby cat hugging an otter with an orange scarf"
    filename = "new_api_test.png"
    
    result = generate_image_test(prompt, filename)
    
    if result:
        print(f"\n🎉 SUCCESS! Image generated: {result}")
        print("📁 Check the PNG file in this folder.")
        
        # Test additional image
        print("\n3️⃣ Testing additional image...")
        result2 = generate_image_test(
            "A beautiful sunset over mountains with a lake",
            "sunset_test.png"
        )
        
        if result2:
            print(f"✅ Second image also generated: {result2}")
        
        print("\n🎉 API Key is working perfectly!")
        
    else:
        print("\n❌ Image generation failed. Check billing and quota.")

if __name__ == "__main__":
    main()
