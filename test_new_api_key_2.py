#!/usr/bin/env python3
"""
Test the new OpenAI API key provided by user
"""

from openai import OpenAI
import j<PERSON>

def test_new_api_key():
    """Test the new OpenAI API key"""
    
    api_key = "********************************************************************************************************************************************************************"
    
    print("🔑 Testing New OpenAI API Key")
    print("=" * 50)
    print(f"Key: {api_key[:20]}...{api_key[-10:]}")
    
    try:
        # Initialize OpenAI client
        client = OpenAI(api_key=api_key)
        
        # Test basic chat completion
        print("\n📝 Testing basic chat completion...")
        response = client.chat.completions.create(
            model="gpt-4o-mini",
            messages=[
                {
                    "role": "system",
                    "content": "You are a helpful AI assistant. Respond professionally and accurately."
                },
                {
                    "role": "user", 
                    "content": "What is 2+2? Give a brief answer."
                }
            ],
            max_tokens=100,
            temperature=0.7
        )
        
        ai_response = response.choices[0].message.content
        tokens_used = response.usage.total_tokens if response.usage else 0
        
        print(f"✅ API Response: {ai_response}")
        print(f"📊 Tokens used: {tokens_used}")
        print("✅ Basic chat completion works!")
        
        # Test more complex question
        print("\n🧠 Testing complex question...")
        response2 = client.chat.completions.create(
            model="gpt-4o-mini",
            messages=[
                {
                    "role": "system",
                    "content": "You are Tincada AI. Respond in the same language the user uses."
                },
                {
                    "role": "user", 
                    "content": "What is artificial intelligence? Give me a detailed explanation."
                }
            ],
            max_tokens=300,
            temperature=0.7
        )
        
        ai_response2 = response2.choices[0].message.content
        tokens_used2 = response2.usage.total_tokens if response2.usage else 0
        
        print(f"✅ Complex Response: {ai_response2[:200]}...")
        print(f"📊 Tokens used: {tokens_used2}")
        print("✅ Complex questions work!")
        
        # Test Somali language
        print("\n🇸🇴 Testing Somali language...")
        response3 = client.chat.completions.create(
            model="gpt-4o-mini",
            messages=[
                {
                    "role": "system",
                    "content": "You are Tincada AI. Always respond in the same language the user uses. If they speak Somali, respond in Somali."
                },
                {
                    "role": "user", 
                    "content": "Salam alaykum! Maxaa ka dhigan AI? Sharax si kooban."
                }
            ],
            max_tokens=200,
            temperature=0.7
        )
        
        ai_response3 = response3.choices[0].message.content
        tokens_used3 = response3.usage.total_tokens if response3.usage else 0
        
        print(f"✅ Somali Response: {ai_response3}")
        print(f"📊 Tokens used: {tokens_used3}")
        print("✅ Somali language works!")
        
        print("\n🎉 ALL TESTS PASSED! API key is working perfectly!")
        return True
        
    except Exception as e:
        print(f"❌ API Error: {e}")
        print("❌ API key may not be working or may lack permissions")
        return False

if __name__ == "__main__":
    success = test_new_api_key()
    if success:
        print("\n🎯 API key is ready for use! AI will use real OpenAI data.")
    else:
        print("\n💥 API key test failed! Will use mock responses.")
