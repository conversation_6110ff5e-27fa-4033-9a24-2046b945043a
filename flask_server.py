#!/usr/bin/env python3
"""
Tincada AI Flask Backend Server
Secure OpenAI API integration with proper error handling
"""

import os
import json
import logging
from datetime import datetime
from flask import Flask, request, jsonify, send_from_directory
from flask_cors import CORS
import time
import replicate

# Load environment variables
try:
    from dotenv import load_dotenv
    load_dotenv()
    print("✅ Environment variables loaded from .env file")
except ImportError:
    print("⚠️  python-dotenv not installed. Install with: pip install python-dotenv")
except Exception as e:
    print(f"⚠️  Could not load .env file: {e}")

# Try to import OpenAI
try:
    from openai import OpenAI
    OPENAI_AVAILABLE = True
except ImportError:
    OPENAI_AVAILABLE = False
    print("⚠️  OpenAI library not installed. Install with: pip install openai")

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize Flask app
app = Flask(__name__)
CORS(app)

# Initialize OpenAI client
def get_openai_client():
    """Initialize OpenAI client with API key from environment"""
    if not OPENAI_AVAILABLE:
        return None
        
    api_key = os.getenv('OPENAI_API_KEY')
    if not api_key:
        logger.warning("OPENAI_API_KEY environment variable not set")
        return None
    
    try:
        client = OpenAI(api_key=api_key)
        # Test the client with a simple call
        client.models.list()
        logger.info("OpenAI client initialized successfully")
        return client
    except Exception as e:
        logger.error(f"Failed to initialize OpenAI client: {e}")
        return None

# Global client instance
openai_client = get_openai_client()

# Initialize Replicate client
def get_replicate_client():
    """Initialize Replicate client with API token from environment"""
    try:
        replicate_token = os.getenv('REPLICATE_API_TOKEN')
        if replicate_token:
            os.environ['REPLICATE_API_TOKEN'] = replicate_token
            logger.info(f"✅ Replicate client initialized with token: {replicate_token[:20]}...")
            return replicate
        else:
            logger.warning("⚠️ No Replicate API token found in environment variables")
            return None
    except Exception as e:
        logger.error(f"❌ Failed to initialize Replicate client: {e}")
        return None

# Global Replicate client instance
replicate_client = get_replicate_client()

# Rate limiting
user_requests = {}
RATE_LIMIT = 60  # requests per hour
RATE_WINDOW = 3600  # 1 hour in seconds

def check_rate_limit(user_id):
    """Check if user has exceeded rate limit"""
    current_time = time.time()
    
    if user_id not in user_requests:
        user_requests[user_id] = []
    
    # Remove old requests outside the window
    user_requests[user_id] = [
        req_time for req_time in user_requests[user_id]
        if current_time - req_time < RATE_WINDOW
    ]
    
    # Check if under limit
    if len(user_requests[user_id]) >= RATE_LIMIT:
        return False
    
    # Add current request
    user_requests[user_id].append(current_time)
    return True

@app.route('/')
def index():
    """Serve the main dashboard"""
    return send_from_directory('.', 'dashboard.html')

@app.route('/<path:filename>')
def serve_static(filename):
    """Serve static files"""
    try:
        return send_from_directory('.', filename)
    except:
        return "File not found", 404

@app.route('/api/chat', methods=['POST'])
def chat():
    """Handle chat requests with OpenAI API"""
    try:
        # Get request data
        data = request.get_json()
        if not data or 'message' not in data:
            return jsonify({'error': 'Message is required'}), 400
        
        message = data.get('message', '').strip()
        user_id = data.get('user_id', 'anonymous')
        
        if not message:
            return jsonify({'error': 'Message cannot be empty'}), 400
        
        # Check rate limit
        if not check_rate_limit(user_id):
            return jsonify({
                'error': 'Rate limit exceeded. Please wait before sending more messages.',
                'retry_after': 3600
            }), 429
        
        # Check if OpenAI client is available
        if not openai_client:
            return jsonify({
                'response': generate_fallback_response(message),
                'source': 'fallback',
                'timestamp': datetime.now().isoformat()
            })
        
        # Get chat history if provided
        chat_history = data.get('chat_history', [])

        # Detect language and build appropriate system message
        detected_language = detect_message_language(message)
        system_message = get_multilingual_system_message(detected_language)

        # Build messages array
        messages = [
            {
                "role": "system",
                "content": system_message
            }
        ]

        # Add chat history
        messages.extend(chat_history)

        # Add current message
        messages.append({
            "role": "user",
            "content": message
        })

        # Make OpenAI API call
        try:
            response = openai_client.chat.completions.create(
                model="gpt-4o-mini",
                messages=messages,
                max_tokens=1000,
                temperature=0.7,
                stream=False
            )
            
            ai_response = response.choices[0].message.content
            
            # Log successful request
            logger.info(f"Successful API call for user {user_id}")
            
            return jsonify({
                'response': ai_response,
                'source': 'openai',
                'timestamp': datetime.now().isoformat(),
                'model': 'gpt-4o-mini',
                'tokens_used': response.usage.total_tokens if response.usage else 0
            })
            
        except Exception as api_error:
            logger.error(f"OpenAI API error: {api_error}")
            return jsonify({
                'response': generate_fallback_response(message),
                'source': 'fallback_after_error',
                'timestamp': datetime.now().isoformat(),
                'error': str(api_error)
            })
    
    except Exception as e:
        logger.error(f"Chat endpoint error: {e}")
        return jsonify({
            'response': 'I apologize, but I encountered an error. Please try again.',
            'source': 'error',
            'timestamp': datetime.now().isoformat()
        }), 500

@app.route('/api/tools/<tool_type>', methods=['POST'])
def handle_tool(tool_type):
    """Handle AI tool requests"""
    try:
        data = request.get_json()
        user_id = data.get('user_id', 'anonymous')
        
        # Check rate limit
        if not check_rate_limit(user_id):
            return jsonify({
                'error': 'Rate limit exceeded. Please wait before using tools.',
                'retry_after': 3600
            }), 429
        
        # Process different tools
        if tool_type == 'think-longer':
            return handle_think_longer(data)
        elif tool_type == 'deep-research':
            return handle_deep_research(data)
        elif tool_type == 'create-image':
            return handle_create_image(data)
        elif tool_type == 'image-to-video':
            return handle_image_to_video(data)
        elif tool_type == 'upload-image':
            return handle_image_upload(data)
        elif tool_type == 'web-search':
            return handle_web_search(data)
        elif tool_type == 'canvas':
            return handle_canvas(data)
        else:
            return jsonify({'error': 'Unknown tool type'}), 400
    
    except Exception as e:
        logger.error(f"Tool endpoint error: {e}")
        return jsonify({'error': 'Tool processing error'}), 500

def handle_think_longer(data):
    """Handle Think Longer tool"""
    prompt = data.get('prompt', '')
    depth = data.get('depth', 'standard')
    
    if not openai_client:
        return jsonify({
            'response': f"""
# 💡 Deep Analysis (Fallback Mode)

## Problem Analysis
{prompt}

## Multi-Perspective Analysis

### Analytical Approach
- Breaking down the core components
- Identifying key variables and constraints
- Systematic evaluation of options

### Creative Solutions
- Alternative approaches to consider
- Innovative methodologies
- Cross-disciplinary insights

### Practical Implementation
- Feasibility assessment
- Resource requirements
- Timeline considerations

## Recommendations
Based on {depth} analysis, here are key recommendations for moving forward.

*Note: This is a fallback response. Connect OpenAI API for enhanced analysis.*
            """,
            'tool': 'think-longer',
            'source': 'fallback',
            'timestamp': datetime.now().isoformat()
        })
    
    try:
        system_prompt = f"""You are an expert analyst. Provide {depth} analysis with:
        1. Multiple perspectives
        2. Step-by-step reasoning
        3. Comprehensive solutions
        4. Practical recommendations
        
        Be thorough and consider all angles of the problem."""
        
        response = openai_client.chat.completions.create(
            model="gpt-4o-mini",
            messages=[
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": prompt}
            ],
            max_tokens=1500,
            temperature=0.3
        )
        
        return jsonify({
            'response': response.choices[0].message.content,
            'tool': 'think-longer',
            'source': 'openai',
            'timestamp': datetime.now().isoformat()
        })
    
    except Exception as e:
        logger.error(f"Think Longer error: {e}")
        return jsonify({'error': 'Analysis failed'}), 500

def handle_deep_research(data):
    """Handle Deep Research tool"""
    topic = data.get('topic', '')
    scope = data.get('scope', 'overview')
    
    return jsonify({
        'response': f"""
# 🔍 Deep Research Results

## Research Topic: {topic}

### Executive Summary
Comprehensive research conducted on "{topic}" with {scope} scope.

### Key Findings
1. **Current State**: Latest developments and trends
2. **Historical Context**: Evolution and milestones
3. **Future Outlook**: Predictions and opportunities

### Sources & Citations
- Academic research papers
- Industry reports
- Expert analysis
- Current news articles

*Note: This is a simulated research response. Real implementation would include actual web search and data aggregation.*
        """,
        'tool': 'deep-research',
        'timestamp': datetime.now().isoformat()
    })

def handle_create_image(data):
    """Handle Create Image tool using Replicate API"""
    prompt = data.get('prompt', '')
    style = data.get('style', 'realistic')
    size = data.get('size', 'square')

    if not replicate_client:
        return jsonify({'error': 'Image generation service unavailable'}), 503

    try:
        # Map size to dimensions
        size_mapping = {
            'square': {'width': 1024, 'height': 1024},
            'landscape': {'width': 1792, 'height': 1024},
            'portrait': {'width': 1024, 'height': 1792}
        }
        dimensions = size_mapping.get(size, {'width': 1024, 'height': 1024})

        # Enhance prompt based on style
        enhanced_prompt = enhance_image_prompt(prompt, style)

        logger.info(f"Generating image with Replicate: {enhanced_prompt}")

        # Generate image with Replicate SDXL
        output = replicate_client.run(
            "stability-ai/sdxl:39ed52f2a78e934b3ba6e2a89f5b1c712de7dfea535525255b1aa35c5565e08b",
            input={
                "prompt": enhanced_prompt,
                "width": dimensions['width'],
                "height": dimensions['height'],
                "num_outputs": 1,
                "scheduler": "K_EULER",
                "num_inference_steps": 50,
                "guidance_scale": 7.5,
                "prompt_strength": 0.8,
                "refine": "expert_ensemble_refiner",
                "high_noise_frac": 0.8
            }
        )

        # Extract image URL from output
        image_url = output[0] if isinstance(output, list) else output

        if not image_url:
            raise Exception("No image URL returned from Replicate")

        logger.info(f"Image generated successfully: {image_url}")

        return jsonify({
            'response': f"""
# 🎨 Image Generated Successfully!

## Your AI-Generated Image

**Original Prompt**: "{prompt}"
**Enhanced Prompt**: "{enhanced_prompt}"
**Style**: {style}
**Size**: {size} ({dimensions['width']}x{dimensions['height']})

---

### 🖼️ Generated Image

<div style="text-align: center; margin: 2rem 0;">
    <img src="{image_url}" alt="AI Generated Image" style="max-width: 100%; height: auto; border-radius: 12px; box-shadow: 0 8px 25px rgba(0,0,0,0.3);">
</div>

### 📊 Image Details
- **Resolution**: {dimensions['width']}x{dimensions['height']}
- **Model**: Stable Diffusion XL
- **Quality**: High
- **Format**: PNG
- **Status**: ✅ Generated successfully

### 🔗 Image URL
[Download Image]({image_url})

### 💡 Pro Tips
- **Right-click** the image to save
- **Share the URL** to show others
- **Try different styles** for variety
- **Be specific** in prompts for better results

*Generated using Replicate Stable Diffusion XL API*
            """,
            'tool': 'create-image',
            'timestamp': datetime.now().isoformat(),
            'status': 'completed',
            'image_url': image_url,
            'enhanced_prompt': enhanced_prompt
        })

    except Exception as e:
        logger.error(f"Image generation error: {e}")
        error_msg = str(e)

        if "billing" in error_msg.lower() or "credits" in error_msg.lower():
            error_response = "❌ Replicate account has no credits for image generation."
        elif "content_policy" in error_msg.lower() or "safety" in error_msg.lower():
            error_response = "❌ Image prompt violates content policy. Please try a different prompt."
        elif "rate_limit" in error_msg.lower():
            error_response = "❌ Rate limit exceeded. Please wait before generating more images."
        elif "authentication" in error_msg.lower():
            error_response = "❌ Replicate API authentication failed. Check your API token."
        else:
            error_response = f"❌ Image generation failed: {error_msg}"

        return jsonify({'error': error_response}), 500

def enhance_image_prompt(prompt, style):
    """Enhance image prompt based on style"""
    style_enhancements = {
        'realistic': 'photorealistic, high quality, detailed',
        'artistic': 'artistic, creative, expressive, beautiful composition',
        'cartoon': 'cartoon style, animated, colorful, fun',
        'abstract': 'abstract art, creative interpretation, artistic',
        'digital-art': 'digital art, modern, sleek, professional'
    }

    enhancement = style_enhancements.get(style, 'high quality')
    return f"{prompt}, {enhancement}"

def handle_image_to_video(data):
    """Handle Image to Video conversion using Replicate API"""
    image_url = data.get('image_url', '')
    motion_strength = data.get('motion_strength', 5)
    duration = data.get('duration', 3)
    fps = data.get('fps', 24)

    if not replicate_client:
        return jsonify({'error': 'Video generation service unavailable'}), 503

    if not image_url:
        return jsonify({'error': 'Image URL is required'}), 400

    try:
        logger.info(f"Converting image to video: {image_url}")

        # Use alternative video generation model
        output = replicate_client.run(
            "anotherjesse/zeroscope-v2-xl:9f747673945c62801b13b84701c783929c0ee784e4748ec062204894dda1a351",
            input={
                "prompt": f"Animate this image with motion strength {motion_strength}/10",
                "init_image": image_url,
                "width": 512,
                "height": 512,
                "num_frames": duration * fps,
                "num_inference_steps": 50,
                "guidance_scale": 17.5,
                "negative_prompt": "static, still, motionless"
            }
        )

        # Extract video URL from output
        video_url = output if isinstance(output, str) else output[0] if isinstance(output, list) else None

        if not video_url:
            raise Exception("No video URL returned from Replicate")

        logger.info(f"Video generated successfully: {video_url}")

        return jsonify({
            'response': f"""
# 🎬 Video Generated Successfully!

## Your AI-Generated Video

**Source Image**: [View Image]({image_url})
**Motion Strength**: {motion_strength}/10
**Duration**: {duration} seconds
**FPS**: {fps}

---

### 🎥 Generated Video

<div style="text-align: center; margin: 2rem 0;">
    <video controls style="max-width: 100%; height: auto; border-radius: 12px; box-shadow: 0 8px 25px rgba(0,0,0,0.3);">
        <source src="{video_url}" type="video/mp4">
        Your browser does not support the video tag.
    </video>
</div>

### 📊 Video Details
- **Resolution**: HD Quality
- **Duration**: {duration} seconds
- **Frame Rate**: {fps} FPS
- **Motion Level**: {motion_strength}/10
- **Model**: Stable Video Diffusion
- **Status**: ✅ Generated successfully

### 🔗 Video URL
[Download Video]({video_url})

### 💡 Pro Tips
- **Right-click** the video to save
- **Share the URL** to show others
- **Try different motion strengths** for variety
- **Use high-quality images** for better results

*Generated using Replicate Stable Video Diffusion API*
            """,
            'tool': 'image-to-video',
            'timestamp': datetime.now().isoformat(),
            'status': 'completed',
            'video_url': video_url,
            'source_image': image_url,
            'motion_strength': motion_strength,
            'duration': duration,
            'fps': fps
        })

    except Exception as e:
        logger.error(f"Video generation error: {e}")
        error_msg = str(e)

        if "billing" in error_msg.lower() or "credits" in error_msg.lower():
            error_response = "❌ Replicate account has no credits for video generation."
        elif "rate_limit" in error_msg.lower():
            error_response = "❌ Rate limit exceeded. Please wait before generating more videos."
        elif "invalid" in error_msg.lower():
            error_response = "❌ Invalid image URL or parameters. Please check your inputs."
        else:
            error_response = f"❌ Video generation failed: {error_msg}"

        return jsonify({'error': error_response}), 500

# Add file upload route
@app.route('/api/upload-image', methods=['POST'])
def upload_image():
    """Handle image file uploads"""
    try:
        if 'image' not in request.files:
            return jsonify({'error': 'No image file provided'}), 400

        file = request.files['image']
        if file.filename == '':
            return jsonify({'error': 'No file selected'}), 400

        # Validate file type
        allowed_extensions = {'png', 'jpg', 'jpeg', 'gif', 'webp'}
        file_extension = file.filename.rsplit('.', 1)[1].lower() if '.' in file.filename else ''

        if file_extension not in allowed_extensions:
            return jsonify({'error': 'Invalid file type. Allowed: PNG, JPG, GIF, WebP'}), 400

        # Save file temporarily
        import uuid
        filename = f"{uuid.uuid4()}.{file_extension}"
        filepath = os.path.join('temp_uploads', filename)

        # Create temp directory if it doesn't exist
        os.makedirs('temp_uploads', exist_ok=True)

        file.save(filepath)

        # Return file URL
        file_url = f"http://localhost:5001/temp_uploads/{filename}"

        logger.info(f"Image uploaded successfully: {filename}")

        return jsonify({
            'success': True,
            'image_url': file_url,
            'filename': filename,
            'message': 'Image uploaded successfully'
        })

    except Exception as e:
        logger.error(f"Image upload error: {e}")
        return jsonify({'error': f'Upload failed: {str(e)}'}), 500

# Serve uploaded files
@app.route('/temp_uploads/<filename>')
def serve_uploaded_file(filename):
    """Serve uploaded image files"""
    try:
        return send_from_directory('temp_uploads', filename)
    except Exception as e:
        logger.error(f"File serve error: {e}")
        return jsonify({'error': 'File not found'}), 404

def detect_message_language(message):
    """Detect the language of the message"""
    message_lower = message.lower()

    # Somali language indicators
    somali_words = ['waa', 'maxaa', 'sidee', 'marka', 'haddii', 'laakiin', 'oo', 'ka', 'ku', 'la', 'aan', 'waxaan', 'tahay', 'yahay']

    # Arabic language indicators
    arabic_words = ['السلام', 'مرحبا', 'كيف', 'ماذا', 'هل', 'في', 'من', 'إلى', 'على', 'هذا', 'ذلك']

    # French language indicators
    french_words = ['bonjour', 'comment', 'vous', 'êtes', 'dans', 'avec', 'pour', 'que', 'qui', 'est', 'une', 'des']

    # Spanish language indicators
    spanish_words = ['hola', 'cómo', 'está', 'qué', 'para', 'con', 'por', 'que', 'una', 'del', 'los', 'las']

    # German language indicators
    german_words = ['hallo', 'wie', 'ist', 'das', 'der', 'die', 'und', 'mit', 'für', 'auf', 'von', 'zu']

    # Italian language indicators
    italian_words = ['ciao', 'come', 'che', 'per', 'con', 'una', 'del', 'della', 'sono', 'hai', 'molto']

    # Count matches for each language
    somali_count = sum(1 for word in somali_words if word in message_lower)
    arabic_count = sum(1 for word in arabic_words if word in message_lower)
    french_count = sum(1 for word in french_words if word in message_lower)
    spanish_count = sum(1 for word in spanish_words if word in message_lower)
    german_count = sum(1 for word in german_words if word in message_lower)
    italian_count = sum(1 for word in italian_words if word in message_lower)

    # Determine language based on highest count
    language_scores = {
        'somali': somali_count,
        'arabic': arabic_count,
        'french': french_count,
        'spanish': spanish_count,
        'german': german_count,
        'italian': italian_count
    }

    # Get language with highest score
    detected = max(language_scores, key=language_scores.get)

    # If no clear match, default to English
    if language_scores[detected] == 0:
        return 'english'

    return detected

def get_multilingual_system_message(language):
    """Get system message based on detected language"""

    system_messages = {
        'somali': """Waxaad tahay Tincada AI, kaaliye AI ah oo aad u aqoon badan. Waxaad ku hadashaa af-Soomaali si dabiici ah.
        Waxaad bixisaa jawaabo faa'iido leh, qurux badan, oo af-Soomaali nadiif ah.
        Haddii la weydiiyo coding ama tiknoolajiyad, bixi tusaalooyin cad oo sharaxaad leh.
        Waxaad karti kartaa inaad ku hadashid luqado kala duwan marka loo baahdo.""",

        'arabic': """أنت Tincada AI، مساعد ذكي مفيد ومتخصص. تتحدث العربية بطلاقة وتقدم إجابات مفيدة ودقيقة.
        إذا سُئلت عن البرمجة أو التكنولوجيا، قدم أمثلة واضحة مع الشرح.
        يمكنك التحدث بلغات مختلفة حسب الحاجة.""",

        'french': """Vous êtes Tincada AI, un assistant IA utile et compétent. Vous parlez français couramment et fournissez des réponses utiles et précises.
        Si on vous demande de la programmation ou de la technologie, donnez des exemples clairs avec des explications.
        Vous pouvez parler différentes langues selon les besoins.""",

        'spanish': """Eres Tincada AI, un asistente de IA útil y competente. Hablas español con fluidez y proporcionas respuestas útiles y precisas.
        Si te preguntan sobre programación o tecnología, da ejemplos claros con explicaciones.
        Puedes hablar diferentes idiomas según sea necesario.""",

        'german': """Sie sind Tincada AI, ein hilfreicher und kompetenter KI-Assistent. Sie sprechen fließend Deutsch und geben hilfreiche und genaue Antworten.
        Wenn Sie nach Programmierung oder Technologie gefragt werden, geben Sie klare Beispiele mit Erklärungen.
        Sie können je nach Bedarf verschiedene Sprachen sprechen.""",

        'italian': """Sei Tincada AI, un assistente IA utile e competente. Parli italiano fluentemente e fornisci risposte utili e accurate.
        Se ti viene chiesto di programmazione o tecnologia, dai esempi chiari con spiegazioni.
        Puoi parlare diverse lingue secondo necessità.""",

        'english': """You are Tincada AI, a helpful and knowledgeable AI assistant. You speak multiple languages fluently and provide helpful, accurate information.
        If asked about coding or technology, provide clear examples with explanations.
        You can communicate in different languages as needed, including Somali, Arabic, French, Spanish, German, and Italian."""
    }

    return system_messages.get(language, system_messages['english'])

def handle_web_search(data):
    """Handle Web Search tool"""
    query = data.get('query', '')
    search_type = data.get('type', 'general')
    
    return jsonify({
        'response': f"""
# 🌐 Web Search Results

**Query**: "{query}"
**Type**: {search_type}

## Top Results

### 1. Primary Source
**Title**: Latest information on {query}
**Summary**: Comprehensive overview with current data and analysis
**Relevance**: 98%

### 2. News Article  
**Title**: Recent developments in {query}
**Published**: 2 hours ago
**Summary**: Breaking news and latest updates
**Relevance**: 95%

### 3. Academic Source
**Title**: Research paper on {query}
**Authors**: Expert researchers
**Summary**: Peer-reviewed analysis with statistical data
**Relevance**: 92%

*Note: This is a simulated search. Real implementation requires web search API integration.*
        """,
        'tool': 'web-search',
        'timestamp': datetime.now().isoformat()
    })

def handle_canvas(data):
    """Handle Canvas tool"""
    canvas_type = data.get('type', 'document')
    title = data.get('title', 'Untitled Canvas')
    
    return jsonify({
        'response': f"""
# ✏️ Canvas Created: {title}

**Type**: {canvas_type}
**Status**: Ready for collaboration

## Canvas Features
- ✅ Real-time editing
- ✅ Multi-user collaboration  
- ✅ Version history
- ✅ Export options

## Getting Started
1. **Open Canvas**: Click the link below
2. **Invite Collaborators**: Share with team members
3. **Start Creating**: Use the toolbar to add content
4. **Save & Export**: Your work is automatically saved

### 🔗 Canvas Link
**[Open {title} Canvas](javascript:void(0))**

*Canvas workspace is now ready for use*
        """,
        'tool': 'canvas',
        'timestamp': datetime.now().isoformat(),
        'canvas_id': f"canvas_{int(time.time())}"
    })

def generate_fallback_response(message):
    """Generate fallback response when OpenAI is unavailable"""
    message_lower = message.lower()
    
    if any(word in message_lower for word in ['hello', 'hi', 'hey', 'salam']):
        return "Hello! I'm Tincada AI. How can I help you today? (Running in fallback mode - connect OpenAI API for enhanced responses)"
    
    elif any(word in message_lower for word in ['help', 'caawi']):
        return "I can help you with various tasks including answering questions, writing, coding, and analysis. What would you like assistance with?"
    
    elif any(word in message_lower for word in ['code', 'programming', 'python', 'javascript']):
        return "I can help you with programming in various languages. What programming task are you working on?"
    
    elif any(word in message_lower for word in ['somali', 'somalia']):
        return "Waan ku caawin karaa af-Soomaaliga. Maxaad rabto in aan kaa caawiyo?"
    
    else:
        return f"I understand you're asking about: '{message}'. I'm currently running in fallback mode. For enhanced AI responses, please connect the OpenAI API. How else can I help you?"

@app.route('/api/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({
        'status': 'healthy',
        'timestamp': datetime.now().isoformat(),
        'openai_available': openai_client is not None,
        'openai_library_installed': OPENAI_AVAILABLE,
        'replicate_available': replicate_client is not None,
        'features': {
            'chat': openai_client is not None,
            'image_generation': replicate_client is not None,
            'video_generation': replicate_client is not None,
            'multilingual': True,
            'replicate_models': {
                'image': 'stability-ai/sdxl',
                'video': 'anotherjesse/zeroscope-v2-xl'
            }
        }
    })

if __name__ == '__main__':
    print("🚀 Starting Tincada AI Flask Server...")
    print("📱 Dashboard: http://localhost:5001")
    print("🔧 API Health: http://localhost:5001/api/health")
    
    if not OPENAI_AVAILABLE:
        print("⚠️  OpenAI library not installed. Install with: pip install openai flask flask-cors")
    elif not os.getenv('OPENAI_API_KEY'):
        print("⚠️  OPENAI_API_KEY environment variable not set!")
        print("   Set it with: export OPENAI_API_KEY='your-api-key-here'")
        print("   The server will run with fallback responses.")
    else:
        print("✅ OpenAI API configured and ready!")
    
    app.run(host='0.0.0.0', port=5001, debug=True)
