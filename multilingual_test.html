<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tincada AI - Multilingual Test</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 900px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .language-selector {
            padding: 20px;
            background: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
        }

        .language-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            justify-content: center;
        }

        .lang-btn {
            padding: 10px 20px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s;
        }

        .lang-btn:hover {
            background: #0056b3;
            transform: translateY(-2px);
        }

        .lang-btn.active {
            background: #28a745;
        }

        .chat-container {
            padding: 30px;
        }

        .chat-messages {
            height: 400px;
            overflow-y: auto;
            border: 2px solid #e0e0e0;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            background: #f9f9f9;
        }

        .message {
            margin-bottom: 15px;
            padding: 15px;
            border-radius: 15px;
            max-width: 80%;
        }

        .user-message {
            background: #007bff;
            color: white;
            margin-left: auto;
            text-align: right;
        }

        .ai-message {
            background: #e9ecef;
            color: #333;
            margin-right: auto;
            text-align: left;
        }

        .input-container {
            display: flex;
            gap: 10px;
        }

        .message-input {
            flex: 1;
            padding: 15px;
            border: 2px solid #ddd;
            border-radius: 25px;
            font-size: 16px;
            outline: none;
        }

        .send-button {
            padding: 15px 30px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 20px;
            color: #666;
        }

        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #3498db;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .status {
            padding: 10px;
            text-align: center;
            font-weight: bold;
            border-radius: 10px;
            margin-bottom: 20px;
        }

        .status.connected {
            background: #d4edda;
            color: #155724;
        }

        .status.disconnected {
            background: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🌍 Tincada AI - Multilingual</h1>
            <p>AI that speaks ALL world languages - الذكي متعدد اللغات - AI luqado badan ku hadla</p>
        </div>

        <div class="language-selector">
            <h3 style="text-align: center; margin-bottom: 15px;">🗣️ Choose Your Language / اختر لغتك / Dooro luqaddaada</h3>
            <div class="language-buttons">
                <button class="lang-btn" onclick="setLanguage('so', 'Salam alaykum! Sidee tahay?')">🇸🇴 Somali</button>
                <button class="lang-btn" onclick="setLanguage('ar', 'السلام عليكم! كيف حالك؟')">🇸🇦 Arabic</button>
                <button class="lang-btn" onclick="setLanguage('en', 'Hello! How are you?')">🇺🇸 English</button>
                <button class="lang-btn" onclick="setLanguage('es', '¡Hola! ¿Cómo estás?')">🇪🇸 Spanish</button>
                <button class="lang-btn" onclick="setLanguage('fr', 'Bonjour! Comment allez-vous?')">🇫🇷 French</button>
                <button class="lang-btn" onclick="setLanguage('de', 'Hallo! Wie geht es dir?')">🇩🇪 German</button>
                <button class="lang-btn" onclick="setLanguage('it', 'Ciao! Come stai?')">🇮🇹 Italian</button>
                <button class="lang-btn" onclick="setLanguage('tr', 'Merhaba! Nasılsın?')">🇹🇷 Turkish</button>
                <button class="lang-btn" onclick="setLanguage('sw', 'Hujambo! Habari?')">🇰🇪 Swahili</button>
                <button class="lang-btn" onclick="setLanguage('am', 'ሰላም! እንዴት ነህ?')">🇪🇹 Amharic</button>
            </div>
        </div>

        <div class="chat-container">
            <div id="status" class="status disconnected">
                🔴 Connecting to server...
            </div>

            <div id="chatMessages" class="chat-messages">
                <div class="message ai-message">
                    <strong>Tincada AI:</strong> 🌍 Welcome! I can speak in ANY language you prefer. Choose a language above or just start typing in your preferred language!
                </div>
            </div>

            <div class="loading" id="loading">
                <div class="spinner"></div>
                <p>AI is responding...</p>
            </div>

            <div class="input-container">
                <input type="text" id="messageInput" class="message-input" 
                       placeholder="Type your message in any language..." 
                       onkeypress="handleKeyPress(event)">
                <button onclick="sendMessage()" id="sendButton" class="send-button">Send</button>
            </div>
        </div>
    </div>

    <script>
        let currentLanguage = 'en';
        let chatHistory = [];

        // Language greetings
        const greetings = {
            'so': 'Salam alaykum! Sidee tahay?',
            'ar': 'السلام عليكم! كيف حالك؟',
            'en': 'Hello! How are you?',
            'es': '¡Hola! ¿Cómo estás?',
            'fr': 'Bonjour! Comment allez-vous?',
            'de': 'Hallo! Wie geht es dir?',
            'it': 'Ciao! Come stai?',
            'tr': 'Merhaba! Nasılsın?',
            'sw': 'Hujambo! Habari?',
            'am': 'ሰላም! እንዴት ነህ?'
        };

        // Set language and send greeting
        function setLanguage(lang, greeting) {
            currentLanguage = lang;
            
            // Update active button
            document.querySelectorAll('.lang-btn').forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');
            
            // Send greeting
            document.getElementById('messageInput').value = greeting;
            sendMessage();
        }

        // Check server status
        async function checkServerStatus() {
            try {
                const response = await fetch('/api/health');
                const data = await response.json();
                
                const statusDiv = document.getElementById('status');
                if (response.ok) {
                    statusDiv.className = 'status connected';
                    statusDiv.innerHTML = `🟢 Server is running (${data.mock_mode ? 'Mock Mode' : 'OpenAI Mode'})`;
                } else {
                    throw new Error('Server error');
                }
            } catch (error) {
                const statusDiv = document.getElementById('status');
                statusDiv.className = 'status disconnected';
                statusDiv.innerHTML = '🔴 Cannot connect to server';
            }
        }

        // Send message to AI
        async function sendMessage() {
            const input = document.getElementById('messageInput');
            const message = input.value.trim();
            
            if (!message) return;

            // Add user message to chat
            addMessage(message, 'user');
            input.value = '';
            
            // Show loading
            document.getElementById('loading').style.display = 'block';
            document.getElementById('sendButton').disabled = true;

            try {
                const response = await fetch('/api/chat', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        message: message,
                        user_id: 'multilingual_user',
                        chat_history: chatHistory
                    })
                });

                const data = await response.json();
                
                if (response.ok) {
                    addMessage(data.response, 'ai');
                    
                    // Update chat history
                    chatHistory.push({role: 'user', content: message});
                    chatHistory.push({role: 'assistant', content: data.response});
                    
                    // Keep only last 10 messages
                    if (chatHistory.length > 10) {
                        chatHistory = chatHistory.slice(-10);
                    }
                } else {
                    addMessage('Error: ' + data.error, 'ai');
                }
            } catch (error) {
                addMessage('Error: ' + error.message, 'ai');
            } finally {
                document.getElementById('loading').style.display = 'none';
                document.getElementById('sendButton').disabled = false;
            }
        }

        // Add message to chat
        function addMessage(message, sender) {
            const chatMessages = document.getElementById('chatMessages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${sender}-message`;
            
            if (sender === 'user') {
                messageDiv.innerHTML = `<strong>You:</strong> ${message}`;
            } else {
                messageDiv.innerHTML = `<strong>Tincada AI:</strong> ${message.replace(/\n/g, '<br>')}`;
            }
            
            chatMessages.appendChild(messageDiv);
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        // Handle Enter key
        function handleKeyPress(event) {
            if (event.key === 'Enter') {
                sendMessage();
            }
        }

        // Initialize
        checkServerStatus();
        setInterval(checkServerStatus, 30000); // Check every 30 seconds
    </script>
</body>
</html>
