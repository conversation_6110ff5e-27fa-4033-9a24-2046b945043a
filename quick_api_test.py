import requests
import json

# Test basic connection
try:
    print("Testing server connection...")
    response = requests.get('http://localhost:5001/api/health')
    print(f"Health check: {response.status_code}")
    if response.status_code == 200:
        print(f"Response: {response.json()}")
    
    # Test chat
    print("\nTesting chat API...")
    chat_data = {
        "message": "Salam alaykum",
        "user_id": "test"
    }
    
    chat_response = requests.post('http://localhost:5001/api/chat', json=chat_data)
    print(f"Chat response: {chat_response.status_code}")
    if chat_response.status_code == 200:
        result = chat_response.json()
        print(f"AI Response: {result['response'][:100]}...")
        print(f"Source: {result['source']}")
    else:
        print(f"Error: {chat_response.text}")
        
except Exception as e:
    print(f"Error: {e}")
