<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple API Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background: #1a1a1a;
            color: white;
        }
        .container {
            background: #2a2a2a;
            padding: 20px;
            border-radius: 10px;
        }
        input, button {
            width: 100%;
            padding: 10px;
            margin: 10px 0;
            border: 1px solid #444;
            border-radius: 5px;
            background: #333;
            color: white;
        }
        button {
            background: #007bff;
            cursor: pointer;
        }
        .response {
            background: #333;
            padding: 15px;
            border-radius: 5px;
            margin-top: 20px;
            white-space: pre-wrap;
        }
        .success { color: #28a745; }
        .error { color: #dc3545; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Simple API Test</h1>
        
        <input type="text" id="message" placeholder="Geli su'aal" value="Hello, how are you?">
        <button onclick="testAPI()">Test API</button>
        
        <div id="result" class="response" style="display: none;"></div>
    </div>

    <script>
        async function testAPI() {
            const message = document.getElementById('message').value;
            const result = document.getElementById('result');
            
            result.style.display = 'block';
            result.innerHTML = '⏳ Testing API...';
            
            try {
                const response = await fetch('/api_test.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ message: message })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    result.innerHTML = `<span class="success">✅ SUCCESS!</span>\n\nAI Response: ${data.message}\n\nModel: ${data.model}\nTokens: ${data.tokens_used}\nTime: ${data.timestamp}`;
                } else {
                    result.innerHTML = `<span class="error">❌ ERROR!</span>\n\n${data.error}`;
                }
                
            } catch (error) {
                result.innerHTML = `<span class="error">❌ CONNECTION ERROR!</span>\n\n${error.message}`;
            }
        }
    </script>
</body>
</html>
