<!DOCTYPE html>
<html lang="so">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Login - Tincada AI</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }

        .login-container {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 3rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 450px;
            text-align: center;
        }

        .logo-title {
            font-size: 2rem;
            font-weight: 700;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 2rem;
        }

        .form-group {
            margin-bottom: 1.5rem;
            text-align: left;
        }

        .form-label {
            display: block;
            color: white;
            font-weight: 500;
            margin-bottom: 0.5rem;
            font-size: 0.9rem;
        }

        .form-input {
            width: 100%;
            padding: 12px 15px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            color: white;
            font-family: inherit;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .form-input:focus {
            outline: none;
            border-color: #4ecdc4;
            background: rgba(255, 255, 255, 0.15);
            box-shadow: 0 0 0 3px rgba(78, 205, 196, 0.2);
        }

        .form-input::placeholder {
            color: rgba(255, 255, 255, 0.6);
        }

        .login-btn {
            width: 100%;
            background: linear-gradient(45deg, #4ecdc4, #44a08d);
            border: none;
            color: white;
            padding: 15px;
            border-radius: 10px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(78, 205, 196, 0.3);
            margin-bottom: 1rem;
        }

        .login-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(78, 205, 196, 0.4);
        }

        .divider {
            display: flex;
            align-items: center;
            margin: 1.5rem 0;
            color: rgba(255, 255, 255, 0.6);
            font-size: 0.9rem;
        }

        .divider::before,
        .divider::after {
            content: '';
            flex: 1;
            height: 1px;
            background: rgba(255, 255, 255, 0.2);
        }

        .divider span {
            padding: 0 1rem;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 20px;
            padding: 5px 15px;
        }

        .google-btn {
            width: 100%;
            background: #ffffff;
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: #333;
            padding: 15px;
            border-radius: 10px;
            font-size: 1rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 12px;
        }

        .google-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
            border-color: #4285f4;
        }

        .apple-btn {
            width: 100%;
            background: #000000;
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: #ffffff;
            padding: 15px;
            border-radius: 10px;
            font-size: 1rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 12px;
        }

        .apple-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.4);
            background: #1a1a1a;
        }

        .status {
            margin-top: 2rem;
            padding: 1rem;
            background: rgba(76, 175, 80, 0.2);
            border: 1px solid rgba(76, 175, 80, 0.3);
            border-radius: 10px;
            color: #4caf50;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <h1 class="logo-title">🔐 Test Login</h1>
        
        <form id="loginForm">
            <div class="form-group">
                <label class="form-label" for="email">Email</label>
                <input type="email" id="email" class="form-input" placeholder="Enter your email" required>
            </div>

            <div class="form-group">
                <label class="form-label" for="password">Password</label>
                <input type="password" id="password" class="form-input" placeholder="Enter your password" required>
            </div>

            <button type="submit" class="login-btn">
                <i class="fas fa-sign-in-alt"></i>
                Login
            </button>

            <div class="divider">
                <span>or</span>
            </div>

            <button type="button" class="google-btn" onclick="testGoogleLogin()">
                <i class="fab fa-google"></i>
                <span>Sign in with Google</span>
            </button>

            <button type="button" class="apple-btn" onclick="testAppleLogin()">
                <i class="fab fa-apple"></i>
                <span>Sign in with Apple</span>
            </button>
        </form>

        <div class="status" id="status">
            ✅ Test login page is working!<br>
            🌐 Server: http://localhost:5001<br>
            📱 All buttons are functional
        </div>
    </div>

    <script>
        // Test form submission
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            e.preventDefault();
            document.getElementById('status').innerHTML = '✅ Form submitted successfully!<br>📧 Email: ' + document.getElementById('email').value;
        });

        function testGoogleLogin() {
            document.getElementById('status').innerHTML = '🔄 Google login clicked!<br>✅ Button is working correctly';
        }

        function testAppleLogin() {
            document.getElementById('status').innerHTML = '🔄 Apple login clicked!<br>✅ Button is working correctly';
        }

        // Test page load
        window.addEventListener('load', function() {
            console.log('✅ Test login page loaded successfully');
            setTimeout(() => {
                document.getElementById('status').innerHTML += '<br>⏰ Page loaded at: ' + new Date().toLocaleTimeString();
            }, 1000);
        });
    </script>
</body>
</html>
