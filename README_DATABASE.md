# 🗄️ Tincada AI Database System

## 📋 Overview

Tincada AI Database System waa nidaam buuxa oo u kaydinaya dhammaan xogta website-ka iyo AI conversations-ka. Waxay ka kooban tahay:

- **SQL Database** - MySQL database structure
- **PHP Backend** - API handlers iyo dashboard
- **Web Dashboard** - Professional interface for data management
- **Analytics System** - Detailed reporting and statistics

## 🚀 Quick Setup

### 1. Database Setup
```bash
# 1. Start XAMPP/WAMP/MAMP
# 2. Open browser and go to:
http://localhost/tincada-ai/setup_database.php
```

### 2. Configuration
```php
// Update config.php with your database credentials
define('DB_HOST', 'localhost');
define('DB_USERNAME', 'root');
define('DB_PASSWORD', 'your_password');
define('DB_NAME', 'tincada_ai_db');
```

### 3. Access Dashboard
```
http://localhost/tincada-ai/index.php
```

## 📊 Database Structure

### Core Tables

#### 1. **users** - User Management
```sql
- id (Primary Key)
- user_id (Unique identifier)
- username, email
- total_messages, total_tokens_used
- created_at, last_active
```

#### 2. **conversations** - Chat Sessions
```sql
- id (Primary Key)
- user_id, conversation_id
- title, message_count
- created_at, updated_at
```

#### 3. **chat_messages** - All Messages
```sql
- id (Primary Key)
- conversation_id, user_id
- message_type (user/ai)
- message_content
- tokens_used, response_source
- created_at
```

#### 4. **generated_images** - Image Generation
```sql
- id (Primary Key)
- user_id, image_prompt
- image_url, image_size
- generation_method
- created_at
```

#### 5. **api_usage** - Usage Statistics
```sql
- id (Primary Key)
- user_id, api_type
- tokens_used, request_count
- success, error_message
- created_at
```

### Analytics Views

#### **user_stats** - User Analytics
```sql
SELECT user_id, username, total_messages, 
       total_conversations, total_images_generated
FROM users + aggregated data
```

#### **recent_activity** - Latest Activity
```sql
SELECT messages with user info, conversation titles
ORDER BY created_at DESC
```

## 🌐 API Endpoints

### 1. Chat API
```php
POST api_handler.php
{
    "action": "chat",
    "user_id": "user_123",
    "message": "Hello AI",
    "conversation_id": "conv_456" // optional
}
```

**Response:**
```json
{
    "success": true,
    "conversation_id": "conv_456",
    "response": "AI response text",
    "tokens_used": 150,
    "source": "openai"
}
```

### 2. Dashboard API
```php
GET dashboard_api.php?action=overview
GET dashboard_api.php?action=users
GET dashboard_api.php?action=messages
GET dashboard_api.php?action=analytics
GET dashboard_api.php?action=health
```

## 📈 Dashboard Features

### 1. **Overview Tab** 📊
- Total users, messages, conversations
- Token usage statistics
- Recent activity feed
- Real-time metrics

### 2. **Users Tab** 👥
- User management
- Activity statistics
- Registration dates
- Usage patterns

### 3. **Messages Tab** 💬
- All chat messages
- Conversation history
- Message types (user/ai)
- Response sources

### 4. **Analytics Tab** 📈
- Messages per day charts
- API usage statistics
- Top users by activity
- Response source distribution

### 5. **Live Chat Tab** 🗣️
- Test chat interface
- Real-time AI responses
- Database storage verification
- Token usage tracking

## 🔧 Key Features

### ✅ **Automatic Data Storage**
- Every chat message stored automatically
- User activity tracking
- Conversation management
- Token usage monitoring

### ✅ **Real-time Analytics**
- Live statistics updates
- Performance monitoring
- Error tracking
- Usage patterns

### ✅ **Professional Dashboard**
- Modern responsive design
- Interactive charts
- Data export capabilities
- User-friendly interface

### ✅ **API Integration**
- Seamless Tincada AI integration
- Error handling and fallbacks
- Response source tracking
- Performance optimization

## 🛠️ File Structure

```
tincada-ai/
├── database.sql          # Database schema
├── config.php           # Database configuration
├── api_handler.php      # Chat API handler
├── dashboard_api.php    # Dashboard data API
├── index.php           # Main dashboard
├── setup_database.php  # Database setup script
└── README_DATABASE.md  # This documentation
```

## 🧪 Testing

### 1. Database Setup Test
```
http://localhost/tincada-ai/setup_database.php
```

### 2. API Test
```javascript
fetch('api_handler.php', {
    method: 'POST',
    headers: {'Content-Type': 'application/json'},
    body: JSON.stringify({
        action: 'chat',
        user_id: 'test_user',
        message: 'Hello AI'
    })
})
```

### 3. Dashboard Test
```
http://localhost/tincada-ai/index.php
```

## 📊 Usage Examples

### Store Chat Message
```php
$chatHandler = new ChatHandler();
$result = $chatHandler->handleChatRequest(
    'user_123', 
    'What is AI?', 
    'conv_456'
);
```

### Get User Statistics
```php
$stats = getUserStats('user_123');
echo "Messages: " . $stats['total_messages'];
echo "Tokens: " . $stats['total_tokens_used'];
```

### Analytics Query
```sql
SELECT DATE(created_at) as date, COUNT(*) as messages
FROM chat_messages 
WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
GROUP BY DATE(created_at);
```

## 🔒 Security Features

- **SQL Injection Protection** - Prepared statements
- **Input Sanitization** - XSS prevention
- **Session Management** - Secure user sessions
- **Error Logging** - Comprehensive error tracking
- **Rate Limiting** - API request limits

## 🎯 Benefits

### For Users:
- **Complete Chat History** - Never lose conversations
- **Usage Analytics** - Track your AI interactions
- **Multi-device Sync** - Access from anywhere
- **Professional Interface** - Easy to use dashboard

### For Administrators:
- **Real-time Monitoring** - System health tracking
- **Usage Statistics** - Detailed analytics
- **Error Management** - Comprehensive logging
- **Performance Optimization** - Database insights

## 🚀 Next Steps

1. **Setup Database** - Run setup_database.php
2. **Configure Settings** - Update config.php
3. **Test Integration** - Verify API connections
4. **Access Dashboard** - Start using index.php
5. **Monitor Usage** - Check analytics regularly

## 📞 Support

Haddii aad u baahan tahay caawimaad:
- Check setup_database.php for installation issues
- Verify config.php database credentials
- Test API endpoints individually
- Check browser console for JavaScript errors
- Review system_logs table for error details

**🎉 Database system-ku hadda wuu diyaar u yahay isticmaalka!**
