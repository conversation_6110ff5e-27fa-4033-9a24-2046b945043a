<!DOCTYPE html>
<html lang="so">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test AI Code Response - Tincada AI</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
            padding: 2rem;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 2rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        h1 {
            text-align: center;
            font-size: 2.5rem;
            margin-bottom: 2rem;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .chat-simulation {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
        }

        .message {
            display: flex;
            gap: 15px;
            margin-bottom: 2rem;
            align-items: flex-start;
        }

        .message.user {
            flex-direction: row-reverse;
        }

        .message-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;
        }

        .message.user .message-avatar {
            background: #4ecdc4;
            color: white;
        }

        .message.ai .message-avatar {
            background: #2d2d2d;
            overflow: hidden;
        }

        .message.ai .message-avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .message-content {
            flex: 1;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 1.5rem;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .message.user .message-content {
            background: rgba(78, 205, 196, 0.2);
            border-color: rgba(78, 205, 196, 0.3);
        }

        .message-text {
            line-height: 1.6;
        }

        .message-time {
            font-size: 0.8rem;
            opacity: 0.7;
            margin-top: 0.5rem;
        }

        .test-buttons {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
            margin-bottom: 2rem;
        }

        .test-btn {
            background: linear-gradient(45deg, #4ecdc4, #44a08d);
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 25px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .test-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(78, 205, 196, 0.4);
        }

        .inline-code {
            background: rgba(0, 0, 0, 0.3);
            padding: 2px 6px;
            border-radius: 4px;
            font-family: 'Consolas', 'Monaco', monospace;
            font-size: 0.9em;
        }

        @media (max-width: 768px) {
            body { padding: 1rem; }
            .container { padding: 1rem; }
            h1 { font-size: 2rem; }
            .test-buttons { flex-direction: column; }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🤖 AI Code Response Test</h1>
        
        <div class="test-buttons">
            <button class="test-btn" onclick="testJavaScriptCode()">
                <i class="fab fa-js"></i> Test JavaScript
            </button>
            <button class="test-btn" onclick="testPythonCode()">
                <i class="fab fa-python"></i> Test Python
            </button>
            <button class="test-btn" onclick="testHTMLCode()">
                <i class="fab fa-html5"></i> Test HTML
            </button>
            <button class="test-btn" onclick="testCSSCode()">
                <i class="fab fa-css3"></i> Test CSS
            </button>
            <button class="test-btn" onclick="clearMessages()">
                <i class="fas fa-trash"></i> Clear
            </button>
        </div>
        
        <div class="chat-simulation" id="chatSimulation">
            <!-- Messages will be added here -->
        </div>
    </div>

    <script src="ai-code-component.js"></script>
    <script>
        // Simulate AI message formatting like dashboard
        function formatAIMessage(text) {
            // First escape HTML
            let formatted = escapeHtml(text);

            // Format code blocks with AI Code Component
            formatted = formatted.replace(/```(\w+)?\n([\s\S]*?)```/g, (match, language, code) => {
                const lang = language || 'javascript';
                // Use AI Code Block component for professional code display
                return aiCodeBlock.createCodeBlock(code.trim(), lang);
            });

            // Format inline code
            formatted = formatted.replace(/`([^`]+)`/g, '<code class="inline-code">$1</code>');

            // Format bold text
            formatted = formatted.replace(/\*\*([^*]+)\*\*/g, '<strong>$1</strong>');

            // Format italic text
            formatted = formatted.replace(/\*([^*]+)\*/g, '<em>$1</em>');

            return formatted;
        }

        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML.replace(/\n/g, '<br>');
        }

        function addMessage(type, text) {
            const chatSimulation = document.getElementById('chatSimulation');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}`;
            
            const avatar = document.createElement('div');
            avatar.className = 'message-avatar';
            
            if (type === 'user') {
                avatar.innerHTML = '<i class="fas fa-user"></i>';
            } else {
                avatar.innerHTML = '<img src="images/icon.jpg" alt="Tincada AI" onerror="this.outerHTML=\'<i class=&quot;fas fa-robot&quot;></i>\'">';
            }
            
            const content = document.createElement('div');
            content.className = 'message-content';
            
            const formattedText = type === 'ai' ? formatAIMessage(text) : escapeHtml(text);
            
            content.innerHTML = `
                <div class="message-text">${formattedText}</div>
                <div class="message-time">${new Date().toLocaleTimeString()}</div>
            `;
            
            messageDiv.appendChild(avatar);
            messageDiv.appendChild(content);
            chatSimulation.appendChild(messageDiv);
            
            // Scroll to bottom
            chatSimulation.scrollTop = chatSimulation.scrollHeight;
        }

        function testJavaScriptCode() {
            addMessage('user', 'Samee JavaScript function oo check gareynaya user authentication');
            
            setTimeout(() => {
                const aiResponse = `Halkan waa JavaScript function oo check gareynaya user authentication:

\`\`\`javascript
// Check user authentication status
function checkUserAuth() {
    const token = localStorage.getItem('auth_token');
    const user = localStorage.getItem('user_data');
    
    if (!token || !user) {
        console.log('User not authenticated');
        return false;
    }
    
    try {
        const userData = JSON.parse(user);
        const tokenExpiry = userData.tokenExpiry;
        
        if (Date.now() > tokenExpiry) {
            console.log('Token expired');
            localStorage.removeItem('auth_token');
            localStorage.removeItem('user_data');
            return false;
        }
        
        return {
            isAuthenticated: true,
            user: userData,
            token: token
        };
    } catch (error) {
        console.error('Auth check error:', error);
        return false;
    }
}

// Usage example
const authStatus = checkUserAuth();
if (authStatus) {
    console.log('Welcome back,', authStatus.user.name);
} else {
    window.location.href = '/login.html';
}
\`\`\`

Function-kan wuxuu check gareeyaa:
- **Token existence** - localStorage-ka token ka jira
- **User data** - User information la kaydsaday
- **Token expiry** - Token-ku ma dhacay
- **Error handling** - JSON parsing errors

Isticmaal \`checkUserAuth()\` si aad u hubiso user authentication status.`;
                
                addMessage('ai', aiResponse);
            }, 1000);
        }

        function testPythonCode() {
            addMessage('user', 'Samee Python script oo file-yada organize gareynaya');
            
            setTimeout(() => {
                const aiResponse = `Halkan waa Python script oo file-yada organize gareynaya by extension:

\`\`\`python
import os
import shutil
from pathlib import Path

def organize_files(source_dir, target_dir=None):
    """
    Organize files by extension into separate folders
    """
    if target_dir is None:
        target_dir = source_dir
    
    # File type mappings
    file_types = {
        'images': ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.svg'],
        'documents': ['.pdf', '.doc', '.docx', '.txt', '.rtf'],
        'videos': ['.mp4', '.avi', '.mkv', '.mov', '.wmv'],
        'audio': ['.mp3', '.wav', '.flac', '.aac', '.ogg'],
        'archives': ['.zip', '.rar', '.7z', '.tar', '.gz'],
        'code': ['.py', '.js', '.html', '.css', '.java', '.cpp']
    }
    
    # Create directories if they don't exist
    for folder in file_types.keys():
        folder_path = Path(target_dir) / folder
        folder_path.mkdir(exist_ok=True)
    
    # Process files
    source_path = Path(source_dir)
    moved_files = 0
    
    for file_path in source_path.iterdir():
        if file_path.is_file():
            file_ext = file_path.suffix.lower()
            
            # Find appropriate folder
            target_folder = 'others'  # default
            for folder, extensions in file_types.items():
                if file_ext in extensions:
                    target_folder = folder
                    break
            
            # Create 'others' folder if needed
            if target_folder == 'others':
                others_path = Path(target_dir) / 'others'
                others_path.mkdir(exist_ok=True)
            
            # Move file
            target_path = Path(target_dir) / target_folder / file_path.name
            try:
                shutil.move(str(file_path), str(target_path))
                print(f"Moved: {file_path.name} -> {target_folder}/")
                moved_files += 1
            except Exception as e:
                print(f"Error moving {file_path.name}: {e}")
    
    print(f"\\nOrganization complete! Moved {moved_files} files.")

# Usage example
if __name__ == "__main__":
    downloads_folder = os.path.expanduser("~/Downloads")
    organize_files(downloads_folder)
\`\`\`

Script-kan wuxuu sameeyaa:
- **File categorization** - Files by extension
- **Folder creation** - Automatic directory setup  
- **Safe moving** - Error handling included
- **Progress tracking** - Shows moved files count

Run gareeyso \`python organize_files.py\` si aad u organize gareeyso Downloads folder-ka.`;
                
                addMessage('ai', aiResponse);
            }, 1000);
        }

        function testHTMLCode() {
            addMessage('user', 'Samee modern HTML form oo responsive ah');
            
            setTimeout(() => {
                const aiResponse = `Halkan waa modern HTML form oo responsive design leh:

\`\`\`html
<!DOCTYPE html>
<html lang="so">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Modern Contact Form</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="form-container">
        <div class="form-header">
            <h2><i class="fas fa-envelope"></i> La Xiriir</h2>
            <p>Fariin noo dir, waan kaa jawaabi doonaa</p>
        </div>
        
        <form class="contact-form" id="contactForm">
            <div class="form-group">
                <label for="name">
                    <i class="fas fa-user"></i> Magaca
                </label>
                <input type="text" id="name" name="name" required 
                       placeholder="Magacaaga gali">
                <span class="error-message" id="nameError"></span>
            </div>
            
            <div class="form-group">
                <label for="email">
                    <i class="fas fa-envelope"></i> Email
                </label>
                <input type="email" id="email" name="email" required 
                       placeholder="<EMAIL>">
                <span class="error-message" id="emailError"></span>
            </div>
            
            <div class="form-group">
                <label for="phone">
                    <i class="fas fa-phone"></i> Telefoon
                </label>
                <input type="tel" id="phone" name="phone" 
                       placeholder="+252 XX XXX XXXX">
            </div>
            
            <div class="form-group">
                <label for="subject">
                    <i class="fas fa-tag"></i> Mawduuca
                </label>
                <select id="subject" name="subject" required>
                    <option value="">Dooro mawduuc</option>
                    <option value="general">Su'aal guud</option>
                    <option value="support">Taageero</option>
                    <option value="business">Ganacsi</option>
                    <option value="feedback">Ra'yi</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="message">
                    <i class="fas fa-comment"></i> Fariin
                </label>
                <textarea id="message" name="message" rows="5" required 
                          placeholder="Fariintaada halkan ku qor..."></textarea>
                <span class="char-count">0/500</span>
            </div>
            
            <div class="form-group checkbox-group">
                <label class="checkbox-label">
                    <input type="checkbox" id="newsletter" name="newsletter">
                    <span class="checkmark"></span>
                    Waan rabaa in aan helo newsletter-ka
                </label>
            </div>
            
            <div class="form-actions">
                <button type="reset" class="btn-secondary">
                    <i class="fas fa-undo"></i> Reset
                </button>
                <button type="submit" class="btn-primary">
                    <i class="fas fa-paper-plane"></i> Dir Fariin
                </button>
            </div>
        </form>
        
        <div class="form-footer">
            <p><i class="fas fa-shield-alt"></i> Macluumaadkaaga waa ammaan</p>
        </div>
    </div>
</body>
</html>
\`\`\`

Form-kan wuxuu leeyahay:
- **Responsive design** - Mobile iyo desktop
- **Form validation** - Client-side checking
- **Icon integration** - FontAwesome icons
- **Accessibility** - Proper labels and ARIA
- **Modern styling** - Clean, professional look

Ku dar CSS styling si aad u hesho beautiful form!`;
                
                addMessage('ai', aiResponse);
            }, 1000);
        }

        function testCSSCode() {
            addMessage('user', 'Samee CSS animations oo modern ah');
            
            setTimeout(() => {
                const aiResponse = `Halkan waa modern CSS animations oo smooth ah:

\`\`\`css
/* Modern CSS Animations */

/* Fade in animation */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Slide in from left */
@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-100px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Bounce animation */
@keyframes bounce {
    0%, 20%, 53%, 80%, 100% {
        transform: translate3d(0, 0, 0);
    }
    40%, 43% {
        transform: translate3d(0, -30px, 0);
    }
    70% {
        transform: translate3d(0, -15px, 0);
    }
    90% {
        transform: translate3d(0, -4px, 0);
    }
}

/* Pulse animation */
@keyframes pulse {
    0% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(78, 205, 196, 0.7);
    }
    70% {
        transform: scale(1.05);
        box-shadow: 0 0 0 10px rgba(78, 205, 196, 0);
    }
    100% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(78, 205, 196, 0);
    }
}

/* Gradient animation */
@keyframes gradientShift {
    0% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
    100% {
        background-position: 0% 50%;
    }
}

/* Usage classes */
.fade-in {
    animation: fadeIn 0.6s ease-out;
}

.slide-in-left {
    animation: slideInLeft 0.8s ease-out;
}

.bounce {
    animation: bounce 1s ease-in-out;
}

.pulse {
    animation: pulse 2s infinite;
}

.gradient-bg {
    background: linear-gradient(-45deg, #667eea, #764ba2, #f093fb, #f5576c);
    background-size: 400% 400%;
    animation: gradientShift 15s ease infinite;
}

/* Hover effects */
.hover-lift {
    transition: all 0.3s ease;
}

.hover-lift:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

.hover-glow {
    transition: all 0.3s ease;
}

.hover-glow:hover {
    box-shadow: 0 0 20px rgba(78, 205, 196, 0.6);
    transform: scale(1.02);
}

/* Loading spinner */
.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(78, 205, 196, 0.3);
    border-top: 4px solid #4ecdc4;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
\`\`\`

Animations-kan waxay bixiyaan:
- **Smooth transitions** - 60fps performance
- **Modern effects** - Fade, slide, bounce, pulse
- **Hover interactions** - Lift and glow effects
- **Loading states** - Spinner animation
- **Background effects** - Gradient shifting

Isticmaal classes-ka HTML elements-ka si aad u hesho beautiful animations!`;
                
                addMessage('ai', aiResponse);
            }, 1000);
        }

        function clearMessages() {
            document.getElementById('chatSimulation').innerHTML = '';
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🤖 AI Code Response Test loaded');
            addMessage('ai', 'Ahlan! Waxaan diyaar u ahay in aan ku tuso sida **AI Code Component** u shaqeeyo. Dooro test button si aad u aragto code blocks oo professional ah.');
        });
    </script>
</body>
</html>
