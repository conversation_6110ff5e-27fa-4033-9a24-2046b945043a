#!/usr/bin/env python3
"""
OpenAI API Setup Script for Tincada AI
Helps configure OpenAI API key securely
"""

import os
import sys
from pathlib import Path

def create_env_file():
    """Create .env file with OpenAI API key"""
    print("🔑 OpenAI API Key Setup")
    print("=" * 40)
    
    # Check if .env already exists
    env_file = Path('.env')
    if env_file.exists():
        print("⚠️  .env file already exists!")
        overwrite = input("Do you want to overwrite it? (y/N): ").lower().strip()
        if overwrite != 'y':
            print("Setup cancelled.")
            return False
    
    print("\n📝 Please enter your OpenAI API key:")
    print("   (Get it from: https://platform.openai.com/api-keys)")
    print("   (It should start with 'sk-')")
    
    api_key = input("\nAPI Key: ").strip()
    
    # Basic validation
    if not api_key:
        print("❌ API key cannot be empty!")
        return False
    
    if not api_key.startswith('sk-'):
        print("⚠️  Warning: API key should start with 'sk-'")
        confirm = input("Continue anyway? (y/N): ").lower().strip()
        if confirm != 'y':
            return False
    
    # Create .env file
    try:
        with open('.env', 'w') as f:
            f.write(f"# OpenAI API Configuration\n")
            f.write(f"OPENAI_API_KEY={api_key}\n")
            f.write(f"\n# Server Configuration\n")
            f.write(f"FLASK_ENV=development\n")
            f.write(f"FLASK_DEBUG=True\n")
            f.write(f"\n# Rate Limiting\n")
            f.write(f"RATE_LIMIT_PER_HOUR=60\n")
            f.write(f"\n# Security\n")
            f.write(f"SECRET_KEY=tincada-ai-secret-key-{os.urandom(8).hex()}\n")
        
        print("✅ .env file created successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Error creating .env file: {e}")
        return False

def test_api_key():
    """Test the OpenAI API key"""
    print("\n🧪 Testing OpenAI API key...")
    
    try:
        from openai import OpenAI
        
        # Load environment variables
        from dotenv import load_dotenv
        load_dotenv()
        
        api_key = os.getenv('OPENAI_API_KEY')
        if not api_key:
            print("❌ API key not found in environment!")
            return False
        
        # Initialize client
        client = OpenAI(api_key=api_key)
        
        # Test with a simple request
        response = client.chat.completions.create(
            model="gpt-4o-mini",
            messages=[
                {"role": "user", "content": "Say hello in Somali"}
            ],
            max_tokens=50
        )
        
        print("✅ API key is working!")
        print(f"   Response: {response.choices[0].message.content}")
        print(f"   Tokens used: {response.usage.total_tokens}")
        return True
        
    except ImportError:
        print("❌ OpenAI library not installed!")
        print("   Install with: pip install openai python-dotenv")
        return False
    except Exception as e:
        print(f"❌ API test failed: {e}")
        if "401" in str(e):
            print("   This usually means the API key is invalid.")
        elif "429" in str(e):
            print("   Rate limit exceeded. Try again later.")
        elif "insufficient_quota" in str(e):
            print("   Your OpenAI account has no credits.")
        return False

def install_dependencies():
    """Install required Python packages"""
    print("\n📦 Installing dependencies...")
    
    try:
        import subprocess
        
        packages = [
            "flask==2.3.3",
            "flask-cors==4.0.0", 
            "openai==1.3.0",
            "python-dotenv==1.0.0",
            "requests==2.31.0"
        ]
        
        for package in packages:
            print(f"   Installing {package}...")
            result = subprocess.run([sys.executable, "-m", "pip", "install", package], 
                                  capture_output=True, text=True)
            if result.returncode != 0:
                print(f"   ❌ Failed to install {package}")
                print(f"   Error: {result.stderr}")
                return False
        
        print("✅ All dependencies installed!")
        return True
        
    except Exception as e:
        print(f"❌ Error installing dependencies: {e}")
        return False

def main():
    """Main setup function"""
    print("🚀 Tincada AI - OpenAI Setup")
    print("=" * 50)
    
    # Check Python version
    if sys.version_info < (3, 7):
        print("❌ Python 3.7 or higher is required!")
        return
    
    print("✅ Python version OK")
    
    # Install dependencies
    print("\n1️⃣ Installing dependencies...")
    if not install_dependencies():
        print("❌ Failed to install dependencies!")
        return
    
    # Create .env file
    print("\n2️⃣ Setting up API key...")
    if not create_env_file():
        print("❌ Failed to setup API key!")
        return
    
    # Test API key
    print("\n3️⃣ Testing API connection...")
    if test_api_key():
        print("\n🎉 Setup completed successfully!")
        print("\n📋 Next steps:")
        print("   1. Run: python flask_server.py")
        print("   2. Open: http://localhost:5001")
        print("   3. Test chat functionality")
        print("   4. Try AI tools")
        
        print("\n⚠️  Security reminders:")
        print("   - Never commit .env file to git")
        print("   - Add .env to .gitignore")
        print("   - Keep your API key secure")
        
    else:
        print("\n⚠️  API test failed, but setup is complete.")
        print("   You can still run the server in fallback mode.")
        print("   Check your API key and try again.")

if __name__ == "__main__":
    main()
