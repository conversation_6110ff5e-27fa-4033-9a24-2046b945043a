#!/usr/bin/env python3
"""
Quick HTML Code Card Test
"""

import requests
import json

def quick_test():
    print("🧪 Quick HTML Code Card Test")
    print("=" * 40)
    
    api_url = "http://localhost:5001/api/chat"
    
    # Simple HTML test
    payload = {
        "message": "write simple html div tag",
        "user_id": "quick_test_user"
    }
    
    print("❓ Testing: write simple html div tag")
    print("🚀 Sending request...")
    
    try:
        response = requests.post(
            api_url,
            json=payload,
            headers={"Content-Type": "application/json"},
            timeout=15
        )
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Response received!")
            
            # Check response format
            if 'response' in data:
                ai_response = data['response']
                print(f"📊 Response type: Custom format")
            elif 'choices' in data:
                ai_response = data['choices'][0]['message']['content']
                print(f"📊 Response type: OpenAI format")
            else:
                print("❌ Unknown response format")
                print(f"Data: {data}")
                return
            
            print(f"📏 Response length: {len(ai_response)} characters")
            
            # Check for HTML code blocks
            if "```html" in ai_response:
                print("🎯 ✅ SUCCESS: Found ```html code block!")
                
                # Extract HTML content
                import re
                html_matches = re.findall(r'```html\n?(.*?)```', ai_response, re.DOTALL)
                if html_matches:
                    print(f"💻 HTML blocks found: {len(html_matches)}")
                    for i, html_code in enumerate(html_matches, 1):
                        print(f"📝 HTML Block {i}:")
                        print(f"   {html_code.strip()[:100]}...")
                else:
                    print("⚠️ ```html found but couldn't extract content")
                    
            else:
                print("❌ FAILED: No ```html code block found!")
                print("🔍 Checking for HTML content...")
                
                # Check for HTML tags
                html_tags = ['<div>', '<html>', '<p>', '<h1>', '<span>']
                found_tags = [tag for tag in html_tags if tag in ai_response]
                
                if found_tags:
                    print(f"⚠️ Found HTML tags but not in code block: {found_tags}")
                else:
                    print("ℹ️ No HTML tags found")
            
            # Show full response
            print("\n📄 Full AI Response:")
            print("-" * 40)
            print(ai_response)
            print("-" * 40)
            
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            print(f"Response: {response.text}")
            
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    quick_test()
