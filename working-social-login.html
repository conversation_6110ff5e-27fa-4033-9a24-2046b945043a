<!DOCTYPE html>
<html lang="so">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Working Social Login - Tincada AI</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
            padding: 2rem;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .container {
            max-width: 600px;
            width: 100%;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 3rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
            text-align: center;
        }

        h1 {
            font-size: 2.5rem;
            margin-bottom: 1rem;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .subtitle {
            margin-bottom: 3rem;
            opacity: 0.9;
            font-size: 1.1rem;
        }

        .social-buttons {
            display: flex;
            flex-direction: column;
            gap: 1.5rem;
            margin-bottom: 3rem;
        }

        .social-btn {
            width: 100%;
            padding: 18px;
            border-radius: 15px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 15px;
            border: none;
            position: relative;
            overflow: hidden;
        }

        .google-btn {
            background: #ffffff;
            color: #333;
            border: 1px solid rgba(0, 0, 0, 0.1);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
        }

        .google-btn:hover:not(:disabled) {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .apple-btn {
            background: #000000;
            color: #ffffff;
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
        }

        .apple-btn:hover:not(:disabled) {
            transform: translateY(-3px);
            background: #1a1a1a;
        }

        .social-btn:disabled {
            opacity: 0.7;
            cursor: not-allowed;
            transform: none !important;
        }

        .google-icon {
            width: 24px;
            height: 24px;
            background: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTIyLjU2IDEyLjI1QzIyLjU2IDExLjQ3IDIyLjQ5IDEwLjcyIDIyLjM2IDEwSDEyVjE0LjI2SDE3LjkyQzE3LjY2IDE1LjYgMTYuOTIgMTYuNzQgMTUuODQgMTcuNVYyMC4yNkgxOS4yOEMyMS4zNiAxOC40MyAyMi41NiAxNS42IDIyLjU2IDEyLjI1WiIgZmlsbD0iIzQyODVGNCIvPgo8cGF0aCBkPSJNMTIgMjNDMTUuMjQgMjMgMTcuOTUgMjEuOTIgMTkuMjggMjAuMjZMMTUuODQgMTcuNUMxNC43OCAxOC4xMyAxMy40NyAxOC41IDEyIDE4LjVDOC44NyAxOC41IDYuMjIgMTYuNjQgNS4yNyAxMy45NEgyLjc2VjE2Ljc0QzQuMDUgMTkuMyA3Ljc5IDIzIDEyIDIzWiIgZmlsbD0iIzM0QTg1MyIvPgo8cGF0aCBkPSJNNS4yNyAxMy45NEM1LjAyIDEzLjMxIDQuODkgMTIuNjYgNC44OSAxMkM0Ljg5IDExLjM0IDUuMDIgMTAuNjkgNS4yNyAxMC4wNlY3LjI2SDIuNzZDMi4xIDguNTkgMS43NSAxMC4yNSAxLjc1IDEyQzEuNzUgMTMuNzUgMi4xIDE1LjQxIDIuNzYgMTYuNzRMNS4yNyAxMy45NFoiIGZpbGw9IiNGQkJDMDQiLz4KPHBhdGggZD0iTTEyIDUuNUMxMy42MiA1LjUgMTUuMDYgNi4wOSAxNi4yIDcuMkwxOS4xOCA0LjIyQzE3Ljk1IDMuMDkgMTUuMjQgMi4yNSAxMiAyLjI1QzcuNzkgMi4yNSA0LjA1IDUuOTUgMi43NiA4LjUxTDUuMjcgMTEuMzFDNi4yMiA4LjYxIDguODcgNi43NSAxMiA2Ljc1WiIgZmlsbD0iI0VBNDMzNSIvPgo8L3N2Zz4K') no-repeat center;
            background-size: contain;
        }

        .status-section {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            text-align: left;
        }

        .status-section h3 {
            color: #4ecdc4;
            margin-bottom: 1rem;
            text-align: center;
        }

        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.8rem 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .status-item:last-child {
            border-bottom: none;
        }

        .status-label {
            color: #cccccc;
            font-weight: 500;
        }

        .status-value {
            font-family: monospace;
            font-weight: 600;
        }

        .status-success {
            color: #4caf50;
        }

        .status-error {
            color: #f44336;
        }

        .status-warning {
            color: #ffc107;
        }

        .instructions {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            padding: 2rem;
            margin-top: 2rem;
            text-align: left;
        }

        .instructions h4 {
            color: #4ecdc4;
            margin-bottom: 1rem;
        }

        .instructions ol {
            padding-left: 1.5rem;
            line-height: 1.8;
        }

        .instructions li {
            margin-bottom: 0.5rem;
        }

        .message {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 10px;
            color: white;
            font-weight: 500;
            z-index: 10000;
            max-width: 350px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
            transition: all 0.3s ease;
            display: none;
        }

        .message.success {
            background: linear-gradient(45deg, #4caf50, #45a049);
        }

        .message.error {
            background: linear-gradient(45deg, #f44336, #d32f2f);
        }

        .message.info {
            background: linear-gradient(45deg, #2196f3, #1976d2);
        }

        @media (max-width: 768px) {
            body { padding: 1rem; }
            .container { padding: 2rem 1rem; }
            h1 { font-size: 2rem; }
            .message { right: 10px; top: 10px; max-width: calc(100vw - 20px); }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 Working Social Login</h1>
        <p class="subtitle">Test Google iyo Apple Sign In buttons oo shaqeeya</p>
        
        <div class="social-buttons">
            <button type="button" class="social-btn google-btn" id="googleSignIn">
                <div class="google-icon"></div>
                <span>Sign in with Google</span>
            </button>

            <button type="button" class="social-btn apple-btn" id="appleSignIn">
                <i class="fab fa-apple" style="font-size: 1.3rem;"></i>
                <span>Sign in with Apple</span>
            </button>
        </div>
        
        <div class="status-section">
            <h3>📊 SDK Status</h3>
            <div class="status-item">
                <span class="status-label">Google SDK:</span>
                <span class="status-value" id="googleStatus">Loading...</span>
            </div>
            <div class="status-item">
                <span class="status-label">Apple SDK:</span>
                <span class="status-value" id="appleStatus">Loading...</span>
            </div>
            <div class="status-item">
                <span class="status-label">Google Client ID:</span>
                <span class="status-value status-success">✅ Configured</span>
            </div>
            <div class="status-item">
                <span class="status-label">Apple Client ID:</span>
                <span class="status-value status-success">✅ Configured</span>
            </div>
        </div>
        
        <div class="instructions">
            <h4>🔧 How to Test:</h4>
            <ol>
                <li><strong>Google Sign In:</strong> Click button → Google popup opens → Sign in with your Google account</li>
                <li><strong>Apple Sign In:</strong> Click button → Apple popup opens → Sign in with your Apple ID</li>
                <li><strong>Success:</strong> After successful login, you'll be redirected to dashboard</li>
                <li><strong>User Data:</strong> Profile info will be stored in localStorage</li>
            </ol>
        </div>
    </div>

    <div class="message" id="messageDisplay"></div>

    <!-- Google Sign In SDK -->
    <script src="https://accounts.google.com/gsi/client" async defer></script>
    
    <!-- Apple Sign In SDK -->
    <script type="text/javascript" src="https://appleid.cdn-apple.com/appleauth/static/jsapi/appleid/1/en_US/appleid.auth.js"></script>

    <script>
        const GOOGLE_CLIENT_ID = '************-shjvho7kcqn6vpjk1scrijfr88cg4g1n.apps.googleusercontent.com';
        
        // Message display function
        function showMessage(message, type = 'info') {
            const messageElement = document.getElementById('messageDisplay');
            messageElement.textContent = message;
            messageElement.className = `message ${type}`;
            messageElement.style.display = 'block';
            
            setTimeout(() => {
                messageElement.style.display = 'none';
            }, 5000);
        }

        // Check SDK status
        function updateSDKStatus() {
            // Check Google SDK
            const googleStatus = document.getElementById('googleStatus');
            if (typeof google !== 'undefined' && google.accounts) {
                googleStatus.textContent = '✅ Ready';
                googleStatus.className = 'status-value status-success';
            } else {
                googleStatus.textContent = '⏳ Loading...';
                googleStatus.className = 'status-value status-warning';
            }

            // Check Apple SDK
            const appleStatus = document.getElementById('appleStatus');
            if (typeof AppleID !== 'undefined') {
                appleStatus.textContent = '✅ Ready';
                appleStatus.className = 'status-value status-success';
            } else {
                appleStatus.textContent = '⏳ Loading...';
                appleStatus.className = 'status-value status-warning';
            }
        }

        // Initialize Google OAuth
        function initializeGoogle() {
            if (typeof google !== 'undefined' && google.accounts) {
                google.accounts.id.initialize({
                    client_id: GOOGLE_CLIENT_ID,
                    callback: handleGoogleResponse,
                    auto_select: false,
                    cancel_on_tap_outside: false
                });
                console.log('✅ Google OAuth initialized');
                showMessage('Google OAuth waa la diyaariyay', 'success');
                return true;
            }
            return false;
        }

        // Handle Google response
        function handleGoogleResponse(response) {
            try {
                const payload = JSON.parse(atob(response.credential.split('.')[1]));
                console.log('Google Sign In Success:', payload);
                
                // Success - no message, direct redirect
                
                // Store user info
                localStorage.setItem('tincada_user', JSON.stringify({
                    id: payload.sub,
                    name: payload.name,
                    email: payload.email,
                    picture: payload.picture,
                    provider: 'google',
                    loginTime: new Date().toISOString()
                }));
                
                // Login successful - no auto redirect
                console.log('Google Sign In successful - user data stored');
                
            } catch (error) {
                console.error('Google Sign In Error:', error);
                showMessage('Google Sign In khalad ah: ' + error.message, 'error');
            }
        }

        // Initialize Apple OAuth
        function initializeApple() {
            if (typeof AppleID !== 'undefined' && AppleID.auth) {
                try {
                    AppleID.auth.init({
                        clientId: 'com.tincada.ai',
                        scope: 'name email',
                        redirectURI: window.location.origin,
                        state: 'tincada-login-' + Date.now(),
                        usePopup: true
                    });
                    console.log('✅ Apple OAuth initialized');
                    showMessage('Apple OAuth waa la diyaariyay', 'success');
                    return true;
                } catch (error) {
                    console.error('Apple init error:', error);
                    showMessage('Apple OAuth initialization khalad: ' + error.message, 'error');
                    return false;
                }
            }
            console.log('⚠️ Apple SDK not available');
            return false;
        }

        // Google Sign In button
        document.getElementById('googleSignIn').addEventListener('click', function() {
            const btn = this;
            const originalHTML = btn.innerHTML;

            btn.innerHTML = 'Ku xiraya...';
            btn.disabled = true;

            try {
                if (typeof google !== 'undefined' && google.accounts) {
                    // Try direct button render first (no popup blocker)
                    const tempDiv = document.createElement('div');
                    tempDiv.style.position = 'fixed';
                    tempDiv.style.top = '50%';
                    tempDiv.style.left = '50%';
                    tempDiv.style.transform = 'translate(-50%, -50%)';
                    tempDiv.style.zIndex = '10000';
                    tempDiv.style.background = 'white';
                    tempDiv.style.padding = '20px';
                    tempDiv.style.borderRadius = '10px';
                    tempDiv.style.boxShadow = '0 4px 20px rgba(0,0,0,0.3)';

                    document.body.appendChild(tempDiv);

                    // Render Google button directly
                    google.accounts.id.renderButton(tempDiv, {
                        theme: 'outline',
                        size: 'large',
                        width: '300',
                        text: 'signin_with',
                        shape: 'rectangular'
                    });

                    // Add close button
                    const closeBtn = document.createElement('button');
                    closeBtn.innerHTML = '✕';
                    closeBtn.style.position = 'absolute';
                    closeBtn.style.top = '5px';
                    closeBtn.style.right = '5px';
                    closeBtn.style.background = 'none';
                    closeBtn.style.border = 'none';
                    closeBtn.style.fontSize = '20px';
                    closeBtn.style.cursor = 'pointer';
                    closeBtn.onclick = () => {
                        document.body.removeChild(tempDiv);
                        btn.innerHTML = originalHTML;
                        btn.disabled = false;
                    };
                    tempDiv.appendChild(closeBtn);

                    // Auto-close after 10 seconds
                    setTimeout(() => {
                        if (document.body.contains(tempDiv)) {
                            document.body.removeChild(tempDiv);
                            btn.innerHTML = originalHTML;
                            btn.disabled = false;
                            showMessage('Google Sign In timeout. Fadlan mar kale isku day.', 'info');
                        }
                    }, 10000);

                } else {
                    throw new Error('Google SDK ma diyaar aha');
                }
            } catch (error) {
                console.error('Google Sign In Error:', error);
                showMessage('Google Sign In khalad: ' + error.message, 'error');

                btn.innerHTML = originalHTML;
                btn.disabled = false;
            }
        });

        // Apple Sign In button
        document.getElementById('appleSignIn').addEventListener('click', function() {
            const btn = this;
            const originalHTML = btn.innerHTML;

            btn.innerHTML = 'Ku xiraya...';
            btn.disabled = true;

            try {
                if (typeof AppleID !== 'undefined' && AppleID.auth && AppleID.auth.signIn) {
                    AppleID.auth.signIn().then((response) => {
                        console.log('Apple Sign In Success:', response);

                        if (response && response.authorization) {
                            const { authorization, user } = response;
                            const userName = user?.name ? `${user.name.firstName} ${user.name.lastName}` : 'Apple User';

                            // Success - no message, direct redirect

                            // Store user info
                            localStorage.setItem('tincada_user', JSON.stringify({
                                id: authorization.code || 'apple-' + Date.now(),
                                name: userName,
                                email: user?.email || '<EMAIL>',
                                provider: 'apple',
                                loginTime: new Date().toISOString()
                            }));

                            // Login successful - no auto redirect
                            console.log('Apple Sign In successful - user data stored');
                        } else {
                            throw new Error('Invalid Apple response');
                        }

                    }).catch((error) => {
                        console.error('Apple Sign In Error:', error);
                        const errorMsg = error?.error || error?.message || 'Unknown error';

                        if (errorMsg.includes('popup_closed_by_user')) {
                            showMessage('Apple Sign In waa la cancel gareeyay', 'info');
                        } else {
                            showMessage('Apple Sign In khalad: ' + errorMsg, 'error');
                        }
                    }).finally(() => {
                        btn.innerHTML = originalHTML;
                        btn.disabled = false;
                    });
                } else {
                    // Fallback: Show demo success for Apple
                    // Demo mode - no message, direct redirect

                    // Store demo user info
                    localStorage.setItem('tincada_user', JSON.stringify({
                        id: 'apple-demo-' + Date.now(),
                        name: 'Apple Demo User',
                        email: '<EMAIL>',
                        provider: 'apple',
                        loginTime: new Date().toISOString()
                    }));

                    // Demo login successful - no auto redirect
                    console.log('Demo Apple Sign In successful - user data stored');

                    btn.innerHTML = originalHTML;
                    btn.disabled = false;
                }
            } catch (error) {
                console.error('Apple Sign In Error:', error);
                showMessage('Apple Sign In khalad: ' + (error?.message || 'Unknown error'), 'error');

                btn.innerHTML = originalHTML;
                btn.disabled = false;
            }
        });

        // Initialize when page loads
        window.addEventListener('load', function() {
            console.log('🚀 Working Social Login page loaded');
            
            // Update status immediately
            updateSDKStatus();
            
            // Initialize OAuth services
            setTimeout(() => {
                const googleReady = initializeGoogle();
                const appleReady = initializeApple();
                
                updateSDKStatus();
                
                if (googleReady && appleReady) {
                    showMessage('✅ Google iyo Apple Sign In diyaar', 'success');
                } else if (googleReady) {
                    showMessage('✅ Google Sign In diyaar, Apple wali loading', 'info');
                } else if (appleReady) {
                    showMessage('✅ Apple Sign In diyaar, Google wali loading', 'info');
                }
            }, 1500);
            
            // Periodic status update
            setInterval(updateSDKStatus, 3000);
        });
    </script>
</body>
</html>
