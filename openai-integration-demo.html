<!DOCTYPE html>
<html lang="so">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OpenAI Integration Demo - Tincada AI</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
            padding: 2rem;
            margin: 0;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 3rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        h1 {
            text-align: center;
            font-size: 2.5rem;
            margin-bottom: 1rem;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .subtitle {
            text-align: center;
            font-size: 1.2rem;
            margin-bottom: 3rem;
            opacity: 0.9;
        }
        
        .integration-section {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            border-left: 4px solid #4ecdc4;
        }
        
        .integration-section h2 {
            color: #4ecdc4;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .code-block {
            background: rgba(0, 0, 0, 0.5);
            border-radius: 8px;
            padding: 1.5rem;
            margin: 1rem 0;
            border: 1px solid rgba(255, 255, 255, 0.1);
            overflow-x: auto;
        }
        
        .code-block pre {
            margin: 0;
            color: #e6e6e6;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            line-height: 1.4;
        }
        
        .code-block .comment {
            color: #888;
        }
        
        .code-block .string {
            color: #a8e6cf;
        }
        
        .code-block .keyword {
            color: #ffd93d;
        }
        
        .setup-steps {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            margin: 2rem 0;
        }
        
        .step-card {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 1.5rem;
            transition: all 0.3s ease;
        }
        
        .step-card:hover {
            transform: translateY(-5px);
            border-color: rgba(255, 255, 255, 0.2);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }
        
        .step-number {
            width: 40px;
            height: 40px;
            background: linear-gradient(45deg, #10a37f, #0d8f6f);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            margin-bottom: 1rem;
        }
        
        .step-card h3 {
            color: #ffffff;
            margin-bottom: 0.5rem;
        }
        
        .step-card p {
            color: #cccccc;
            line-height: 1.5;
            margin: 0;
        }
        
        .api-features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin: 2rem 0;
        }
        
        .feature-item {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            padding: 1rem;
            border: 1px solid rgba(255, 255, 255, 0.1);
            text-align: center;
        }
        
        .feature-icon {
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }
        
        .feature-item h4 {
            color: #ffffff;
            margin-bottom: 0.5rem;
            font-size: 0.9rem;
        }
        
        .feature-item p {
            color: #cccccc;
            font-size: 0.8rem;
            margin: 0;
        }
        
        .warning-box {
            background: rgba(255, 193, 7, 0.1);
            border: 1px solid rgba(255, 193, 7, 0.3);
            border-radius: 10px;
            padding: 1.5rem;
            margin: 2rem 0;
            border-left: 4px solid #ffc107;
        }
        
        .warning-box h3 {
            color: #ffc107;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .success {
            background: rgba(76, 175, 80, 0.2);
            border: 1px solid rgba(76, 175, 80, 0.3);
            border-radius: 10px;
            padding: 1.5rem;
            margin: 2rem 0;
            border-left: 4px solid #4caf50;
            text-align: center;
        }
        
        .demo-buttons {
            display: flex;
            gap: 20px;
            justify-content: center;
            margin-top: 3rem;
            flex-wrap: wrap;
        }
        
        .demo-btn {
            background: linear-gradient(45deg, #4ecdc4, #44a08d);
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 25px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 10px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(78, 205, 196, 0.3);
        }
        
        .demo-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(78, 205, 196, 0.4);
        }
        
        .demo-btn.secondary {
            background: linear-gradient(45deg, #ff6b6b, #ee5a52);
            box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
        }
        
        .demo-btn.secondary:hover {
            box-shadow: 0 6px 20px rgba(255, 107, 107, 0.4);
        }
        
        @media (max-width: 768px) {
            body { padding: 1rem; }
            .container { padding: 2rem 1rem; }
            h1 { font-size: 2rem; }
            .setup-steps { grid-template-columns: 1fr; }
            .demo-buttons { flex-direction: column; align-items: center; }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🤖 OpenAI API Integration</h1>
        <p class="subtitle">Secure OpenAI integration oo leh proper error handling iyo fallback responses</p>
        
        <div class="integration-section">
            <h2>🔑 API Key Setup</h2>
            <p>Secure way to configure your OpenAI API key:</p>
            
            <div class="code-block">
                <pre><code><span class="comment"># 1. Copy environment template</span>
<span class="keyword">cp</span> .env.example .env

<span class="comment"># 2. Edit .env file</span>
<span class="string">OPENAI_API_KEY</span>=<span class="string">your-actual-api-key-here</span>

<span class="comment"># 3. Install dependencies</span>
<span class="keyword">pip install</span> -r requirements.txt

<span class="comment"># 4. Run Flask server</span>
<span class="keyword">python</span> flask_server.py</code></pre>
            </div>
        </div>
        
        <div class="setup-steps">
            <div class="step-card">
                <div class="step-number">1</div>
                <h3>Get API Key</h3>
                <p>Visit platform.openai.com, create account, and generate API key</p>
            </div>
            
            <div class="step-card">
                <div class="step-number">2</div>
                <h3>Configure Environment</h3>
                <p>Add API key to .env file for secure storage</p>
            </div>
            
            <div class="step-card">
                <div class="step-number">3</div>
                <h3>Install Dependencies</h3>
                <p>Run pip install to get Flask, OpenAI, and other packages</p>
            </div>
            
            <div class="step-card">
                <div class="step-number">4</div>
                <h3>Start Server</h3>
                <p>Launch Flask server with OpenAI integration</p>
            </div>
        </div>
        
        <div class="integration-section">
            <h2>🛠️ Backend Integration</h2>
            <p>Professional Flask server oo leh OpenAI API integration:</p>
            
            <div class="code-block">
                <pre><code><span class="keyword">from</span> openai <span class="keyword">import</span> OpenAI
<span class="keyword">from</span> flask <span class="keyword">import</span> Flask, request, jsonify

<span class="comment"># Initialize OpenAI client</span>
<span class="keyword">def</span> <span class="string">get_openai_client</span>():
    api_key = os.getenv(<span class="string">'OPENAI_API_KEY'</span>)
    <span class="keyword">if not</span> api_key:
        <span class="keyword">return</span> <span class="string">None</span>
    
    <span class="keyword">try</span>:
        client = OpenAI(api_key=api_key)
        <span class="keyword">return</span> client
    <span class="keyword">except</span> Exception <span class="keyword">as</span> e:
        logger.error(<span class="string">f"Failed to initialize: {e}"</span>)
        <span class="keyword">return</span> <span class="string">None</span></code></pre>
            </div>
        </div>
        
        <div class="api-features">
            <div class="feature-item">
                <div class="feature-icon">🔒</div>
                <h4>Secure API Keys</h4>
                <p>Environment variables</p>
            </div>
            
            <div class="feature-item">
                <div class="feature-icon">⚡</div>
                <h4>Rate Limiting</h4>
                <p>60 requests/hour</p>
            </div>
            
            <div class="feature-item">
                <div class="feature-icon">🛡️</div>
                <h4>Error Handling</h4>
                <p>Graceful fallbacks</p>
            </div>
            
            <div class="feature-item">
                <div class="feature-icon">📊</div>
                <h4>Usage Tracking</h4>
                <p>Token monitoring</p>
            </div>
            
            <div class="feature-item">
                <div class="feature-icon">🔄</div>
                <h4>Fallback Mode</h4>
                <p>Works without API</p>
            </div>
            
            <div class="feature-item">
                <div class="feature-icon">🎯</div>
                <h4>Tool Integration</h4>
                <p>5 AI tools ready</p>
            </div>
        </div>
        
        <div class="warning-box">
            <h3>⚠️ Security Warning</h3>
            <p><strong>Never commit your API key to version control!</strong></p>
            <ul style="color: #cccccc; margin: 1rem 0;">
                <li>Use environment variables (.env file)</li>
                <li>Add .env to .gitignore</li>
                <li>Rotate keys regularly</li>
                <li>Monitor usage and costs</li>
            </ul>
        </div>
        
        <div class="integration-section">
            <h2>🔄 Fallback System</h2>
            <p>System wuxuu shaqeeyaa even without OpenAI API:</p>
            
            <div class="code-block">
                <pre><code><span class="keyword">def</span> <span class="string">generate_fallback_response</span>(message):
    <span class="comment"># Intelligent fallback responses</span>
    <span class="keyword">if</span> <span class="string">'hello'</span> <span class="keyword">in</span> message.lower():
        <span class="keyword">return</span> <span class="string">"Hello! I'm Tincada AI..."</span>
    
    <span class="keyword">elif</span> <span class="string">'code'</span> <span class="keyword">in</span> message.lower():
        <span class="keyword">return</span> <span class="string">"I can help with programming..."</span>
    
    <span class="keyword">else</span>:
        <span class="keyword">return</span> <span class="string">"I'm here to help! (Fallback mode)"</span></code></pre>
            </div>
        </div>
        
        <div class="success">
            <h3>🎉 OpenAI Integration Complete!</h3>
            <p><strong>Secure Backend:</strong> Flask server oo leh proper API integration</p>
            <p><strong>Error Handling:</strong> Graceful fallbacks iyo rate limiting</p>
            <p><strong>Production Ready:</strong> Environment variables iyo security best practices</p>
            <p><strong>Full Functionality:</strong> Chat, tools, iyo comprehensive features</p>
        </div>
        
        <div class="demo-buttons">
            <a href="dashboard.html" class="demo-btn">
                🤖 Test AI Integration
            </a>
            <a href="SETUP.md" class="demo-btn secondary" target="_blank">
                📖 Setup Guide
            </a>
            <a href="updated-tools-demo.html" class="demo-btn secondary">
                🛠️ AI Tools Demo
            </a>
        </div>
    </div>
</body>
</html>
