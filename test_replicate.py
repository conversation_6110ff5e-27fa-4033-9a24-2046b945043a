#!/usr/bin/env python3
"""
Test Replicate API connection and image-to-video functionality
"""

import os
import replicate
from dotenv import load_dotenv

def main():
    print("🧪 Testing Replicate API Connection")
    print("=" * 50)
    
    # Load environment variables
    load_dotenv()
    
    # Get API token
    api_token = os.getenv('REPLICATE_API_TOKEN')
    if not api_token:
        print("❌ No REPLICATE_API_TOKEN found in .env file")
        return

    print(f"🔑 API Token found: {api_token[:20]}...")

    # Test image generation first
    print("🎨 Testing image generation...")

    try:
        # Test SDXL image generation
        output = replicate.run(
            "stability-ai/sdxl:39ed52f2a78e934b3ba6e2a89f5b1c712de7dfea535525255b1aa35c5565e08b",
            input={
                "prompt": "A beautiful sunset over mountains, photorealistic, high quality",
                "width": 1024,
                "height": 1024,
                "num_outputs": 1,
                "scheduler": "K_EULER",
                "num_inference_steps": 50,
                "guidance_scale": 7.5
            }
        )

        image_url = output[0] if isinstance(output, list) else output
        print("✅ Image generation successful!")
        print(f"🖼️ Image URL: {image_url}")

    except Exception as e:
        print(f"❌ Image generation error: {e}")
        if "billing" in str(e).lower():
            print("   💳 No credits for image generation")
        elif "authentication" in str(e).lower():
            print("   🔑 Authentication failed")
    
    # Set environment variable
    os.environ['REPLICATE_API_TOKEN'] = api_token
    
    try:
        print("🔄 Testing API connection...")
        
        # Test with a simple model first
        print("📋 Listing available models...")
        
        # Try to run a simple prediction
        print("🎬 Testing image-to-video conversion...")
        
        # Use a sample image URL
        sample_image = "https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=512&h=512&fit=crop"
        
        print(f"📸 Using sample image: {sample_image}")
        
        # Test Stable Video Diffusion
        output = replicate.run(
            "stability-ai/stable-video-diffusion:3f0457e4619daac51203dedb1a4f3482d9b4c90e3e5e0a0a9b8b8b8b8b8b8b8b",
            input={
                "input_image": sample_image,
                "motion_bucket_id": 5,
                "cond_aug": 0.02,
                "decoding_t": 7,
                "video_length": 3,
                "sizing_strategy": "maintain_aspect_ratio",
                "frames_per_second": 24
            }
        )
        
        print("✅ Video generation successful!")
        print(f"🎥 Video URL: {output}")
        
        # Test different model if the first one fails
    except Exception as e:
        print(f"❌ Error: {e}")
        
        if "billing" in str(e).lower() or "credits" in str(e).lower():
            print("   💳 No credits available for video generation")
        elif "authentication" in str(e).lower():
            print("   🔑 Authentication failed - check API token")
        elif "not found" in str(e).lower():
            print("   🔍 Model not found - trying alternative...")
            
            # Try alternative model
            try:
                print("🔄 Trying alternative model...")
                output = replicate.run(
                    "anotherjesse/zeroscope-v2-xl:9f747673945c62801b13b84701c783929c0ee784e4748ec062204894dda1a351",
                    input={
                        "prompt": "A mountain landscape with moving clouds",
                        "width": 512,
                        "height": 512,
                        "num_frames": 24,
                        "num_inference_steps": 50
                    }
                )
                
                print("✅ Alternative model successful!")
                print(f"🎥 Video URL: {output}")
                
            except Exception as e2:
                print(f"❌ Alternative model also failed: {e2}")
        else:
            print(f"   ❓ Unknown error: {e}")

if __name__ == "__main__":
    main()
