<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tincada AI - Working Chat</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
            color: white;
            height: 100vh;
            overflow: hidden;
        }
        
        .chat-container {
            display: flex;
            flex-direction: column;
            height: 100vh;
            max-width: 800px;
            margin: 0 auto;
            background: #1e1e1e;
            box-shadow: 0 0 30px rgba(0,0,0,0.5);
        }
        
        .header {
            background: linear-gradient(90deg, #007bff, #0056b3);
            padding: 20px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.3);
        }
        
        .header h1 {
            font-size: 24px;
            margin-bottom: 5px;
        }
        
        .header p {
            opacity: 0.9;
            font-size: 14px;
        }
        
        .chat-messages {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            background: #1a1a1a;
        }
        
        .message {
            margin-bottom: 20px;
            padding: 15px;
            border-radius: 10px;
            max-width: 80%;
            word-wrap: break-word;
        }
        
        .user-message {
            background: #007bff;
            margin-left: auto;
            text-align: right;
        }
        
        .ai-message {
            background: #2a2a2a;
            border-left: 4px solid #007bff;
        }
        
        .input-container {
            padding: 20px;
            background: #2a2a2a;
            border-top: 1px solid #444;
        }
        
        .input-group {
            display: flex;
            gap: 10px;
        }
        
        #messageInput {
            flex: 1;
            padding: 15px;
            border: 1px solid #444;
            border-radius: 25px;
            background: #333;
            color: white;
            font-size: 16px;
            outline: none;
        }
        
        #messageInput:focus {
            border-color: #007bff;
        }
        
        #sendButton {
            padding: 15px 25px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            transition: background 0.3s;
        }
        
        #sendButton:hover {
            background: #0056b3;
        }
        
        #sendButton:disabled {
            background: #666;
            cursor: not-allowed;
        }
        
        .typing {
            display: none;
            padding: 15px;
            background: #2a2a2a;
            border-radius: 10px;
            margin-bottom: 20px;
            max-width: 80%;
        }
        
        .typing-dots {
            display: inline-block;
        }
        
        .typing-dots span {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #007bff;
            margin: 0 2px;
            animation: typing 1.4s infinite ease-in-out;
        }
        
        .typing-dots span:nth-child(1) { animation-delay: -0.32s; }
        .typing-dots span:nth-child(2) { animation-delay: -0.16s; }
        
        @keyframes typing {
            0%, 80%, 100% { transform: scale(0); }
            40% { transform: scale(1); }
        }
        
        .status {
            padding: 10px 20px;
            text-align: center;
            font-size: 14px;
            background: #333;
        }
        
        .status.success { background: #28a745; }
        .status.error { background: #dc3545; }
        .status.warning { background: #ffc107; color: black; }
    </style>
</head>
<body>
    <div class="chat-container">
        <div class="header">
            <h1>🤖 Tincada AI</h1>
            <p>AI Assistant - Dhamaan luuqaadaha calamka ku hadal</p>
        </div>
        
        <div id="status" class="status">🔄 Connecting to AI...</div>
        
        <div class="chat-messages" id="chatMessages">
            <div class="message ai-message">
                <strong>Tincada AI:</strong><br>
                Salaan! Waxaan ahay Tincada AI. Waxaan ku jawaabi karaa dhamaan su'aalaha aad i weyddiiso. Su'aal kasta oo aad qabto, i weydii!
                <br><br>
                <em>Hello! I'm Tincada AI. I can answer all your questions in any language. Ask me anything!</em>
            </div>
        </div>
        
        <div class="typing" id="typingIndicator">
            <strong>Tincada AI:</strong> 
            <div class="typing-dots">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </div>
        
        <div class="input-container">
            <div class="input-group">
                <input type="text" id="messageInput" placeholder="Geli su'aashaada halkan... / Type your question here..." autocomplete="off">
                <button id="sendButton">📤 Send</button>
            </div>
        </div>
    </div>

    <script>
        class TincadaAI {
            constructor() {
                this.apiKey = '********************************************************************************************************************************************************************';
                this.apiUrl = 'https://api.openai.com/v1/chat/completions';
                this.chatHistory = [];
                this.init();
            }
            
            init() {
                this.bindEvents();
                this.updateStatus('✅ AI Ready - Diyaar u yahay!', 'success');
            }
            
            bindEvents() {
                const sendButton = document.getElementById('sendButton');
                const messageInput = document.getElementById('messageInput');
                
                sendButton.addEventListener('click', () => this.sendMessage());
                messageInput.addEventListener('keypress', (e) => {
                    if (e.key === 'Enter') {
                        this.sendMessage();
                    }
                });
            }
            
            updateStatus(message, type = '') {
                const status = document.getElementById('status');
                status.textContent = message;
                status.className = `status ${type}`;
            }
            
            async sendMessage() {
                const messageInput = document.getElementById('messageInput');
                const message = messageInput.value.trim();
                
                if (!message) return;
                
                // Add user message to chat
                this.addMessage(message, 'user');
                messageInput.value = '';
                
                // Show typing indicator
                this.showTyping(true);
                this.updateStatus('🤖 AI jawaabaya...', 'warning');
                
                try {
                    const response = await this.callOpenAI(message);
                    this.showTyping(false);
                    this.addMessage(response, 'ai');
                    this.updateStatus('✅ Jawaab la helay!', 'success');
                } catch (error) {
                    this.showTyping(false);
                    this.addMessage(`Waan ka xumahay, khalad ayaa dhacay: ${error.message}`, 'ai');
                    this.updateStatus('❌ Khalad dhacay', 'error');
                }
            }
            
            async callOpenAI(message) {
                const systemMessage = {
                    role: 'system',
                    content: `You are Tincada AI, a helpful assistant. You can communicate in all world languages. 
                    When user writes in Somali, respond in Somali. When they write in English, respond in English. 
                    When they write in any other language, respond in that language. 
                    Be helpful, accurate, and professional. You can help with coding, questions, explanations, and any topic.`
                };
                
                this.chatHistory.push({ role: 'user', content: message });
                
                const messages = [systemMessage, ...this.chatHistory.slice(-10)]; // Keep last 10 messages
                
                const response = await fetch(this.apiUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${this.apiKey}`
                    },
                    body: JSON.stringify({
                        model: 'gpt-4o-mini',
                        messages: messages,
                        max_tokens: 1000,
                        temperature: 0.7,
                        stream: false
                    })
                });
                
                if (!response.ok) {
                    const errorData = await response.json().catch(() => ({}));
                    throw new Error(`API Error: ${response.status} - ${errorData.error?.message || response.statusText}`);
                }
                
                const data = await response.json();
                
                if (!data.choices || !data.choices[0] || !data.choices[0].message) {
                    throw new Error('Invalid response format from API');
                }
                
                const aiResponse = data.choices[0].message.content;
                this.chatHistory.push({ role: 'assistant', content: aiResponse });
                
                return aiResponse;
            }
            
            addMessage(content, sender) {
                const chatMessages = document.getElementById('chatMessages');
                const messageDiv = document.createElement('div');
                messageDiv.className = `message ${sender}-message`;
                
                if (sender === 'user') {
                    messageDiv.innerHTML = `<strong>Adiga:</strong><br>${this.escapeHtml(content)}`;
                } else {
                    messageDiv.innerHTML = `<strong>Tincada AI:</strong><br>${this.formatMessage(content)}`;
                }
                
                chatMessages.appendChild(messageDiv);
                chatMessages.scrollTop = chatMessages.scrollHeight;
            }
            
            formatMessage(text) {
                // Basic formatting for AI responses
                let formatted = this.escapeHtml(text);
                
                // Format code blocks
                formatted = formatted.replace(/```([\s\S]*?)```/g, '<pre style="background: #333; padding: 10px; border-radius: 5px; margin: 10px 0; overflow-x: auto;"><code>$1</code></pre>');
                
                // Format inline code
                formatted = formatted.replace(/`([^`]+)`/g, '<code style="background: #333; padding: 2px 5px; border-radius: 3px;">$1</code>');
                
                // Format bold text
                formatted = formatted.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
                
                // Format line breaks
                formatted = formatted.replace(/\n/g, '<br>');
                
                return formatted;
            }
            
            escapeHtml(text) {
                const div = document.createElement('div');
                div.textContent = text;
                return div.innerHTML;
            }
            
            showTyping(show) {
                const typingIndicator = document.getElementById('typingIndicator');
                typingIndicator.style.display = show ? 'block' : 'none';
                
                if (show) {
                    const chatMessages = document.getElementById('chatMessages');
                    chatMessages.scrollTop = chatMessages.scrollHeight;
                }
            }
        }
        
        // Initialize the AI chat
        window.addEventListener('load', () => {
            new TincadaAI();
        });
    </script>
</body>
</html>
