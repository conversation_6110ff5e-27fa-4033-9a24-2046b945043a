#!/usr/bin/env python3
"""
🧪 Test New OpenAI API Key
Testing the new API key you provided
"""

from openai import OpenAI
import requests

def test_new_openai_key():
    """
    Test the new OpenAI API key
    """
    print("🧪 Testing New OpenAI API Key")
    print("=" * 50)
    
    api_key = "********************************************************************************************************************************************************************"
    
    try:
        client = OpenAI(api_key=api_key)
        
        # Test 1: Basic API access
        print("📡 Testing basic API access...")
        try:
            models = client.models.list()
            print("✅ Basic API access: SUCCESS")
        except Exception as e:
            print(f"❌ Basic API access: FAILED - {e}")
            return False
        
        # Test 2: Chat completion
        print("💬 Testing chat completion...")
        try:
            response = client.chat.completions.create(
                model="gpt-4o-mini",
                messages=[
                    {"role": "system", "content": "You are a helpful assistant that responds in Somali."},
                    {"role": "user", "content": "Maxaad tahay?"}
                ],
                max_tokens=100
            )
            
            ai_response = response.choices[0].message.content
            print("✅ Chat completion: SUCCESS")
            print(f"🤖 AI Response: {ai_response[:100]}...")
            
        except Exception as e:
            print(f"❌ Chat completion: FAILED - {e}")
            return False
        
        # Test 3: Image generation
        print("🎨 Testing image generation...")
        try:
            response = client.images.generate(
                model="dall-e-3",
                prompt="A simple test image of a red apple",
                n=1,
                size="1024x1024",
                response_format="url"
            )
            
            image_url = response.data[0].url
            print("✅ Image generation: SUCCESS")
            print(f"🖼️ Image URL: {image_url}")
            return True
            
        except Exception as e:
            print(f"❌ Image generation: FAILED - {e}")
            print("Note: This might be due to billing or permissions")
            return False
            
    except Exception as e:
        print(f"❌ OpenAI client error: {e}")
        return False

def test_flask_with_new_key():
    """
    Test Flask server with new API key
    """
    print("\n🌐 Testing Flask Server with New API Key")
    print("=" * 50)
    
    try:
        # Test chat endpoint
        print("💬 Testing chat endpoint...")
        
        test_data = {
            "message": "Maxaad tahay? Ku jawaab Somali",
            "user_id": "test_user"
        }
        
        response = requests.post(
            "http://localhost:5001/api/chat",
            json=test_data,
            timeout=30
        )
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Flask chat: SUCCESS")
            print(f"🤖 Response: {data['response'][:100]}...")
            return True
        else:
            print(f"❌ Flask chat: HTTP {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Flask test error: {e}")
        return False

def main():
    """
    Run all tests with new API key
    """
    print("🔑 NEW OPENAI API KEY TEST")
    print("=" * 60)
    print("Testing API key: ********************************************************************************************************************************************************************")
    print("=" * 60)
    
    # Test direct API
    api_works = test_new_openai_key()
    
    # Test Flask server
    flask_works = test_flask_with_new_key()
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 FINAL RESULTS:")
    print("-" * 30)
    print(f"🔑 Direct OpenAI API: {'✅ WORKING' if api_works else '❌ NOT WORKING'}")
    print(f"🌐 Flask Server: {'✅ WORKING' if flask_works else '❌ NOT WORKING'}")
    
    if api_works and flask_works:
        print("\n🎉 SUCCESS! New API key is working!")
        print("✨ AI can now respond to all questions using the API")
        print("🌐 Server: http://localhost:5001")
    elif api_works:
        print("\n⚠️ API works but Flask server needs restart")
        print("🔄 Restart server: python app.py")
    else:
        print("\n❌ API key has issues")
        print("🔧 Check billing and permissions")

if __name__ == "__main__":
    main()
