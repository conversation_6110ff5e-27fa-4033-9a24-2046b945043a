#!/usr/bin/env python3
"""
Test script to verify API connection and functionality
"""

import requests
import json
import time

def test_server_health():
    """Test if server is running"""
    try:
        response = requests.get('http://localhost:5001/api/health', timeout=5)
        if response.status_code == 200:
            data = response.json()
            print("✅ Server health check passed")
            print(f"   Status: {data['status']}")
            print(f"   Mock mode: {data['mock_mode']}")
            return True
        else:
            print(f"❌ Server health check failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Server health check error: {e}")
        return False

def test_chat_api():
    """Test chat API with a simple message"""
    try:
        test_message = "Salam alaykum! Sidee tahay?"
        
        data = {
            "message": test_message,
            "user_id": "test_user",
            "chat_history": []
        }
        
        print(f"📤 Sending test message: {test_message}")
        
        response = requests.post(
            'http://localhost:5001/api/chat',
            json=data,
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Chat API test passed")
            print(f"   Response: {result['response'][:100]}...")
            print(f"   Source: {result['source']}")
            print(f"   Tokens used: {result['tokens_used']}")
            return True
        else:
            print(f"❌ Chat API test failed: {response.status_code}")
            print(f"   Error: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Chat API test error: {e}")
        return False

def test_code_generation():
    """Test code generation capability"""
    try:
        test_message = "Fadlan ii samee JavaScript function oo calculate gareeysa area of circle"
        
        data = {
            "message": test_message,
            "user_id": "test_user",
            "chat_history": []
        }
        
        print(f"📤 Testing code generation: {test_message}")
        
        response = requests.post(
            'http://localhost:5001/api/chat',
            json=data,
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Code generation test passed")
            print(f"   Response length: {len(result['response'])} characters")
            print(f"   Source: {result['source']}")
            if 'function' in result['response'].lower():
                print("   ✅ Response contains code function")
            return True
        else:
            print(f"❌ Code generation test failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Code generation test error: {e}")
        return False

if __name__ == "__main__":
    print("🧪 Starting Tincada AI API Tests...")
    print("=" * 50)
    
    # Test 1: Server Health
    print("\n1️⃣ Testing server health...")
    health_ok = test_server_health()
    
    if not health_ok:
        print("❌ Server is not running. Please start the server first.")
        exit(1)
    
    # Test 2: Basic Chat
    print("\n2️⃣ Testing basic chat functionality...")
    chat_ok = test_chat_api()
    
    # Test 3: Code Generation
    print("\n3️⃣ Testing code generation...")
    code_ok = test_code_generation()
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 Test Results Summary:")
    print(f"   Server Health: {'✅ PASS' if health_ok else '❌ FAIL'}")
    print(f"   Basic Chat: {'✅ PASS' if chat_ok else '❌ FAIL'}")
    print(f"   Code Generation: {'✅ PASS' if code_ok else '❌ FAIL'}")
    
    if all([health_ok, chat_ok, code_ok]):
        print("\n🎉 All tests passed! Tincada AI is working properly.")
    else:
        print("\n⚠️ Some tests failed. Please check the server configuration.")
