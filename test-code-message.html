<!DOCTYPE html>
<html lang="so">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Code Message - Tincada AI</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 2rem;
            background: #f0f0f0;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .test-btn {
            background: #4ecdc4;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px;
        }
        .test-btn:hover {
            background: #44a08d;
        }
        #result {
            margin-top: 2rem;
            padding: 1rem;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: #f9f9f9;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 Test AI Code Response</h1>
        <p>Click buttons to test different code responses:</p>
        
        <button class="test-btn" onclick="testJavaScript()">Test JavaScript</button>
        <button class="test-btn" onclick="testPython()">Test Python</button>
        <button class="test-btn" onclick="testHTML()">Test HTML</button>
        <button class="test-btn" onclick="testCSS()">Test CSS</button>
        
        <div id="result"></div>
    </div>

    <script src="ai-code-component.js"></script>
    <script>
        function testJavaScript() {
            const code = `// Modern JavaScript function
const processUserData = async (userData) => {
    try {
        const { name, email, age } = userData;
        
        if (!name || !email) {
            throw new Error('Name and email are required');
        }
        
        const userProfile = {
            id: generateUserId(),
            name: name.trim(),
            email: email.toLowerCase(),
            age: parseInt(age) || 0,
            createdAt: new Date().toISOString()
        };
        
        await saveUserProfile(userProfile);
        return userProfile;
        
    } catch (error) {
        console.error('Error:', error);
        throw error;
    }
};`;
            
            const result = aiCodeBlock.createCodeBlock(code, 'javascript');
            document.getElementById('result').innerHTML = `
                <h3>JavaScript Code Block:</h3>
                ${result}
            `;
        }

        function testPython() {
            const code = `# Python data analysis function
import pandas as pd
import numpy as np

def analyze_data(data_file):
    """Analyze data from CSV file"""
    try:
        # Load data
        df = pd.read_csv(data_file)
        
        # Basic statistics
        stats = {
            'rows': len(df),
            'columns': len(df.columns),
            'missing_values': df.isnull().sum().sum(),
            'numeric_columns': df.select_dtypes(include=[np.number]).columns.tolist()
        }
        
        # Generate summary
        summary = df.describe()
        
        return {
            'statistics': stats,
            'summary': summary,
            'data_types': df.dtypes.to_dict()
        }
        
    except Exception as e:
        print(f"Error analyzing data: {e}")
        return None

# Usage
result = analyze_data('data.csv')
print(result)`;
            
            const result = aiCodeBlock.createCodeBlock(code, 'python');
            document.getElementById('result').innerHTML = `
                <h3>Python Code Block:</h3>
                ${result}
            `;
        }

        function testHTML() {
            const code = `<!DOCTYPE html>
<html lang="so">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Modern Form</title>
</head>
<body>
    <div class="form-container">
        <h2>Contact Form</h2>
        
        <form class="contact-form">
            <div class="form-group">
                <label for="name">Name</label>
                <input type="text" id="name" name="name" required>
            </div>
            
            <div class="form-group">
                <label for="email">Email</label>
                <input type="email" id="email" name="email" required>
            </div>
            
            <div class="form-group">
                <label for="message">Message</label>
                <textarea id="message" name="message" rows="5"></textarea>
            </div>
            
            <button type="submit" class="submit-btn">
                Send Message
            </button>
        </form>
    </div>
</body>
</html>`;
            
            const result = aiCodeBlock.createCodeBlock(code, 'html');
            document.getElementById('result').innerHTML = `
                <h3>HTML Code Block:</h3>
                ${result}
            `;
        }

        function testCSS() {
            const code = `/* Modern CSS with animations */
:root {
    --primary-color: #4ecdc4;
    --secondary-color: #44a08d;
    --text-color: #2c3e50;
    --background: #ffffff;
    --shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.form-container {
    max-width: 600px;
    margin: 2rem auto;
    padding: 2rem;
    background: var(--background);
    border-radius: 12px;
    box-shadow: var(--shadow);
    transition: all 0.3s ease;
}

.form-container:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: var(--text-color);
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(78, 205, 196, 0.1);
}

.submit-btn {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    padding: 0.75rem 2rem;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.submit-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(78, 205, 196, 0.4);
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.form-container {
    animation: fadeIn 0.6s ease-out;
}`;
            
            const result = aiCodeBlock.createCodeBlock(code, 'css');
            document.getElementById('result').innerHTML = `
                <h3>CSS Code Block:</h3>
                ${result}
            `;
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🧪 Test page loaded');
            document.getElementById('result').innerHTML = `
                <p><em>Click a button above to test AI code block rendering...</em></p>
            `;
        });
    </script>
</body>
</html>
