# 🤖 Tincada AI - Complete PHP/MySQL System

## Overview
Tincada AI is a comprehensive AI chat system with full PHP backend, MySQL database integration, and professional dashboard. The system supports multilingual conversations, code generation, user management, and real-time analytics.

## Features
- ✅ **Full PHP Backend** - Complete server-side functionality
- ✅ **MySQL Database** - User management, chat history, payments
- ✅ **Professional Dashboard** - Real-time analytics and management
- ✅ **Multilingual Support** - Somali, English, Arabic, French, Spanish, German
- ✅ **OpenAI Integration** - Real AI responses with API
- ✅ **Code Generation** - HTML, CSS, JavaScript with syntax highlighting
- ✅ **Professional Code Cards** - Interactive code blocks with copy/edit functionality
- ✅ **User Management** - Registration, authentication, profiles
- ✅ **Payment System** - Subscription management and billing
- ✅ **RESTful API** - Complete API endpoints
- ✅ **Image Generation** - AI-powered image creation
- ✅ **File Upload Support** - Image and document handling
- ✅ **Responsive Design** - Works on all devices

## System Requirements

### Server Requirements
- **PHP 7.4+** (PHP 8.0+ recommended)
- **MySQL 5.7+** or **MariaDB 10.3+**
- **Apache/Nginx** with mod_rewrite
- **cURL extension** for API calls
- **PDO MySQL extension**
- **JSON extension**

### Recommended Setup
- **XAMPP** (Windows/Mac/Linux)
- **WAMP** (Windows)
- **MAMP** (Mac)
- **LAMP** (Linux)

## Quick Start

### Option 1: XAMPP Setup (Recommended)
```bash
# 1. Download and install XAMPP
# 2. Start Apache and MySQL services
# 3. Copy project to htdocs folder
cp -r "tincada-ai" "C:\xampp\htdocs\"

# 4. Access via browser
http://localhost/tincada-ai
```

### Option 2: Manual PHP Server
```bash
# Navigate to project directory
cd "c:\Users\<USER>\OneDrive\Desktop\tincada ai"

# Start PHP built-in server (requires PHP installation)
php -S localhost:8000

# Open browser
http://localhost:8000
```

## Database Setup

### 1. Create Database
```sql
-- Import the provided SQL file
mysql -u root -p < tincada_database.sql
```

### 2. Configure Database Connection
Edit `database_config.php`:
```php
define('DB_HOST', 'localhost');
define('DB_USERNAME', 'root');
define('DB_PASSWORD', 'your_password');
define('DB_NAME', 'tincada_ai_db');
```

### 3. Test Database Connection
Visit: `http://localhost/tincada-ai/api/health`

## File Structure
```
tincada-ai/
├── index.html              # Main chat interface
├── styles.css              # Frontend styling
├── script.js               # Frontend functionality
├── dashboard.php           # PHP dashboard with database
├── database_config.php     # Database connection & classes
├── api_endpoints.php       # RESTful API endpoints
├── simple_api.php          # Fallback API (no database)
├── tincada_database.sql    # Complete database schema
├── .htaccess              # URL routing configuration
└── README.md              # This documentation
```

## Core Components

### Frontend Files
- **index.html** - Main chat interface with code cards
- **styles.css** - Professional styling and animations
- **script.js** - AI chat, code formatting, file handling

### Backend Files
- **dashboard.php** - Complete dashboard with real-time data
- **database_config.php** - Database classes and utilities
- **api_endpoints.php** - Full API with database integration
- **simple_api.php** - Lightweight API fallback

### Database
- **tincada_database.sql** - Complete schema with 8 tables
- **Users, Messages, Payments, Images** - Core data tables
- **Views and Procedures** - Analytics and reporting
- **Sample Data** - Test data for development

## API Endpoints

### Chat API
```
POST /api/chat
{
    "message": "Hello, how are you?",
    "language": "auto",
    "user_id": "user_123"
}
```

### User Management
```
GET /api/users              # Get all users
GET /api/users/{id}         # Get specific user
POST /api/users             # Create new user
PUT /api/users/{id}         # Update user
```

### Statistics
```
GET /api/stats              # System statistics
GET /api/messages           # Recent messages
GET /api/payments           # Payment history
```

### Health Check
```
GET /api/health             # System status
```

## Dashboard Features

### Overview Section
- Total users, messages, revenue
- Daily activity charts
- Language usage statistics
- Real-time metrics

### User Management
- User list with pagination
- Account type management
- Activity tracking
- Subscription status

### Message Analytics
- Message history
- Token usage tracking
- Language distribution
- Response time metrics

### Payment Management
- Transaction history
- Revenue analytics
- Subscription tracking
- Payment status monitoring

### AI Chat Interface
- Direct chat with AI
- Real-time responses
- Message history
- Multi-language support

## Language Support

The system supports automatic detection and responses in:
- **Somali (so)** - Native language support
- **English (en)** - International standard
- **Arabic (ar)** - RTL support included
- **French (fr)** - European market
- **Spanish (es)** - Latin American market
- **German (de)** - European market

## OpenAI Integration

### API Configuration
The system uses OpenAI GPT-4o-mini for intelligent responses:
```php
$openai_api_key = 'your-openai-api-key';
$model = 'gpt-4o-mini';
```

### Fallback System
If OpenAI API fails, the system provides:
- Intelligent fallback responses
- Code generation examples
- Multi-language support
- Context-aware replies

## Code Generation

Request code in any language:
- "Generate HTML form" → Professional HTML with styling
- "Create CSS animation" → Modern CSS with transitions
- "Write JavaScript function" → Clean, commented JS code
- "Samee HTML code" (Somali) → Localized code generation

Features:
- Syntax highlighting with highlight.js
- Copy to clipboard functionality
- Edit/preview modals
- Professional formatting

## Security Features

- **SQL Injection Protection** - PDO prepared statements
- **XSS Prevention** - Input sanitization
- **CSRF Protection** - Token validation
- **Rate Limiting** - API request limits
- **Input Validation** - Server-side validation
- **Secure Headers** - Security headers in .htaccess

## Performance Optimization

- **Database Indexing** - Optimized queries
- **Caching** - Response caching
- **Compression** - Gzip compression
- **CDN Ready** - Static asset optimization
- **Lazy Loading** - Efficient resource loading

## Deployment

### Production Setup
1. **Upload files** to web server
2. **Import database** using provided SQL
3. **Configure database** connection
4. **Set permissions** for upload directories
5. **Enable SSL** for security
6. **Configure backups** for database

### Environment Configuration
```php
// Production settings
define('ENVIRONMENT', 'production');
define('DEBUG_MODE', false);
define('LOG_ERRORS', true);
```

## Troubleshooting

### Database Connection Issues
```bash
# Check MySQL service
sudo systemctl status mysql

# Test connection
mysql -u root -p -e "SHOW DATABASES;"
```

### PHP Issues
```bash
# Check PHP version
php --version

# Check required extensions
php -m | grep -E "(pdo|curl|json)"
```

### Apache/Nginx Issues
```bash
# Check Apache status
sudo systemctl status apache2

# Check error logs
tail -f /var/log/apache2/error.log
```

### API Not Working
1. Check `.htaccess` file exists
2. Verify mod_rewrite is enabled
3. Test direct PHP file access
4. Check error logs

## Development

### Local Development Setup
```bash
# Clone/download project
git clone [repository-url]

# Install XAMPP/WAMP
# Start Apache + MySQL
# Import database
# Configure database connection
# Access via localhost
```

### Adding New Features
1. **Database Changes** - Update schema in SQL file
2. **API Endpoints** - Add routes in `api_endpoints.php`
3. **Frontend** - Update HTML/CSS/JS files
4. **Dashboard** - Extend `dashboard.php`

## Monitoring

### System Health
- Database connection status
- API response times
- Error rates
- User activity

### Analytics
- Daily active users
- Message volume
- Token usage
- Revenue tracking

## Support

For technical support:
1. Check error logs in browser console
2. Verify database connection
3. Test API endpoints directly
4. Check server error logs
5. Validate configuration files

---

**Tincada AI** - Complete AI chat system with professional dashboard and full database integration! 🚀
