/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* Code Card Styles */
.code-card {
    background: #1e1e1e;
    border-radius: 12px;
    margin: 15px 0;
    overflow: hidden;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
    border: 1px solid #333;
    font-family: 'Fira Code', 'Monaco', 'Consolas', monospace;
}

.code-header {
    background: linear-gradient(135deg, #2d2d2d 0%, #1a1a1a 100%);
    padding: 12px 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #333;
}

.code-language {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #61dafb;
    font-weight: 600;
    font-size: 14px;
    text-transform: uppercase;
}

.code-language i {
    font-size: 16px;
}

.code-actions {
    display: flex;
    gap: 8px;
}

.code-btn {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: #fff;
    padding: 6px 12px;
    border-radius: 6px;
    font-size: 12px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 4px;
    transition: all 0.3s ease;
}

.code-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-1px);
}

.copy-btn:hover {
    background: #28a745;
}

.edit-btn:hover {
    background: #007bff;
}

.code-content {
    background: #1e1e1e;
    overflow-x: auto;
}

.code-content pre {
    margin: 0;
    padding: 20px;
    background: transparent;
    color: #f8f8f2;
    font-size: 14px;
    line-height: 1.6;
    overflow-x: auto;
}

.code-content code {
    font-family: 'Fira Code', 'Monaco', 'Consolas', monospace;
    background: transparent;
    color: inherit;
}

/* Syntax highlighting colors */
.language-html .hljs-tag { color: #f92672; }
.language-html .hljs-name { color: #f92672; }
.language-html .hljs-attr { color: #a6e22e; }
.language-html .hljs-string { color: #e6db74; }

.language-css .hljs-selector-tag { color: #f92672; }
.language-css .hljs-selector-class { color: #a6e22e; }
.language-css .hljs-property { color: #66d9ef; }
.language-css .hljs-value { color: #e6db74; }

.language-javascript .hljs-keyword { color: #f92672; }
.language-javascript .hljs-function { color: #a6e22e; }
.language-javascript .hljs-string { color: #e6db74; }
.language-javascript .hljs-number { color: #ae81ff; }

.language-python .hljs-keyword { color: #f92672; }
.language-python .hljs-function { color: #a6e22e; }
.language-python .hljs-string { color: #e6db74; }
.language-python .hljs-number { color: #ae81ff; }

/* Inline code */
.inline-code {
    background: rgba(255, 255, 255, 0.1);
    color: #e6db74;
    padding: 2px 6px;
    border-radius: 4px;
    font-family: 'Fira Code', 'Monaco', 'Consolas', monospace;
    font-size: 0.9em;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Code edit modal */
.code-edit-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10000;
}

.modal-content {
    background: #1e1e1e;
    border-radius: 12px;
    width: 90%;
    max-width: 800px;
    max-height: 80%;
    overflow: hidden;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
}

.modal-header {
    background: linear-gradient(135deg, #2d2d2d 0%, #1a1a1a 100%);
    padding: 16px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #333;
}

.modal-header h3 {
    color: #fff;
    font-size: 18px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.close-btn {
    background: none;
    border: none;
    color: #fff;
    font-size: 18px;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: background 0.3s ease;
}

.close-btn:hover {
    background: rgba(255, 255, 255, 0.1);
}

.modal-body {
    padding: 20px;
}

.code-editor {
    width: 100%;
    height: 300px;
    background: #2d2d2d;
    color: #f8f8f2;
    border: 1px solid #444;
    border-radius: 8px;
    padding: 16px;
    font-family: 'Fira Code', 'Monaco', 'Consolas', monospace;
    font-size: 14px;
    line-height: 1.6;
    resize: vertical;
    outline: none;
}

.code-editor:focus {
    border-color: #61dafb;
    box-shadow: 0 0 0 2px rgba(97, 218, 251, 0.2);
}

.modal-footer {
    padding: 16px 20px;
    background: #2d2d2d;
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    border-top: 1px solid #333;
}

.btn-primary, .btn-secondary {
    padding: 8px 16px;
    border-radius: 6px;
    border: none;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.3s ease;
}

.btn-primary {
    background: #007bff;
    color: white;
}

.btn-primary:hover {
    background: #0056b3;
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #545b62;
}

body {
    font-family: 'Poppins', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
    overflow-x: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header Styles */
.header {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    padding: 1rem 0;
    position: sticky;
    top: 0;
    z-index: 1000;
}

.header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    display: flex;
    align-items: center;
    gap: 10px;
    color: white;
}

.logo i {
    font-size: 2rem;
    background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.logo h1 {
    font-size: 1.8rem;
    font-weight: 700;
    background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.nav {
    display: flex;
    gap: 15px;
    align-items: center;
}

.user-profile {
    display: flex;
    align-items: center;
    gap: 10px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 25px;
    padding: 8px 15px;
    margin-right: 10px;
}

.user-avatar {
    width: 35px;
    height: 35px;
    border-radius: 50%;
    background: linear-gradient(45deg, #4ecdc4, #44a08d);
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    border: 2px solid rgba(255, 255, 255, 0.3);
}

.user-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 50%;
}

.user-avatar i {
    color: white;
    font-size: 1rem;
}

.user-name {
    color: white;
    font-weight: 500;
    font-size: 0.9rem;
    max-width: 120px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.nav-btn {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    padding: 10px 20px;
    border-radius: 25px;
    cursor: pointer;
    font-family: inherit;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
}

.nav-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.logout-btn {
    background: linear-gradient(45deg, #ff6b6b, #ee5a52);
    border: none;
    color: white;
    padding: 12px 25px;
    border-radius: 25px;
    cursor: pointer;
    font-family: inherit;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
}

.logout-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 107, 107, 0.4);
}

/* Main Content */
.main {
    padding: 2rem 0;
    min-height: calc(100vh - 80px);
}

/* Welcome Section */
.welcome-section {
    text-align: center;
    padding: 3rem 0;
}

.welcome-content {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 3rem;
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.ai-avatar {
    width: 100px;
    height: 100px;
    background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 2rem;
    /* animation removed */
}

.ai-avatar i {
    font-size: 3rem;
    color: white;
}

/* pulse animation removed */

.welcome-content h2 {
    font-size: 2.5rem;
    color: white;
    margin-bottom: 1rem;
    font-weight: 700;
}

.welcome-content p {
    font-size: 1.2rem;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 3rem;
    line-height: 1.6;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.feature-card {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    padding: 2rem;
    text-align: center;
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
}

.feature-card:hover {
    transform: translateY(-5px);
    background: rgba(255, 255, 255, 0.15);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.feature-card i {
    font-size: 2.5rem;
    background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 1rem;
}

.feature-card h3 {
    color: white;
    font-size: 1.3rem;
    margin-bottom: 0.5rem;
    font-weight: 600;
}

.feature-card p {
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.9rem;
    line-height: 1.5;
}

/* Chat Container */
.chat-container {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    max-width: 900px;
    margin: 0 auto;
}

.chat-header {
    background: rgba(255, 255, 255, 0.1);
    padding: 1.5rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.chat-header h3 {
    color: white;
    font-size: 1.3rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 10px;
}

.chat-controls {
    display: flex;
    align-items: center;
    gap: 15px;
}

.message-count {
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.9rem;
}

.clear-btn {
    background: rgba(255, 107, 107, 0.2);
    border: 1px solid rgba(255, 107, 107, 0.3);
    color: #ff6b6b;
    padding: 8px 15px;
    border-radius: 20px;
    cursor: pointer;
    font-family: inherit;
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    gap: 5px;
    transition: all 0.3s ease;
}

.clear-btn:hover {
    background: rgba(255, 107, 107, 0.3);
    transform: translateY(-1px);
}

.chat-messages {
    height: 500px;
    overflow-y: auto;
    padding: 1.5rem;
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

/* Message Styles */
.message {
    display: flex;
    gap: 10px;
    /* animation removed */
}

/* slideIn animation removed */

.message.user {
    flex-direction: row-reverse;
}

.message-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.message.user .message-avatar {
    background: linear-gradient(45deg, #4ecdc4, #44a08d);
}

.message.ai .message-avatar {
    background: linear-gradient(45deg, #ff6b6b, #ee5a52);
}

.message-avatar i {
    color: white;
    font-size: 1.2rem;
}

.message-content {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    padding: 1rem 1.5rem;
    max-width: 70%;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.message.user .message-content {
    background: linear-gradient(45deg, #4ecdc4, #44a08d);
    color: white;
}

.message.ai .message-content {
    background: rgba(255, 255, 255, 0.95);
    color: #333;
}

.message-text {
    line-height: 1.6;
    word-wrap: break-word;
}

.message-time {
    font-size: 0.8rem;
    opacity: 0.7;
    margin-top: 0.5rem;
}

/* Input Container */
.chat-input-container {
    border-top: 1px solid rgba(255, 255, 255, 0.2);
    padding: 1.5rem;
}

.file-preview {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    padding: 1rem;
    margin-bottom: 1rem;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.input-wrapper {
    display: flex;
    align-items: flex-end;
    gap: 10px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 25px;
    padding: 10px;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.input-actions {
    display: flex;
    gap: 5px;
}

.action-btn {
    background: none;
    border: none;
    color: rgba(255, 255, 255, 0.8);
    padding: 10px;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.action-btn:hover {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    transform: scale(1.1);
}

#messageInput {
    flex: 1;
    background: none;
    border: none;
    color: white;
    font-family: inherit;
    font-size: 1rem;
    resize: none;
    outline: none;
    min-height: 20px;
    max-height: 120px;
    padding: 10px;
}

#messageInput::placeholder {
    color: rgba(255, 255, 255, 0.6);
}

.send-btn {
    background: linear-gradient(45deg, #4ecdc4, #44a08d);
    border: none;
    color: white;
    width: 45px;
    height: 45px;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(78, 205, 196, 0.3);
}

.send-btn:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 20px rgba(78, 205, 196, 0.4);
}

.send-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
}

/* History Sidebar */
.history-sidebar {
    position: fixed;
    top: 0;
    right: -400px;
    width: 400px;
    height: 100vh;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-left: 1px solid rgba(255, 255, 255, 0.2);
    transition: right 0.3s ease;
    z-index: 1001;
}

.history-sidebar.open {
    right: 0;
}

.history-header {
    padding: 1.5rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.history-header h3 {
    color: white;
    font-size: 1.2rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 10px;
}

.close-btn {
    background: none;
    border: none;
    color: rgba(255, 255, 255, 0.8);
    font-size: 1.2rem;
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.close-btn:hover {
    background: rgba(255, 255, 255, 0.1);
    color: white;
}

.history-content {
    padding: 1rem;
    height: calc(100vh - 80px);
    overflow-y: auto;
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2000;
}

.loading-spinner {
    text-align: center;
    color: white;
}

.loading-spinner i {
    font-size: 3rem;
    margin-bottom: 1rem;
    color: #4ecdc4;
}

.loading-spinner p {
    font-size: 1.1rem;
    font-weight: 500;
}

/* Toast Notifications */
.toast-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 3000;
}

.toast {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 10px;
    padding: 1rem 1.5rem;
    margin-bottom: 10px;
    color: white;
    animation: slideInRight 0.3s ease;
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(100%);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.toast.success {
    border-left: 4px solid #4ecdc4;
}

.toast.error {
    border-left: 4px solid #ff6b6b;
}

.toast.warning {
    border-left: 4px solid #ffa726;
}

/* Responsive Design */
@media (max-width: 768px) {
    .header .container {
        flex-direction: column;
        gap: 1rem;
    }

    .nav {
        flex-wrap: wrap;
        justify-content: center;
    }

    .user-profile {
        order: -1;
        margin-right: 0;
        margin-bottom: 10px;
    }

    .user-name {
        max-width: 100px;
    }

    .welcome-content {
        padding: 2rem 1rem;
    }

    .welcome-content h2 {
        font-size: 2rem;
    }

    .features-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .chat-container {
        margin: 1rem;
        border-radius: 15px;
    }

    .message-content {
        max-width: 85%;
    }

    .history-sidebar {
        width: 100%;
        right: -100%;
    }

    .container {
        padding: 0 15px;
    }
}

/* File Preview Styles */
.file-preview-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    padding: 0.8rem;
    margin-bottom: 0.5rem;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.file-info {
    display: flex;
    align-items: center;
    gap: 10px;
    color: white;
}

.file-info i {
    color: #4ecdc4;
    font-size: 1.2rem;
}

.file-info small {
    color: rgba(255, 255, 255, 0.7);
    margin-left: 5px;
}

.remove-file-btn {
    background: rgba(255, 107, 107, 0.2);
    border: 1px solid rgba(255, 107, 107, 0.3);
    color: #ff6b6b;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.remove-file-btn:hover {
    background: rgba(255, 107, 107, 0.3);
    transform: scale(1.1);
}

.message-files {
    margin-top: 0.5rem;
    padding-top: 0.5rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.file-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 0.3rem 0;
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.9rem;
}

.file-item i {
    color: #4ecdc4;
}

/* History Styles */
.history-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.history-date h4 {
    color: white;
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.history-message {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    padding: 1rem;
    border: 1px solid rgba(255, 255, 255, 0.2);
    cursor: pointer;
    transition: all 0.3s ease;
}

.history-message:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateX(5px);
}

.history-message.user {
    border-left: 3px solid #4ecdc4;
}

.history-message.ai {
    border-left: 3px solid #ff6b6b;
}

.history-text {
    color: white;
    font-size: 0.9rem;
    line-height: 1.4;
    margin-bottom: 0.5rem;
}

.history-time {
    color: rgba(255, 255, 255, 0.6);
    font-size: 0.8rem;
}

/* Voice Recording Animation - removed */

/* recordingPulse animation removed */

/* Typing Indicator */
.typing-indicator {
    display: flex;
    align-items: center;
    gap: 5px;
    padding: 1rem;
    color: rgba(255, 255, 255, 0.7);
}

.typing-dots {
    display: flex;
    gap: 3px;
}

.typing-dot {
    width: 8px;
    height: 8px;
    background: #4ecdc4;
    border-radius: 50%;
    /* animation removed */
}

.typing-dot:nth-child(1) { animation-delay: -0.32s; }
.typing-dot:nth-child(2) { animation-delay: -0.16s; }

/* typingBounce animation removed */

/* Enhanced Button Hover Effects */
.nav-btn:active, .logout-btn:active, .send-btn:active {
    transform: translateY(0) scale(0.95);
}

/* Scrollbar Styling */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
}

::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.5);
}
