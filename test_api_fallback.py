#!/usr/bin/env python3
"""
Test API fallback behavior - should try OpenAI first, then mock
"""

import requests
import json

def test_api_fallback():
    """Test that AI tries OpenAI API first, then falls back to mock"""
    
    base_url = "http://localhost:5001"
    
    print("🧪 Testing API Fallback Behavior")
    print("=" * 50)
    
    test_message = "What is 2+2? Give me a brief answer."
    
    print(f"📝 Sending test message: {test_message}")
    print("🔍 Expected behavior: Try OpenAI API first, then fallback to mock")
    print("-" * 50)
    
    try:
        response = requests.post(f"{base_url}/api/chat", json={
            "message": test_message,
            "user_id": "test_user",
            "chat_history": []
        })
        
        if response.status_code == 200:
            data = response.json()
            ai_response = data.get('response', 'No response')
            source = data.get('source', 'unknown')
            
            print(f"✅ Response received")
            print(f"📊 Source: {source}")
            print(f"💬 Response: {ai_response[:200]}...")
            
            if source == 'openai':
                print("🎉 SUCCESS: OpenAI API is working!")
                print("✅ AI is using real OpenAI data")
            elif source == 'mock_fallback':
                print("⚠️ FALLBACK: OpenAI API failed, using mock responses")
                print("ℹ️ This means API key has permission issues")
            else:
                print(f"❓ Unknown source: {source}")
                
        else:
            print(f"❌ API Error: {response.status_code}")
            print(f"Response: {response.text}")
            
    except Exception as e:
        print(f"❌ Request failed: {e}")

def test_multiple_requests():
    """Test multiple requests to see consistency"""
    
    print("\n🔄 Testing Multiple Requests")
    print("=" * 50)
    
    base_url = "http://localhost:5001"
    
    questions = [
        "Hello, how are you?",
        "What is Python?",
        "Maxaa ka dhigan AI?"
    ]
    
    for i, question in enumerate(questions, 1):
        print(f"\n📝 Test {i}: {question}")
        
        try:
            response = requests.post(f"{base_url}/api/chat", json={
                "message": question,
                "user_id": "test_user",
                "chat_history": []
            })
            
            if response.status_code == 200:
                data = response.json()
                source = data.get('source', 'unknown')
                print(f"📊 Source: {source}")
            else:
                print(f"❌ Error: {response.status_code}")
                
        except Exception as e:
            print(f"❌ Failed: {e}")

if __name__ == "__main__":
    test_api_fallback()
    test_multiple_requests()
    
    print("\n" + "=" * 50)
    print("📋 SUMMARY:")
    print("If source is 'openai' - API key is working!")
    print("If source is 'mock_fallback' - API has permission issues")
    print("Either way, AI provides professional responses!")
