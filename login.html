<!DOCTYPE html>
<html lang="so">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tincada AI - Login</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Poppins', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #333;
            overflow-x: hidden;
        }

        .login-container {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 3rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 450px;
            /* animation removed */
        }

        /* slideIn animation removed */

        .logo-section {
            text-align: center;
            margin-bottom: 2rem;
        }

        .logo-icon {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            margin: 0 auto 1rem;
            /* animation removed */
            overflow: hidden;
            border: 3px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }

        .logo-icon img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 50%;
        }

        /* pulse animation removed */

        .logo-title {
            font-size: 2rem;
            font-weight: 700;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 0.5rem;
        }

        .logo-subtitle {
            color: rgba(255, 255, 255, 0.8);
            font-size: 1rem;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-label {
            display: block;
            color: white;
            font-weight: 500;
            margin-bottom: 0.5rem;
            font-size: 0.9rem;
        }

        .form-input {
            width: 100%;
            padding: 12px 15px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            color: white;
            font-family: inherit;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .form-input:focus {
            outline: none;
            border-color: #4ecdc4;
            background: rgba(255, 255, 255, 0.15);
            box-shadow: 0 0 0 3px rgba(78, 205, 196, 0.2);
        }

        .form-input::placeholder {
            color: rgba(255, 255, 255, 0.6);
        }

        .input-with-icon {
            position: relative;
        }

        .input-icon {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: rgba(255, 255, 255, 0.6);
            font-size: 1.1rem;
        }

        .input-with-icon .form-input {
            padding-left: 45px;
        }

        .password-toggle {
            position: absolute;
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: rgba(255, 255, 255, 0.6);
            cursor: pointer;
            font-size: 1.1rem;
            transition: color 0.3s ease;
        }

        .password-toggle:hover {
            color: white;
        }

        .profile-upload {
            text-align: center;
            margin-bottom: 1.5rem;
        }

        .profile-preview {
            width: 100px;
            height: 100px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.1);
            border: 3px dashed rgba(255, 255, 255, 0.3);
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            overflow: hidden;
        }

        .profile-preview:hover {
            border-color: #4ecdc4;
            background: rgba(78, 205, 196, 0.1);
        }

        .profile-preview img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 50%;
        }

        .profile-preview i {
            font-size: 2rem;
            color: rgba(255, 255, 255, 0.6);
        }

        .upload-text {
            color: rgba(255, 255, 255, 0.8);
            font-size: 0.9rem;
        }

        .file-input {
            display: none;
        }

        .login-btn {
            width: 100%;
            background: linear-gradient(45deg, #4ecdc4, #44a08d);
            border: none;
            color: white;
            padding: 15px;
            border-radius: 10px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(78, 205, 196, 0.3);
            margin-bottom: 1rem;
        }

        .login-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(78, 205, 196, 0.4);
        }

        .login-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .divider {
            display: flex;
            align-items: center;
            margin: 1.5rem 0;
            color: rgba(255, 255, 255, 0.6);
            font-size: 0.9rem;
        }

        .divider::before,
        .divider::after {
            content: '';
            flex: 1;
            height: 1px;
            background: rgba(255, 255, 255, 0.2);
        }

        .divider span {
            padding: 0 1rem;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 20px;
            padding: 5px 15px;
        }

        .google-btn {
            width: 100%;
            background: #ffffff;
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: #333;
            padding: 15px;
            border-radius: 10px;
            font-size: 1rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 12px;
            position: relative;
            overflow: hidden;
        }

        .google-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(66, 133, 244, 0.1), transparent);
            transition: left 0.6s ease;
        }

        .google-btn:hover::before {
            left: 100%;
        }

        .google-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
            border-color: #4285f4;
        }

        .google-icon {
            width: 20px;
            height: 20px;
            background: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTIyLjU2IDEyLjI1QzIyLjU2IDExLjQ3IDIyLjQ5IDEwLjcyIDIyLjM2IDEwSDEyVjE0LjI2SDE3LjkyQzE3LjY2IDE1LjYgMTYuOTIgMTYuNzQgMTUuODQgMTcuNVYyMC4yNkgxOS4yOEMyMS4zNiAxOC40MyAyMi41NiAxNS42IDIyLjU2IDEyLjI1WiIgZmlsbD0iIzQyODVGNCIvPgo8cGF0aCBkPSJNMTIgMjNDMTUuMjQgMjMgMTcuOTUgMjEuOTIgMTkuMjggMjAuMjZMMTUuODQgMTcuNUMxNC43OCAxOC4xMyAxMy40NyAxOC41IDEyIDE4LjVDOC44NyAxOC41IDYuMjIgMTYuNjQgNS4yNyAxMy45NEgyLjc2VjE2Ljc0QzQuMDUgMTkuMyA3Ljc5IDIzIDEyIDIzWiIgZmlsbD0iIzM0QTg1MyIvPgo8cGF0aCBkPSJNNS4yNyAxMy45NEM1LjAyIDEzLjMxIDQuODkgMTIuNjYgNC44OSAxMkM0Ljg5IDExLjM0IDUuMDIgMTAuNjkgNS4yNyAxMC4wNlY3LjI2SDIuNzZDMi4xIDguNTkgMS43NSAxMC4yNSAxLjc1IDEyQzEuNzUgMTMuNzUgMi4xIDE1LjQxIDIuNzYgMTYuNzRMNS4yNyAxMy45NFoiIGZpbGw9IiNGQkJDMDQiLz4KPHBhdGggZD0iTTEyIDUuNUMxMy42MiA1LjUgMTUuMDYgNi4wOSAxNi4yIDcuMkwxOS4xOCA0LjIyQzE3Ljk1IDMuMDkgMTUuMjQgMi4yNSAxMiAyLjI1QzcuNzkgMi4yNSA0LjA1IDUuOTUgMi43NiA4LjUxTDUuMjcgMTEuMzFDNi4yMiA4LjYxIDguODcgNi43NSAxMiA2Ljc1WiIgZmlsbD0iI0VBNDMzNSIvPgo8L3N2Zz4K') no-repeat center;
            background-size: contain;
        }

        .apple-btn {
            width: 100%;
            background: #000000;
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: #ffffff;
            padding: 15px;
            border-radius: 10px;
            font-size: 1rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 12px;
            position: relative;
            overflow: hidden;
        }

        .apple-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            transition: left 0.6s ease;
        }

        .apple-btn:hover::before {
            left: 100%;
        }

        .apple-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.4);
            background: #1a1a1a;
        }

        .social-buttons {
            margin-bottom: 1.5rem;
        }

        .form-footer {
            text-align: center;
            color: rgba(255, 255, 255, 0.8);
            font-size: 0.9rem;
        }

        .form-footer a {
            color: #4ecdc4;
            text-decoration: none;
            font-weight: 500;
        }

        .form-footer a:hover {
            text-decoration: underline;
        }

        .error-message {
            background: rgba(255, 107, 107, 0.2);
            border: 1px solid rgba(255, 107, 107, 0.3);
            color: #ff6b6b;
            padding: 10px;
            border-radius: 8px;
            margin-bottom: 1rem;
            font-size: 0.9rem;
            display: none;
        }

        .success-message {
            background: rgba(76, 175, 80, 0.2);
            border: 1px solid rgba(76, 175, 80, 0.3);
            color: #4caf50;
            padding: 10px;
            border-radius: 8px;
            margin-bottom: 1rem;
            font-size: 0.9rem;
            display: none;
        }

        /* animations removed */

        .google-btn:active,
        .apple-btn:active {
            transform: translateY(0) scale(0.98);
        }

        /* spinner animation removed */

        /* Floating background elements */
        body::before {
            content: '';
            position: fixed;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 1px, transparent 1px);
            background-size: 50px 50px;
            /* animation removed */
            pointer-events: none;
            z-index: -1;
        }

        /* float animation removed */

        /* Dashboard link hover effect */
        .dashboard-link:hover {
            background: rgba(78, 205, 196, 0.1) !important;
            transform: translateY(-2px) !important;
            box-shadow: 0 4px 15px rgba(78, 205, 196, 0.3) !important;
        }

        /* Enhanced button ripple effect */
        .google-btn,
        .apple-btn,
        .login-btn {
            position: relative;
            overflow: hidden;
        }

        .google-btn::after,
        .apple-btn::after,
        .login-btn::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.3);
            transform: translate(-50%, -50%);
            transition: width 0.6s, height 0.6s;
        }

        .google-btn:active::after,
        .apple-btn:active::after,
        .login-btn:active::after {
            width: 300px;
            height: 300px;
        }

        @media (max-width: 480px) {
            .login-container {
                margin: 1rem;
                padding: 2rem 1.5rem;
            }

            .logo-title {
                font-size: 1.8rem;
            }

            .google-btn,
            .apple-btn {
                font-size: 0.9rem;
                padding: 12px;
            }

            .divider {
                margin: 1rem 0;
            }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="logo-section">
            <div class="logo-icon">
                <img src="images/icon.jpg" alt="Tincada AI">
            </div>
            <h1 class="logo-title">Tincada AI</h1>
            <p class="logo-subtitle">Soo gal akoonkaaga</p>
        </div>

        <form id="loginForm">
            <div class="error-message" id="errorMessage"></div>
            <div class="success-message" id="successMessage"></div>

            <div class="profile-upload">
                <div class="profile-preview" id="profilePreview" onclick="document.getElementById('profileImage').click()">
                    <i class="fas fa-camera"></i>
                </div>
                <p class="upload-text">Riix si aad u doorato sawirkaaga</p>
                <input type="file" id="profileImage" class="file-input" accept="image/*">
            </div>

            <div class="form-group">
                <label class="form-label" for="fullName">Magaca Buuxa</label>
                <div class="input-with-icon">
                    <i class="fas fa-user input-icon"></i>
                    <input type="text" id="fullName" class="form-input" placeholder="Geli magacaaga buuxa" required>
                </div>
            </div>

            <div class="form-group">
                <label class="form-label" for="phoneNumber">Lambarka Telefoonka</label>
                <div class="input-with-icon">
                    <i class="fas fa-phone input-icon"></i>
                    <input type="tel" id="phoneNumber" class="form-input" placeholder="+252 61 234 5678" required>
                </div>
            </div>

            <div class="form-group">
                <label class="form-label" for="password">Furaha Sirta ah</label>
                <div class="input-with-icon">
                    <i class="fas fa-lock input-icon"></i>
                    <input type="password" id="password" class="form-input" placeholder="Geli furaha sirta ah" required>
                    <i class="fas fa-eye password-toggle" id="passwordToggle"></i>
                </div>
            </div>

            <button type="submit" class="login-btn" id="loginBtn">
                <i class="fas fa-sign-in-alt"></i>
                Soo Gal
            </button>

            <div class="divider">
                <span>ama</span>
            </div>

            <div class="social-buttons">
                <button type="button" class="google-btn" id="googleSignIn">
                    <div class="google-icon"></div>
                    <span>Ku soo gal Google</span>
                </button>

                <button type="button" class="apple-btn" id="appleSignIn">
                    <i class="fab fa-apple" style="font-size: 1.2rem;"></i>
                    <span>Ku soo gal Apple</span>
                </button>
            </div>
        </form>

        <div class="form-footer">
            <p>Akoon ma lihid? <a href="#" id="registerLink">Samee akoon cusub</a></p>
            <div class="manual-navigation" style="margin-top: 1.5rem; text-align: center; padding-top: 1rem; border-top: 1px solid rgba(255,255,255,0.1);">
                <a href="dashboard.html" class="dashboard-link" style="color: #4ecdc4; text-decoration: none; font-weight: 600; font-size: 1.1rem; display: inline-flex; align-items: center; gap: 8px; padding: 10px 20px; border: 1px solid #4ecdc4; border-radius: 25px; transition: all 0.3s ease;">
                    🏠 Aad Dashboard-ka
                </a>
            </div>
        </div>
    </div>

    <!-- Google Sign In SDK -->
    <script src="https://accounts.google.com/gsi/client" async defer></script>

    <!-- Apple Sign In SDK -->
    <script type="text/javascript" src="https://appleid.cdn-apple.com/appleauth/static/jsapi/appleid/1/en_US/appleid.auth.js"></script>

    <!-- OAuth Configuration -->
    <script src="oauth-config.js"></script>

    <script src="auth.js"></script>
    <script src="login.js"></script>
    <script>
        // Google OAuth Configuration
        const GOOGLE_CLIENT_ID = '************-shjvho7kcqn6vpjk1scrijfr88cg4g1n.apps.googleusercontent.com';

        // Initialize Google Sign In
        function initializeGoogleSignIn() {
            if (typeof google !== 'undefined' && google.accounts) {
                google.accounts.id.initialize({
                    client_id: GOOGLE_CLIENT_ID,
                    callback: handleGoogleSignIn,
                    auto_select: false,
                    cancel_on_tap_outside: false
                });

                console.log('✅ Google Sign In initialized');
            } else {
                console.log('⚠️ Google Sign In SDK not loaded yet');
                setTimeout(initializeGoogleSignIn, 1000);
            }
        }

        // Handle Google Sign In response
        function handleGoogleSignIn(response) {
            console.log('Google Sign In Response:', response);

            try {
                // Decode JWT token
                const payload = JSON.parse(atob(response.credential.split('.')[1]));
                console.log('User Info:', payload);

                // Store user info
                localStorage.setItem('tincada_user', JSON.stringify({
                    id: payload.sub,
                    name: payload.name,
                    email: payload.email,
                    picture: payload.picture,
                    provider: 'google',
                    loginTime: new Date().toISOString()
                }));

                // Show success message
                showMessage(`Ku soo dhaweyn ${payload.name}! Ku guulaysatay Google Sign In`, 'success');

                // Redirect to dashboard after 2 seconds
                setTimeout(() => {
                    window.location.href = 'dashboard.html';
                }, 2000);

                console.log('Google Sign In successful - redirecting to dashboard');

            } catch (error) {
                console.error('Error processing Google Sign In:', error);
                showMessage('Google Sign In khalad ah. Fadlan mar kale isku day.', 'error');
            }
        }

        // Google Sign In button click handler
        document.getElementById('googleSignIn').addEventListener('click', function() {
            const btn = this;

            // Disable button without loading animation
            btn.innerHTML = 'Ku xiraya Google...';
            btn.disabled = true;

            try {
                if (typeof google !== 'undefined' && google.accounts) {
                    // Trigger Google Sign In
                    google.accounts.id.prompt((notification) => {
                        console.log('Google prompt notification:', notification);

                        // Reset button after delay regardless of outcome
                        setTimeout(() => {
                            btn.innerHTML = '<div class="google-icon"></div><span>Ku soo gal Google</span>';
                            btn.disabled = false;
                        }, 3000);

                        if (notification.isNotDisplayed() || notification.isSkippedMoment()) {
                            // Show message about popup blocker
                            showMessage('Popup waa la joojiyay. Fadlan popup-ka ogolow.', 'info');
                        }
                    });
                } else {
                    throw new Error('Google Sign In not initialized');
                }
            } catch (error) {
                console.error('Google Sign In Error:', error);
                showMessage('Google Sign In service ma jiro. Fadlan mar kale isku day.', 'error');

                // Reset button
                btn.innerHTML = '<div class="google-icon"></div><span>Ku soo gal Google</span>';
                btn.disabled = false;
            }
        });

        // Apple Sign In Configuration
        function initializeAppleSignIn() {
            if (typeof AppleID !== 'undefined') {
                AppleID.auth.init({
                    clientId: 'com.tincada.ai', // Replace with your Apple Service ID
                    scope: 'name email',
                    redirectURI: window.location.origin + '/auth/apple/callback',
                    state: 'tincada-login-' + Date.now(),
                    usePopup: true
                });

                console.log('✅ Apple Sign In initialized');
            } else {
                console.log('⚠️ Apple Sign In SDK not loaded yet');
                setTimeout(initializeAppleSignIn, 1000);
            }
        }

        // Apple Sign In button click handler
        document.getElementById('appleSignIn').addEventListener('click', function() {
            const btn = this;

            // Disable button without loading animation
            btn.innerHTML = 'Ku xiraya Apple...';
            btn.disabled = true;

            try {
                if (typeof AppleID !== 'undefined') {
                    AppleID.auth.signIn().then((response) => {
                        console.log('Apple Sign In Response:', response);

                        // Process Apple Sign In response
                        const { authorization, user } = response;

                        // Show success message
                        const userName = user?.name ? `${user.name.firstName} ${user.name.lastName}` : 'Apple User';
                        showMessage(`Ku soo dhaweyn ${userName}! Ku guulaysatay Apple Sign In`, 'success');

                        // Store user info
                        localStorage.setItem('tincada_user', JSON.stringify({
                            id: authorization.code,
                            name: userName,
                            email: user?.email || '<EMAIL>',
                            provider: 'apple',
                            loginTime: new Date().toISOString()
                        }));

                        // Redirect to dashboard after 2 seconds
                        setTimeout(() => {
                            window.location.href = 'dashboard.html';
                        }, 2000);

                        console.log('Apple Sign In successful - redirecting to dashboard');

                    }).catch((error) => {
                        console.error('Apple Sign In Error:', error);
                        showMessage('Apple Sign In khalad ah. Fadlan mar kale isku day.', 'error');

                        // Reset button
                        btn.innerHTML = '<i class="fab fa-apple"></i><span>Ku soo gal Apple</span>';
                        btn.disabled = false;
                    });
                } else {
                    throw new Error('Apple Sign In not initialized');
                }
            } catch (error) {
                console.error('Apple Sign In Error:', error);
                showMessage('Apple Sign In service ma jiro. Fadlan mar kale isku day.', 'error');

                // Reset button
                btn.innerHTML = '<i class="fab fa-apple"></i><span>Ku soo gal Apple</span>';
                btn.disabled = false;
            }
        });

        function showMessage(message, type) {
            const messageElement = type === 'success' ?
                document.getElementById('successMessage') :
                document.getElementById('errorMessage');

            messageElement.textContent = message;
            messageElement.style.display = 'block';

            // Hide after 3 seconds
            setTimeout(() => {
                messageElement.style.display = 'none';
            }, 3000);
        }

        // Initialize social login when page loads
        window.addEventListener('load', function() {
            console.log('🚀 Initializing social login services...');

            // Initialize Google Sign In
            setTimeout(initializeGoogleSignIn, 500);

            // Initialize Apple Sign In
            setTimeout(initializeAppleSignIn, 1000);
        });

        // Add hover effects for social buttons
        document.querySelectorAll('.google-btn, .apple-btn').forEach(button => {
            button.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-2px) scale(1.02)';
            });

            button.addEventListener('mouseleave', function() {
                if (!this.disabled) {
                    this.style.transform = 'translateY(0) scale(1)';
                }
            });
        });

        // Check if user is already logged in
        function checkExistingLogin() {
            const existingUser = localStorage.getItem('tincada_user');
            if (existingUser) {
                try {
                    const user = JSON.parse(existingUser);
                    console.log('Existing user found:', user);

                    // User already logged in - no auto redirect
                    console.log('User already logged in - staying on login page');

                } catch (error) {
                    console.error('Error parsing existing user:', error);
                    localStorage.removeItem('tincada_user');
                }
            }
        }

        // Check for existing login on page load
        setTimeout(checkExistingLogin, 1000);
    </script>
</body>
</html>
