#!/usr/bin/env python3
"""
Test Real Image Generation with New API Key
Testa sawir dhabta ah oo API key cusub ku sameeya
"""

from openai import OpenAI
import base64
import os
from datetime import datetime

# New API Key
API_KEY = "********************************************************************************************************************************************************************"

# Initialize OpenAI client
client = OpenAI(api_key=API_KEY)

def test_api_permissions():
    """Test API permissions step by step"""
    
    print("🔍 Testing API Permissions...")
    print("=" * 50)
    
    try:
        # Test 1: Basic API access
        print("1️⃣ Testing basic API access...")
        models = client.models.list()
        print("✅ Basic API access: SUCCESS")
        
        # Test 2: Check available models
        print("\n2️⃣ Checking available models...")
        model_names = [model.id for model in models.data if 'dall-e' in model.id.lower()]
        if model_names:
            print(f"✅ DALL-E models available: {model_names}")
        else:
            print("⚠️ No DALL-E models found in available models")
        
        # Test 3: Try image generation with minimal request
        print("\n3️⃣ Testing image generation permission...")
        try:
            response = client.images.generate(
                model="dall-e-3",
                prompt="A simple red circle",
                n=1,
                size="1024x1024",
                response_format="url"  # URL is simpler than b64_json
            )
            print("✅ Image generation permission: SUCCESS")
            print(f"✅ Generated image URL: {response.data[0].url}")
            return True
            
        except Exception as img_error:
            print(f"❌ Image generation permission: {img_error}")
            
            # Check specific error types
            error_str = str(img_error)
            if "insufficient permissions" in error_str.lower():
                print("💡 Solution: Check API key permissions in OpenAI dashboard")
            elif "billing" in error_str.lower():
                print("💡 Solution: Add billing information to your OpenAI account")
            elif "quota" in error_str.lower():
                print("💡 Solution: Check your usage limits and billing")
            
            return False
            
    except Exception as e:
        print(f"❌ Basic API access failed: {e}")
        return False

def generate_real_image():
    """Generate the actual requested image"""
    
    print("\n🎨 Generating Real Image...")
    print("=" * 50)
    
    # Your original prompt
    prompt = "A gray tabby cat hugging an otter with an orange scarf"
    
    try:
        print(f"🎨 Prompt: {prompt}")
        print("⏳ Generating image... (this may take 10-30 seconds)")
        
        # Generate image
        response = client.images.generate(
            model="dall-e-3",
            prompt=prompt,
            n=1,
            size="1024x1024",
            quality="standard",  # or "hd" for higher quality
            response_format="b64_json"
        )
        
        # Get base64 image data
        image_b64 = response.data[0].b64_json
        
        # Save image
        filename = "real_cat_and_otter.png"
        with open(filename, "wb") as f:
            f.write(base64.b64decode(image_b64))
        
        print(f"✅ SUCCESS! Real image saved: {filename}")
        print(f"📁 File size: {os.path.getsize(filename):,} bytes")
        print(f"📏 Size: 1024x1024 pixels")
        
        # Also save with timestamp
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_filename = f"cat_otter_{timestamp}.png"
        with open(backup_filename, "wb") as f:
            f.write(base64.b64decode(image_b64))
        
        print(f"📋 Backup saved: {backup_filename}")
        
        return filename
        
    except Exception as e:
        print(f"❌ Real image generation failed: {e}")
        return None

def generate_additional_images():
    """Generate additional test images"""
    
    print("\n🎨 Generating Additional Images...")
    print("=" * 50)
    
    prompts = [
        ("A beautiful sunset over mountains with a lake", "sunset_real.png"),
        ("A futuristic city with flying cars", "futuristic_city_real.png"),
        ("A peaceful garden with colorful flowers", "garden_real.png")
    ]
    
    successful = 0
    
    for prompt, filename in prompts:
        try:
            print(f"\n🎨 Generating: {prompt[:50]}...")
            
            response = client.images.generate(
                model="dall-e-3",
                prompt=prompt,
                n=1,
                size="1024x1024",
                response_format="b64_json"
            )
            
            image_b64 = response.data[0].b64_json
            
            with open(filename, "wb") as f:
                f.write(base64.b64decode(image_b64))
            
            print(f"✅ Saved: {filename}")
            successful += 1
            
        except Exception as e:
            print(f"❌ Failed: {e}")
    
    print(f"\n📊 Generated {successful}/{len(prompts)} additional images")
    return successful

def main():
    """Main test function"""
    
    print("🧪 Real Image Generation Test")
    print("Testing API Key for actual DALL-E image generation")
    print("=" * 70)
    
    # Step 1: Test permissions
    if not test_api_permissions():
        print("\n❌ API permissions test failed.")
        print("\n🔧 Possible solutions:")
        print("1. Check OpenAI dashboard: https://platform.openai.com/api-keys")
        print("2. Ensure API key has image generation permissions")
        print("3. Check billing and usage limits")
        print("4. Verify organization/project access")
        return
    
    # Step 2: Generate main image
    print("\n" + "=" * 70)
    main_image = generate_real_image()
    
    if main_image:
        print(f"\n🎉 SUCCESS! Your requested image is ready!")
        print(f"📁 Main image: {main_image}")
        
        # Step 3: Generate additional images
        additional_count = generate_additional_images()
        
        print("\n" + "=" * 70)
        print("📊 FINAL RESULTS:")
        print(f"✅ Main image (cat & otter): {main_image}")
        print(f"✅ Additional images: {additional_count}")
        print("\n🎉 All images are real DALL-E generated images!")
        print("📁 Check the PNG files in this folder.")
        
    else:
        print("\n❌ Failed to generate real images.")
        print("The system will use mock images instead.")

if __name__ == "__main__":
    main()
