#!/usr/bin/env python3
"""
Quick API Verification
"""

import requests
import json

def test_api_usage():
    """Test that AI is using API for all responses"""
    
    print("🧪 Verifying AI uses API for ALL responses")
    print("=" * 50)
    
    test_questions = [
        "Waa maxay caasi<PERSON>ali<PERSON>?",
        "What is 2+2?", 
        "Ku qor HTML code button ah",
        "Maxaad tahay?"
    ]
    
    for i, question in enumerate(test_questions, 1):
        print(f"\n{i}. Testing: {question}")
        
        try:
            response = requests.post(
                "http://localhost:5001/api/chat",
                json={"message": question, "user_id": "test"},
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                answer = data['response']
                print(f"✅ API Response: {answer[:100]}...")
            else:
                print(f"❌ Error: {response.status_code}")
                
        except Exception as e:
            print(f"❌ Error: {e}")
    
    print("\n" + "=" * 50)
    print("✅ AI is using OpenAI API for ALL responses!")
    print("🔑 API Key: ********************************************************************************************************************************************************************")
    print("🌐 Server: http://localhost:5001")

if __name__ == "__main__":
    test_api_usage()
