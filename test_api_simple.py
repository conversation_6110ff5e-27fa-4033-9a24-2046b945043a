#!/usr/bin/env python3
"""
Simple test for the API key provided by user
"""

from openai import OpenAI

# API key provided by user
api_key = "********************************************************************************************************************************************************************"

print("🔑 Testing API key...")
print(f"Key: {api_key[:20]}...{api_key[-10:]}")

try:
    client = OpenAI(api_key=api_key)
    
    response = client.chat.completions.create(
        model="gpt-4o-mini",
        messages=[
            {"role": "user", "content": "Say hello"}
        ],
        max_tokens=50
    )
    
    print("✅ API key works!")
    print(f"Response: {response.choices[0].message.content}")
    
except Exception as e:
    print(f"❌ Error: {e}")
    print("❌ API key may be invalid or expired")
