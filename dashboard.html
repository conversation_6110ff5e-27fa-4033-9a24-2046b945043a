<!DOCTYPE html>
<html lang="so">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tincada AI - Dashboard</title>
    <link rel="stylesheet" href="dashboard.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Dashboard Container -->
    <div class="dashboard-container">
        <!-- Sidebar -->
        <div class="sidebar" id="sidebar">
            <!-- Sidebar Header -->
            <div class="sidebar-header">
                <div class="logo">
                    <img src="images/icon.jpg" alt="Tincada AI" class="logo-img">
                    <span>Tincada AI</span>
                </div>
                <button class="sidebar-toggle" id="sidebarToggle">
                    <i class="fas fa-bars"></i>
                </button>
            </div>

            <!-- New Chat Button -->
            <div class="new-chat-section">
                <button class="new-chat-btn" id="newChatBtn">
                    <i class="fas fa-plus"></i>
                    <span>Sheeko Cusub</span>
                </button>
            </div>

            <!-- Search Section -->
            <div class="search-section">
                <div class="search-box">
                    <i class="fas fa-search"></i>
                    <input type="text" placeholder="Raadi sheekooyin..." id="searchInput">
                </div>
            </div>

            <!-- Navigation Menu -->
            <div class="nav-menu">
                <div class="nav-item active" id="chatsNav">
                    <i class="fas fa-comments"></i>
                    <span>Sheekooyin</span>
                </div>
                <div class="nav-item" id="gptsNav">
                    <i class="fas fa-robot"></i>
                    <span>GPTs</span>
                    <div class="coming-soon-badge">Soon</div>
                </div>
                <div class="nav-item" id="soraNav">
                    <i class="fas fa-video"></i>
                    <span>Sora</span>
                    <div class="coming-soon-badge">Soon</div>
                </div>
                <div class="nav-item" id="libraryNav">
                    <i class="fas fa-book"></i>
                    <span>Maktabada</span>
                    <div class="coming-soon-badge">Soon</div>
                </div>
            </div>

            <!-- Chat History -->
            <div class="chat-history-section">
                <div class="history-header">
                    <h3>Maanta</h3>
                </div>
                <div class="chat-list" id="chatList">
                    <!-- Chat items will be dynamically added here -->
                </div>
                
                <div class="history-header">
                    <h3>Shalay</h3>
                </div>
                <div class="chat-list" id="yesterdayChatList">
                    <!-- Yesterday's chats -->
                </div>
                
                <div class="history-header">
                    <h3>Usbuucii hore</h3>
                </div>
                <div class="chat-list" id="lastWeekChatList">
                    <!-- Last week's chats -->
                </div>
            </div>

            <!-- User Profile Section -->
            <div class="user-profile-section">
                <div class="user-profile" id="userProfile">
                    <div class="user-avatar" id="userAvatar">
                        <i class="fas fa-user"></i>
                    </div>
                    <div class="user-info">
                        <div class="user-name" id="userName">User</div>
                        <div class="user-status">Free</div>
                    </div>
                    <button class="profile-dropdown-btn" id="profileDropdownBtn">
                        <i class="fas fa-chevron-up"></i>
                    </button>
                </div>
                
                <!-- User Dropdown Menu -->
                <div class="user-dropdown" id="userDropdown">
                    <div class="dropdown-item" id="upgradeBtn">
                        <i class="fas fa-crown"></i>
                        <span>Upgrade plan</span>
                    </div>
                    <div class="dropdown-item" id="helpBtn">
                        <i class="fas fa-question-circle"></i>
                        <span>Help & Support</span>
                    </div>
                    <div class="dropdown-item" id="customizeBtn">
                        <i class="fas fa-palette"></i>
                        <span>Customize Tincada</span>
                    </div>
                    <div class="dropdown-item" id="settingsBtn">
                        <i class="fas fa-cog"></i>
                        <span>Settings</span>
                    </div>
                    <div class="dropdown-item" id="helpBtn">
                        <i class="fas fa-question-circle"></i>
                        <span>Help</span>
                        <i class="fas fa-chevron-right"></i>
                    </div>
                    <div class="dropdown-divider"></div>
                    <div class="dropdown-item" id="logoutBtn">
                        <i class="fas fa-sign-out-alt"></i>
                        <span>Log out</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="main-content" id="mainContent">
            <!-- API Status Banner -->
            <div class="api-status-banner" id="apiStatusBanner" style="display: none;">
                <div class="banner-content">
                    <i class="fas fa-exclamation-triangle"></i>
                    <span>Mock API mode la isticmaalayaa. Gal Settings si aad u geliso API key saxda ah.</span>
                    <button class="banner-close" id="closeBannerBtn">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
            <!-- Header -->
            <div class="main-header">
                <div class="header-left">
                    <button class="mobile-menu-btn" id="mobileMenuBtn">
                        <i class="fas fa-bars"></i>
                    </button>
                    <img src="images/icon.jpg" alt="Tincada AI" class="header-logo">
                    <h1>Tincada AI</h1>
                </div>
                <div class="header-right">
                    <div class="message-counter" id="messageCounter">
                        <i class="fas fa-comment-dots"></i>
                        <span id="messageCountText">0/1000</span>
                    </div>
                    <button class="upgrade-btn">
                        <i class="fas fa-crown"></i>
                        Get Plus
                    </button>
                </div>
            </div>

            <!-- Welcome Screen -->
            <div class="welcome-screen" id="welcomeScreen">
                <div class="welcome-content">
                    <h2 id="welcomeTitle">Maxaad ka fikirtaa maanta?</h2>
                    <div class="chat-input-container">
                        <div class="chat-input-wrapper">
                            <textarea 
                                id="mainChatInput" 
                                placeholder="Su'aal kasta weydii..."
                                rows="1"
                            ></textarea>
                            <div class="input-actions">
                                <button class="action-btn" id="attachBtn" title="Ku dar file">
                                    <i class="fas fa-paperclip"></i>
                                </button>
                                <button class="action-btn tools-btn" id="toolsBtn" title="Tools">
                                    <i class="fas fa-tools"></i>
                                    <span>Tools</span>
                                </button>
                                <button class="action-btn" id="voiceBtn" title="Voice message">
                                    <i class="fas fa-microphone"></i>
                                </button>
                                <button class="action-btn" id="sendBtn" title="Send message">
                                    <i class="fas fa-arrow-up"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Chat Interface -->
            <div class="chat-interface" id="chatInterface" style="display: none;">
                <div class="chat-messages" id="chatMessages">
                    <!-- Messages will be added here -->
                </div>
                
                <div class="chat-input-container">
                    <div class="chat-input-wrapper">
                        <textarea 
                            id="chatInput" 
                            placeholder="Fariin qor..."
                            rows="1"
                        ></textarea>
                        <div class="input-actions">
                            <button class="action-btn" id="chatAttachBtn">
                                <i class="fas fa-paperclip"></i>
                            </button>
                            <button class="action-btn" id="chatVoiceBtn">
                                <i class="fas fa-microphone"></i>
                            </button>
                            <button class="action-btn tools-btn" id="aiToolsBtn" title="AI Tools">
                                <i class="fas fa-tools"></i>
                            </button>
                            <button class="action-btn" id="chatSendBtn">
                                <i class="fas fa-arrow-up"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Settings Modal -->
    <div class="modal-overlay" id="settingsModal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h2>Settings</h2>
                <button class="modal-close" id="closeSettingsModal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <!-- Settings content will be added here -->
            </div>
        </div>
    </div>

    <!-- Payment Modal -->
    <div class="modal-overlay" id="paymentModal" style="display: none;">
        <div class="modal-content payment-modal">
            <div class="modal-header">
                <h2>💰 Upgrade Required</h2>
                <button class="modal-close" id="closePaymentModal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="payment-content">
                    <div class="limit-reached">
                        <i class="fas fa-exclamation-triangle"></i>
                        <h3>Waxaad gaartay xadka fariimaha maalinlaha ah!</h3>
                        <p>Waxaad diray <strong>1,000 fariin</strong> maanta. Si aad u sii waddo, fadlan doorso mid ka mid ah plans-yada hoose:</p>
                    </div>

                    <div class="pricing-plans">
                        <div class="plan-card recommended">
                            <div class="plan-badge">Recommended</div>
                            <h4>Premium Plan</h4>
                            <div class="plan-price">$9.99<span>/month</span></div>
                            <ul class="plan-features">
                                <li><i class="fas fa-check"></i> Unlimited messages</li>
                                <li><i class="fas fa-check"></i> Priority support</li>
                                <li><i class="fas fa-check"></i> Advanced AI features</li>
                                <li><i class="fas fa-check"></i> File upload (50MB)</li>
                                <li><i class="fas fa-check"></i> Voice messages</li>
                            </ul>
                            <button class="plan-btn premium-btn" data-plan="premium">
                                <i class="fas fa-crown"></i>
                                Upgrade to Premium
                            </button>
                        </div>

                        <div class="plan-card">
                            <h4>Basic Plan</h4>
                            <div class="plan-price">$4.99<span>/month</span></div>
                            <ul class="plan-features">
                                <li><i class="fas fa-check"></i> 5,000 messages/day</li>
                                <li><i class="fas fa-check"></i> Standard support</li>
                                <li><i class="fas fa-check"></i> Basic AI features</li>
                                <li><i class="fas fa-check"></i> File upload (10MB)</li>
                                <li><i class="fas fa-times"></i> Voice messages</li>
                            </ul>
                            <button class="plan-btn basic-btn" data-plan="basic">
                                <i class="fas fa-star"></i>
                                Upgrade to Basic
                            </button>
                        </div>

                        <div class="plan-card">
                            <h4>Daily Pass</h4>
                            <div class="plan-price">$1.99<span>/day</span></div>
                            <ul class="plan-features">
                                <li><i class="fas fa-check"></i> 2,000 messages today</li>
                                <li><i class="fas fa-check"></i> All AI features</li>
                                <li><i class="fas fa-check"></i> File upload (25MB)</li>
                                <li><i class="fas fa-check"></i> Voice messages</li>
                                <li><i class="fas fa-times"></i> Priority support</li>
                            </ul>
                            <button class="plan-btn daily-btn" data-plan="daily">
                                <i class="fas fa-calendar-day"></i>
                                Buy Daily Pass
                            </button>
                        </div>
                    </div>

                    <div class="payment-footer">
                        <p><i class="fas fa-shield-alt"></i> Secure payment powered by Stripe</p>
                        <p><i class="fas fa-undo"></i> 30-day money-back guarantee</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Payment Form Modal -->
    <div class="modal-overlay" id="paymentFormModal" style="display: none;">
        <div class="modal-content payment-form-modal">
            <div class="modal-header">
                <h2>💳 Complete Your Purchase</h2>
                <button class="modal-close" id="closePaymentFormModal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="payment-form-content">
                    <!-- Selected Plan Summary -->
                    <div class="plan-summary" id="planSummary">
                        <div class="selected-plan">
                            <h3 id="selectedPlanName">Premium Plan</h3>
                            <div class="plan-price-large" id="selectedPlanPrice">$9.99/month</div>
                            <ul class="plan-benefits" id="selectedPlanBenefits">
                                <li>✅ Unlimited messages</li>
                                <li>✅ Priority support</li>
                                <li>✅ Advanced AI features</li>
                            </ul>
                        </div>
                    </div>

                    <!-- Payment Form -->
                    <form class="payment-form" id="paymentForm">
                        <div class="form-section">
                            <h4><i class="fas fa-credit-card"></i> Payment Information</h4>

                            <div class="form-group">
                                <label for="cardNumber">Card Number</label>
                                <input type="text" id="cardNumber" placeholder="1234 5678 9012 3456" maxlength="19" required>
                                <div class="card-icons">
                                    <i class="fab fa-cc-visa"></i>
                                    <i class="fab fa-cc-mastercard"></i>
                                    <i class="fab fa-cc-amex"></i>
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="expiryDate">Expiry Date</label>
                                    <input type="text" id="expiryDate" placeholder="MM/YY" maxlength="5" required>
                                </div>
                                <div class="form-group">
                                    <label for="cvv">CVV</label>
                                    <input type="text" id="cvv" placeholder="123" maxlength="4" required>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="cardName">Name on Card</label>
                                <input type="text" id="cardName" placeholder="John Doe" required>
                            </div>
                        </div>

                        <div class="form-section">
                            <h4><i class="fas fa-map-marker-alt"></i> Billing Address</h4>

                            <div class="form-group">
                                <label for="email">Email Address</label>
                                <input type="email" id="email" placeholder="<EMAIL>" required>
                            </div>

                            <div class="form-group">
                                <label for="country">Country</label>
                                <select id="country" required>
                                    <option value="">Select Country</option>
                                    <option value="SL">Somaliland</option>
                                    <option value="SO">Somalia</option>
                                    <option value="DJ">Djibouti</option>
                                    <option value="KE">Kenya</option>
                                    <option value="ET">Ethiopia</option>
                                    <option value="US">United States</option>
                                    <option value="CA">Canada</option>
                                    <option value="GB">United Kingdom</option>
                                    <option value="AU">Australia</option>
                                    <option value="DE">Germany</option>
                                    <option value="FR">France</option>
                                    <option value="AE">United Arab Emirates</option>
                                    <option value="SA">Saudi Arabia</option>
                                    <option value="TR">Turkey</option>
                                    <option value="EG">Egypt</option>
                                </select>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="city">City</label>
                                    <input type="text" id="city" placeholder="New York" required>
                                </div>
                                <div class="form-group">
                                    <label for="zipCode">ZIP Code</label>
                                    <input type="text" id="zipCode" placeholder="10001" required>
                                </div>
                            </div>
                        </div>

                        <div class="form-section">
                            <div class="payment-security">
                                <div class="security-badges">
                                    <div class="security-item">
                                        <i class="fas fa-shield-alt"></i>
                                        <span>SSL Encrypted</span>
                                    </div>
                                    <div class="security-item">
                                        <i class="fas fa-lock"></i>
                                        <span>Secure Payment</span>
                                    </div>
                                    <div class="security-item">
                                        <i class="fas fa-undo"></i>
                                        <span>30-Day Refund</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="form-actions">
                            <button type="button" class="btn-secondary" id="backToPlanBtn">
                                <i class="fas fa-arrow-left"></i>
                                Back to Plans
                            </button>
                            <button type="submit" class="btn-primary" id="completePaymentBtn">
                                <i class="fas fa-credit-card"></i>
                                Complete Payment
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Chat Context Menu -->
    <div class="context-menu" id="chatContextMenu" style="display: none;">
        <div class="context-item" onclick="renameChat()">
            <i class="fas fa-edit"></i>
            <span>Magaca beddel</span>
        </div>
        <div class="context-item" onclick="exportChat()">
            <i class="fas fa-download"></i>
            <span>Export chat</span>
        </div>
        <div class="context-item" onclick="duplicateChat()">
            <i class="fas fa-copy"></i>
            <span>Nuqul samee</span>
        </div>
        <div class="context-divider"></div>
        <div class="context-item danger" onclick="deleteChat()">
            <i class="fas fa-trash"></i>
            <span>Tirtir</span>
        </div>
    </div>

    <!-- Rename Chat Modal -->
    <div class="modal-overlay" id="renameChatModal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Magaca Chat-ka beddel</h3>
                <button class="modal-close" onclick="closeRenameModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label for="newChatName">Magac cusub:</label>
                    <input type="text" id="newChatName" placeholder="Gali magaca cusub...">
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn-secondary" onclick="closeRenameModal()">Cancel</button>
                <button class="btn-primary" onclick="confirmRename()">Kaydi</button>
            </div>
        </div>
    </div>

    <!-- Help Modal -->
    <div class="modal-overlay" id="helpModal" style="display: none;">
        <div class="modal-content help-modal">
            <div class="modal-header">
                <h2><i class="fas fa-question-circle"></i> Help & Support</h2>
                <button class="modal-close" onclick="closeHelpModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="help-content">
                    <div class="help-tabs">
                        <button class="help-tab active" onclick="showHelpTab('getting-started')">
                            <i class="fas fa-play"></i> Getting Started
                        </button>
                        <button class="help-tab" onclick="showHelpTab('features')">
                            <i class="fas fa-star"></i> Features
                        </button>
                        <button class="help-tab" onclick="showHelpTab('shortcuts')">
                            <i class="fas fa-keyboard"></i> Shortcuts
                        </button>
                        <button class="help-tab" onclick="showHelpTab('faq')">
                            <i class="fas fa-question"></i> FAQ
                        </button>
                        <button class="help-tab" onclick="showHelpTab('contact')">
                            <i class="fas fa-envelope"></i> Contact
                        </button>
                    </div>

                    <div class="help-tab-content">
                        <!-- Getting Started Tab -->
                        <div class="help-section active" id="getting-started">
                            <h3>🚀 Getting Started with Tincada AI</h3>
                            <div class="help-steps">
                                <div class="help-step">
                                    <div class="step-number">1</div>
                                    <div class="step-content">
                                        <h4>Start a Conversation</h4>
                                        <p>Type your message in the chat box and press Enter or click Send.</p>
                                    </div>
                                </div>
                                <div class="help-step">
                                    <div class="step-number">2</div>
                                    <div class="step-content">
                                        <h4>Manage Your Chats</h4>
                                        <p>Right-click on any chat to rename, export, duplicate, or delete it.</p>
                                    </div>
                                </div>
                                <div class="help-step">
                                    <div class="step-number">3</div>
                                    <div class="step-content">
                                        <h4>Upgrade Your Plan</h4>
                                        <p>Click "Get Plus" to access unlimited messages and premium features.</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Features Tab -->
                        <div class="help-section" id="features">
                            <h3>✨ Features Overview</h3>
                            <div class="feature-grid">
                                <div class="feature-item">
                                    <i class="fas fa-comments"></i>
                                    <h4>Smart Conversations</h4>
                                    <p>AI-powered responses with context awareness</p>
                                </div>
                                <div class="feature-item">
                                    <i class="fas fa-code"></i>
                                    <h4>Code Support</h4>
                                    <p>Syntax highlighting, copy, and edit functionality</p>
                                </div>
                                <div class="feature-item">
                                    <i class="fas fa-download"></i>
                                    <h4>Export Chats</h4>
                                    <p>Download your conversations as JSON files</p>
                                </div>
                                <div class="feature-item">
                                    <i class="fas fa-palette"></i>
                                    <h4>Customization</h4>
                                    <p>Personalize themes and preferences</p>
                                </div>
                            </div>
                        </div>

                        <!-- Shortcuts Tab -->
                        <div class="help-section" id="shortcuts">
                            <h3>⌨️ Keyboard Shortcuts</h3>
                            <div class="shortcuts-list">
                                <div class="shortcut-item">
                                    <kbd>Enter</kbd>
                                    <span>Send message</span>
                                </div>
                                <div class="shortcut-item">
                                    <kbd>Shift + Enter</kbd>
                                    <span>New line</span>
                                </div>
                                <div class="shortcut-item">
                                    <kbd>Ctrl + N</kbd>
                                    <span>New chat</span>
                                </div>
                                <div class="shortcut-item">
                                    <kbd>Ctrl + /</kbd>
                                    <span>Toggle sidebar</span>
                                </div>
                                <div class="shortcut-item">
                                    <kbd>Esc</kbd>
                                    <span>Close modals</span>
                                </div>
                            </div>
                        </div>

                        <!-- FAQ Tab -->
                        <div class="help-section" id="faq">
                            <h3>❓ Frequently Asked Questions</h3>
                            <div class="faq-list">
                                <div class="faq-item">
                                    <h4>How many messages can I send per day?</h4>
                                    <p>Free users get 1,000 messages per day. Premium users get unlimited messages.</p>
                                </div>
                                <div class="faq-item">
                                    <h4>Can I export my chat history?</h4>
                                    <p>Yes! Right-click on any chat and select "Export chat" to download as JSON.</p>
                                </div>
                                <div class="faq-item">
                                    <h4>Is my data secure?</h4>
                                    <p>Absolutely! All conversations are encrypted and stored securely.</p>
                                </div>
                                <div class="faq-item">
                                    <h4>Can I customize the interface?</h4>
                                    <p>Yes! Click on your profile and select "Customize Tincada" for theme options.</p>
                                </div>
                            </div>
                        </div>

                        <!-- Contact Tab -->
                        <div class="help-section" id="contact">
                            <h3>📧 Contact Support</h3>
                            <div class="contact-options">
                                <div class="contact-item">
                                    <i class="fas fa-envelope"></i>
                                    <div>
                                        <h4>Email Support</h4>
                                        <p><EMAIL></p>
                                        <small>Response within 24 hours</small>
                                    </div>
                                </div>
                                <div class="contact-item">
                                    <i class="fas fa-comments"></i>
                                    <div>
                                        <h4>Live Chat</h4>
                                        <p>Available 9 AM - 6 PM EST</p>
                                        <button class="btn-primary">Start Chat</button>
                                    </div>
                                </div>
                                <div class="contact-item">
                                    <i class="fas fa-book"></i>
                                    <div>
                                        <h4>Documentation</h4>
                                        <p>Comprehensive guides and tutorials</p>
                                        <button class="btn-secondary">View Docs</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Customize Modal -->
    <div class="modal-overlay" id="customizeModal" style="display: none;">
        <div class="modal-content customize-modal">
            <div class="modal-header">
                <h2><i class="fas fa-palette"></i> Customize Tincada</h2>
                <button class="modal-close" onclick="closeCustomizeModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="customize-content">
                    <div class="customize-section">
                        <h3><i class="fas fa-user"></i> Profile Settings</h3>
                        <div class="profile-settings">
                            <div class="profile-picture-section">
                                <div class="current-avatar">
                                    <img src="https://via.placeholder.com/80x80/10a37f/ffffff?text=U" alt="Profile" id="currentAvatar">
                                    <div class="avatar-overlay">
                                        <i class="fas fa-camera"></i>
                                    </div>
                                </div>
                                <div class="avatar-controls">
                                    <h4>Profile Picture</h4>
                                    <p>Click to change your profile picture</p>
                                    <input type="file" id="avatarUpload" accept="image/*" style="display: none;">
                                    <button class="btn-primary" onclick="document.getElementById('avatarUpload').click()">
                                        <i class="fas fa-upload"></i> Upload New
                                    </button>
                                    <button class="btn-secondary" onclick="removeAvatar()">
                                        <i class="fas fa-trash"></i> Remove
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="customize-section">
                        <h3><i class="fas fa-paint-brush"></i> Theme Settings</h3>
                        <div class="theme-options">
                            <div class="theme-item active" data-theme="dark">
                                <div class="theme-preview dark-theme"></div>
                                <span>Dark Theme</span>
                            </div>
                            <div class="theme-item" data-theme="light">
                                <div class="theme-preview light-theme"></div>
                                <span>Light Theme</span>
                            </div>
                            <div class="theme-item" data-theme="auto">
                                <div class="theme-preview auto-theme"></div>
                                <span>Auto</span>
                            </div>
                        </div>
                    </div>

                    <div class="customize-section">
                        <h3><i class="fas fa-cog"></i> Preferences</h3>
                        <div class="preferences-list">
                            <div class="preference-item">
                                <div class="preference-info">
                                    <h4>Sound Effects</h4>
                                    <p>Play sounds for notifications and actions</p>
                                </div>
                                <label class="toggle-switch">
                                    <input type="checkbox" id="soundEffects" checked>
                                    <span class="slider"></span>
                                </label>
                            </div>
                            <div class="preference-item">
                                <div class="preference-info">
                                    <h4>Auto-save Chats</h4>
                                    <p>Automatically save conversations</p>
                                </div>
                                <label class="toggle-switch">
                                    <input type="checkbox" id="autoSave" checked>
                                    <span class="slider"></span>
                                </label>
                            </div>
                            <div class="preference-item">
                                <div class="preference-info">
                                    <h4>Typing Indicators</h4>
                                    <p>Show when AI is typing</p>
                                </div>
                                <label class="toggle-switch">
                                    <input type="checkbox" id="typingIndicators" checked>
                                    <span class="slider"></span>
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn-secondary" onclick="resetSettings()">Reset to Default</button>
                <button class="btn-primary" onclick="saveSettings()">Save Changes</button>
            </div>
        </div>
    </div>

    <!-- AI Tools Modal -->
    <div class="modal-overlay" id="aiToolsModal" style="display: none;">
        <div class="modal-content ai-tools-modal">
            <div class="modal-header">
                <h2><i class="fas fa-tools"></i> AI Tools</h2>
                <button class="modal-close" onclick="closeAIToolsModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="tools-grid">
                    <div class="tool-item" onclick="useTool('think-longer')">
                        <div class="tool-icon">
                            💡
                        </div>
                        <div class="tool-content">
                            <h3>Think longer</h3>
                            <p>Extended reasoning iyo deep analysis for complex problems</p>
                        </div>
                    </div>

                    <div class="tool-item" onclick="useTool('deep-research')">
                        <div class="tool-icon">
                            🔍
                        </div>
                        <div class="tool-content">
                            <h3>Deep research</h3>
                            <p>Comprehensive research oo leh multiple sources iyo citations</p>
                        </div>
                    </div>

                    <div class="tool-item" onclick="useTool('create-image')">
                        <div class="tool-icon">
                            🎨
                        </div>
                        <div class="tool-content">
                            <h3>Create image</h3>
                            <p>AI-powered image generation from text descriptions</p>
                        </div>
                    </div>

                    <div class="tool-item" onclick="useTool('web-search')">
                        <div class="tool-icon">
                            🌐
                        </div>
                        <div class="tool-content">
                            <h3>Web search</h3>
                            <p>Real-time internet search oo leh current information</p>
                        </div>
                    </div>

                    <div class="tool-item" onclick="useTool('canvas')">
                        <div class="tool-icon">
                            ✏️
                        </div>
                        <div class="tool-content">
                            <h3>Canvas</h3>
                            <p>Interactive workspace for visual collaboration iyo editing</p>
                        </div>
                    </div>
                </div>

                <div class="tool-item" onclick="closeAIToolsModal()">
                    <div class="tool-icon">
                        🛠️
                    </div>
                    <div class="tool-content">
                        <h3>Tools</h3>
                        <p>Close tools menu and return to chat</p>
                    </div>
                </div>

                <div class="tools-footer">
                    <p><i class="fas fa-info-circle"></i> Select a tool to enhance your AI experience</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Tool Usage Modal -->
    <div class="modal-overlay" id="toolUsageModal" style="display: none;">
        <div class="modal-content tool-usage-modal">
            <div class="modal-header">
                <h2 id="toolUsageTitle"><i class="fas fa-lightbulb"></i> Think Longer</h2>
                <button class="modal-close" onclick="closeToolUsageModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="tool-usage-content" id="toolUsageContent">
                    <!-- Tool-specific content will be loaded here -->
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn-secondary" onclick="closeToolUsageModal()">Cancel</button>
                <button class="btn-primary" onclick="executeToolAction()" id="executeToolBtn">
                    <i class="fas fa-play"></i> Execute
                </button>
            </div>
        </div>
    </div>

    <!-- Typing Indicator -->
    <div class="typing-indicator" id="typingIndicator" style="display: none;">
        <div class="message ai">
            <div class="message-avatar">
                <img src="images/icon.jpg" alt="Tincada AI" class="ai-avatar">
            </div>
            <div class="message-content">
                <div class="typing-animation">
                    <div class="typing-dot"></div>
                    <div class="typing-dot"></div>
                    <div class="typing-dot"></div>
                </div>
                <div class="typing-text">Tincada AI ayaa qoraya...</div>
            </div>
        </div>
    </div>

    <!-- Loading Overlay -->
    <div class="loading-overlay" id="loadingOverlay" style="display: none;">
        <div class="loading-spinner">
            <div class="loading-icon-container">
                <div class="loading-ring"></div>
                <div class="loading-ring"></div>
                <img src="images/icon.jpg" alt="Tincada AI" class="loading-icon">
            </div>
            <div class="loading-text">Tincada AI</div>
            <div class="loading-subtitle">ayaa ka jawaabaysa...</div>
            <div class="loading-dots">
                <div class="loading-dot"></div>
                <div class="loading-dot"></div>
                <div class="loading-dot"></div>
            </div>
        </div>
    </div>

    <!-- Toast Container -->
    <div class="toast-container" id="toastContainer"></div>

    <!-- Code Edit Modal -->
    <div class="code-edit-modal" id="codeEditModal">
        <div class="code-edit-content">
            <div class="code-edit-header">
                <h3 class="code-edit-title">
                    <i class="fas fa-code"></i>
                    <span id="editModalTitle">Edit Code</span>
                </h3>
                <button class="code-edit-close" onclick="closeCodeEditModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="code-edit-body">
                <div class="code-preview-section">
                    <div class="code-preview-label">
                        <i class="fas fa-eye"></i>
                        Original Code Preview
                    </div>
                    <div class="code-preview-container">
                        <div class="code-preview-content" id="originalCodePreview"></div>
                    </div>
                </div>

                <div class="code-preview-label">
                    <i class="fas fa-edit"></i>
                    Edit Code
                </div>
                <textarea class="code-edit-textarea" id="codeEditTextarea" placeholder="Edit your code here..."></textarea>
            </div>
            <div class="code-edit-actions">
                <div class="code-edit-actions-left">
                    <button class="code-edit-btn info" onclick="formatCode()">
                        <i class="fas fa-magic"></i>
                        Format
                    </button>
                    <button class="code-edit-btn secondary" onclick="resetCode()">
                        <i class="fas fa-undo"></i>
                        Reset
                    </button>
                </div>
                <div class="code-edit-actions-right">
                    <button class="code-edit-btn secondary" onclick="closeCodeEditModal()">
                        <i class="fas fa-times"></i>
                        Cancel
                    </button>
                    <button class="code-edit-btn success" onclick="saveEditedCode()">
                        <i class="fas fa-save"></i>
                        Save Changes
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Hidden File Input -->
    <input type="file" id="fileInput" multiple accept="image/*,video/*,.pdf,.doc,.docx,.txt" style="display: none;">

    <script src="ai-code-component.js"></script>
    <script src="auth.js"></script>
    <script src="dashboard.js"></script>
    <script>
        // Initialize dashboard
        let tincadaAI;
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 Initializing Tincada AI Dashboard...');
            tincadaAI = new TincadaDashboard();
            console.log('✅ Dashboard initialized successfully');
        });
    </script>
</body>
</html>
