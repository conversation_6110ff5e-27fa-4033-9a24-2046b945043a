<?php
/**
 * Database Setup Script
 * Run this script to create the database and tables
 */

// Database configuration
$host = 'localhost';
$username = 'root';
$password = ''; // Change this to your MySQL password
$database = 'tincada_ai_db';

echo "<h1>🚀 Tincada AI Database Setup</h1>";
echo "<div style='font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px;'>";

try {
    // Connect to MySQL server (without database)
    echo "<h2>📡 Connecting to MySQL server...</h2>";
    $pdo = new PDO("mysql:host=$host", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "<p style='color: green;'>✅ Connected to MySQL server successfully!</p>";
    
    // Create database
    echo "<h2>🗄️ Creating database...</h2>";
    $pdo->exec("CREATE DATABASE IF NOT EXISTS $database CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    echo "<p style='color: green;'>✅ Database '$database' created successfully!</p>";
    
    // Connect to the new database
    $pdo = new PDO("mysql:host=$host;dbname=$database", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Read and execute SQL file
    echo "<h2>📋 Creating tables...</h2>";
    $sql_content = file_get_contents('database.sql');
    
    if ($sql_content === false) {
        throw new Exception("Could not read database.sql file");
    }
    
    // Split SQL content into individual statements
    $statements = array_filter(
        array_map('trim', explode(';', $sql_content)),
        function($stmt) {
            return !empty($stmt) && !preg_match('/^--/', $stmt);
        }
    );
    
    $created_tables = 0;
    $created_views = 0;
    $inserted_records = 0;
    
    foreach ($statements as $statement) {
        if (empty(trim($statement))) continue;
        
        try {
            $pdo->exec($statement);
            
            if (stripos($statement, 'CREATE TABLE') !== false) {
                $created_tables++;
                preg_match('/CREATE TABLE\s+(?:IF NOT EXISTS\s+)?`?(\w+)`?/i', $statement, $matches);
                $table_name = $matches[1] ?? 'unknown';
                echo "<p style='color: blue;'>📊 Created table: $table_name</p>";
            } elseif (stripos($statement, 'CREATE VIEW') !== false) {
                $created_views++;
                preg_match('/CREATE VIEW\s+`?(\w+)`?/i', $statement, $matches);
                $view_name = $matches[1] ?? 'unknown';
                echo "<p style='color: purple;'>👁️ Created view: $view_name</p>";
            } elseif (stripos($statement, 'INSERT INTO') !== false) {
                $inserted_records++;
                preg_match('/INSERT INTO\s+`?(\w+)`?/i', $statement, $matches);
                $table_name = $matches[1] ?? 'unknown';
                echo "<p style='color: orange;'>📝 Inserted sample data into: $table_name</p>";
            } elseif (stripos($statement, 'CREATE INDEX') !== false) {
                preg_match('/CREATE INDEX\s+`?(\w+)`?/i', $statement, $matches);
                $index_name = $matches[1] ?? 'unknown';
                echo "<p style='color: gray;'>🔍 Created index: $index_name</p>";
            }
            
        } catch (PDOException $e) {
            echo "<p style='color: red;'>❌ Error executing statement: " . $e->getMessage() . "</p>";
            echo "<pre style='background: #f5f5f5; padding: 10px; border-radius: 5px;'>" . htmlspecialchars(substr($statement, 0, 200)) . "...</pre>";
        }
    }
    
    echo "<h2>📊 Setup Summary</h2>";
    echo "<div style='background: #f0f8ff; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<p><strong>✅ Database:</strong> $database</p>";
    echo "<p><strong>📊 Tables created:</strong> $created_tables</p>";
    echo "<p><strong>👁️ Views created:</strong> $created_views</p>";
    echo "<p><strong>📝 Sample records:</strong> $inserted_records</p>";
    echo "</div>";
    
    // Test database connection
    echo "<h2>🧪 Testing database connection...</h2>";
    
    // Test users table
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM users");
    $user_count = $stmt->fetch()['count'];
    echo "<p style='color: green;'>✅ Users table: $user_count records</p>";
    
    // Test conversations table
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM conversations");
    $conv_count = $stmt->fetch()['count'];
    echo "<p style='color: green;'>✅ Conversations table: $conv_count records</p>";
    
    // Test chat_messages table
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM chat_messages");
    $msg_count = $stmt->fetch()['count'];
    echo "<p style='color: green;'>✅ Chat messages table: $msg_count records</p>";
    
    // Test views
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM user_stats");
    $stats_count = $stmt->fetch()['count'];
    echo "<p style='color: green;'>✅ User stats view: $stats_count records</p>";
    
    echo "<h2>🎉 Setup Complete!</h2>";
    echo "<div style='background: #d4edda; color: #155724; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h3>✅ Database is ready for use!</h3>";
    echo "<p><strong>Next steps:</strong></p>";
    echo "<ul>";
    echo "<li>📝 Update config.php with your database credentials</li>";
    echo "<li>🌐 Access the dashboard at: <a href='index.php'>index.php</a></li>";
    echo "<li>🧪 Test the API at: <a href='api_handler.php'>api_handler.php</a></li>";
    echo "<li>📊 View analytics at: <a href='dashboard_api.php?action=overview'>dashboard_api.php</a></li>";
    echo "</ul>";
    echo "</div>";
    
    // Show connection details
    echo "<h2>🔧 Configuration Details</h2>";
    echo "<div style='background: #fff3cd; color: #856404; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<p><strong>Database Host:</strong> $host</p>";
    echo "<p><strong>Database Name:</strong> $database</p>";
    echo "<p><strong>Database User:</strong> $username</p>";
    echo "<p><strong>Character Set:</strong> utf8mb4</p>";
    echo "<p><strong>Collation:</strong> utf8mb4_unicode_ci</p>";
    echo "</div>";
    
} catch (PDOException $e) {
    echo "<div style='background: #f8d7da; color: #721c24; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h2>❌ Database Setup Failed</h2>";
    echo "<p><strong>Error:</strong> " . $e->getMessage() . "</p>";
    echo "<p><strong>Possible solutions:</strong></p>";
    echo "<ul>";
    echo "<li>Check if MySQL server is running</li>";
    echo "<li>Verify database credentials in this script</li>";
    echo "<li>Ensure MySQL user has CREATE DATABASE privileges</li>";
    echo "<li>Check if database.sql file exists in the same directory</li>";
    echo "</ul>";
    echo "</div>";
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; color: #721c24; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h2>❌ Setup Error</h2>";
    echo "<p><strong>Error:</strong> " . $e->getMessage() . "</p>";
    echo "</div>";
}

echo "</div>";

// Add some styling
echo "<style>
    body { 
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        margin: 0;
        padding: 20px;
        min-height: 100vh;
    }
    h1 { 
        color: white; 
        text-align: center; 
        text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        margin-bottom: 30px;
    }
    div {
        background: white;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    }
    a {
        color: #007bff;
        text-decoration: none;
    }
    a:hover {
        text-decoration: underline;
    }
    pre {
        overflow-x: auto;
        font-size: 12px;
    }
</style>";
?>
