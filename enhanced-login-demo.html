<!DOCTYPE html>
<html lang="so">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enhanced Login Demo - Tincada AI</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
            padding: 2rem;
            margin: 0;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 3rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        h1 {
            text-align: center;
            font-size: 2.5rem;
            margin-bottom: 1rem;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .subtitle {
            text-align: center;
            font-size: 1.2rem;
            margin-bottom: 3rem;
            opacity: 0.9;
        }
        
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-bottom: 3rem;
        }
        
        .feature-card {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 2rem;
            transition: all 0.3s ease;
            text-align: center;
        }
        
        .feature-card:hover {
            transform: translateY(-5px);
            border-color: rgba(255, 255, 255, 0.2);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);
        }
        
        .feature-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
        }
        
        .feature-card h3 {
            color: #ffffff;
            margin-bottom: 1rem;
        }
        
        .feature-card p {
            color: #cccccc;
            line-height: 1.6;
        }
        
        .demo-section {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            border-left: 4px solid #4ecdc4;
        }
        
        .demo-section h2 {
            color: #4ecdc4;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .social-buttons-demo {
            display: flex;
            flex-direction: column;
            gap: 1rem;
            max-width: 400px;
            margin: 2rem auto;
        }
        
        .google-btn-demo {
            background: #ffffff;
            color: #333;
            border: 1px solid rgba(255, 255, 255, 0.2);
            padding: 15px;
            border-radius: 10px;
            font-size: 1rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 12px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }
        
        .google-btn-demo:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
        }
        
        .apple-btn-demo {
            background: #000000;
            color: #ffffff;
            border: 1px solid rgba(255, 255, 255, 0.2);
            padding: 15px;
            border-radius: 10px;
            font-size: 1rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 12px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
        }
        
        .apple-btn-demo:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.4);
            background: #1a1a1a;
        }
        
        .divider-demo {
            display: flex;
            align-items: center;
            margin: 1.5rem 0;
            color: rgba(255, 255, 255, 0.6);
            font-size: 0.9rem;
        }
        
        .divider-demo::before,
        .divider-demo::after {
            content: '';
            flex: 1;
            height: 1px;
            background: rgba(255, 255, 255, 0.2);
        }
        
        .divider-demo span {
            padding: 0 1rem;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 20px;
            padding: 5px 15px;
        }
        
        .success {
            background: rgba(76, 175, 80, 0.2);
            border: 1px solid rgba(76, 175, 80, 0.3);
            border-radius: 10px;
            padding: 1.5rem;
            margin: 2rem 0;
            border-left: 4px solid #4caf50;
            text-align: center;
        }
        
        .demo-buttons {
            display: flex;
            gap: 20px;
            justify-content: center;
            margin-top: 3rem;
            flex-wrap: wrap;
        }
        
        .demo-btn {
            background: linear-gradient(45deg, #4ecdc4, #44a08d);
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 25px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 10px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(78, 205, 196, 0.3);
        }
        
        .demo-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(78, 205, 196, 0.4);
        }
        
        .demo-btn.primary {
            background: linear-gradient(45deg, #10a37f, #0d8f6f);
            box-shadow: 0 4px 15px rgba(16, 163, 127, 0.3);
        }
        
        .demo-btn.primary:hover {
            box-shadow: 0 6px 20px rgba(16, 163, 127, 0.4);
        }
        
        .demo-btn.secondary {
            background: linear-gradient(45deg, #ff6b6b, #ee5a52);
            box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
        }
        
        .demo-btn.secondary:hover {
            box-shadow: 0 6px 20px rgba(255, 107, 107, 0.4);
        }
        
        @media (max-width: 768px) {
            body { padding: 1rem; }
            .container { padding: 2rem 1rem; }
            h1 { font-size: 2rem; }
            .features-grid { grid-template-columns: 1fr; }
            .demo-buttons { flex-direction: column; align-items: center; }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 Enhanced Login System</h1>
        <p class="subtitle">Beautiful login page oo leh Google iyo Apple Sign In integration</p>
        
        <div class="features-grid">
            <div class="feature-card">
                <div class="feature-icon">🎨</div>
                <h3>Modern Design</h3>
                <p>Glassmorphism design oo leh beautiful animations, floating backgrounds, iyo professional styling</p>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">🔐</div>
                <h3>Social Login</h3>
                <p>Google iyo Apple Sign In integration oo leh smooth animations iyo loading states</p>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">📱</div>
                <h3>Responsive</h3>
                <p>Mobile-friendly design oo leh adaptive layouts iyo touch-optimized interactions</p>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">⚡</div>
                <h3>Interactive</h3>
                <p>Hover effects, ripple animations, loading spinners, iyo smooth transitions</p>
            </div>
        </div>
        
        <div class="demo-section">
            <h2>🔑 Social Login Buttons</h2>
            <p>Enhanced social login buttons oo leh professional styling:</p>
            
            <div class="social-buttons-demo">
                <div class="divider-demo">
                    <span>ama</span>
                </div>
                
                <button class="google-btn-demo">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                        <path d="M22.56 12.25C22.56 11.47 22.49 10.72 22.36 10H12V14.26H17.92C17.66 15.6 16.92 16.74 15.84 17.5V20.26H19.28C21.36 18.43 22.56 15.6 22.56 12.25Z" fill="#4285F4"/>
                        <path d="M12 23C15.24 23 17.95 21.92 19.28 20.26L15.84 17.5C14.78 18.13 13.47 18.5 12 18.5C8.87 18.5 6.22 16.64 5.27 13.94H2.76V16.74C4.05 19.3 7.79 23 12 23Z" fill="#34A853"/>
                        <path d="M5.27 13.94C5.02 13.31 4.89 12.66 4.89 12C4.89 11.34 5.02 10.69 5.27 10.06V7.26H2.76C2.1 8.59 1.75 10.25 1.75 12C1.75 13.75 2.1 15.41 2.76 16.74L5.27 13.94Z" fill="#FBBC04"/>
                        <path d="M12 5.5C13.62 5.5 15.06 6.09 16.2 7.2L19.18 4.22C17.95 3.09 15.24 2.25 12 2.25C7.79 2.25 4.05 5.95 2.76 8.51L5.27 11.31C6.22 8.61 8.87 6.75 12 6.75Z" fill="#EA4335"/>
                    </svg>
                    <span>Ku soo gal Google</span>
                </button>
                
                <button class="apple-btn-demo">
                    <i class="fab fa-apple" style="font-size: 1.2rem;"></i>
                    <span>Ku soo gal Apple</span>
                </button>
            </div>
        </div>
        
        <div class="demo-section">
            <h2>✨ Enhanced Features</h2>
            <div style="background: rgba(255,255,255,0.05); padding: 1.5rem; border-radius: 10px; margin-top: 1rem;">
                <h4 style="color: #4ecdc4; margin-bottom: 1rem;">🎨 Visual Enhancements:</h4>
                <ul style="color: #cccccc; line-height: 1.8; list-style: none; padding: 0;">
                    <li>🌟 <strong>Glassmorphism Design:</strong> Modern glass effect with backdrop blur</li>
                    <li>🎭 <strong>Floating Background:</strong> Animated dot pattern background</li>
                    <li>💫 <strong>Ripple Effects:</strong> Button press animations</li>
                    <li>🔄 <strong>Loading States:</strong> Spinner animations during login</li>
                    <li>📈 <strong>Hover Effects:</strong> Smooth scale and elevation changes</li>
                    <li>🎨 <strong>Gradient Buttons:</strong> Beautiful color transitions</li>
                </ul>
                
                <h4 style="color: #4ecdc4; margin: 1.5rem 0 1rem;">🔐 Functionality:</h4>
                <ul style="color: #cccccc; line-height: 1.8; list-style: none; padding: 0;">
                    <li>🔑 <strong>Google OAuth:</strong> Secure Google Sign In integration</li>
                    <li>🍎 <strong>Apple Sign In:</strong> Native Apple authentication</li>
                    <li>📱 <strong>Mobile Optimized:</strong> Touch-friendly interactions</li>
                    <li>⚡ <strong>Fast Loading:</strong> Optimized animations and transitions</li>
                    <li>🛡️ <strong>Secure:</strong> Modern authentication standards</li>
                </ul>
            </div>
        </div>
        
        <div class="success">
            <h3>🎉 Enhanced Login System Complete!</h3>
            <p><strong>✅ Modern Design:</strong> Glassmorphism oo leh beautiful animations</p>
            <p><strong>✅ Social Login:</strong> Google iyo Apple Sign In integration</p>
            <p><strong>✅ Interactive UI:</strong> Hover effects, ripples, loading states</p>
            <p><strong>✅ Mobile Ready:</strong> Responsive design oo touch-optimized</p>
            <p><strong>✅ Professional Quality:</strong> Production-ready authentication system</p>
        </div>
        
        <div class="demo-buttons">
            <a href="login.html" class="demo-btn primary">
                🔐 Test Enhanced Login
            </a>
            <a href="dashboard.html" class="demo-btn">
                🏠 Go to Dashboard
            </a>
            <a href="complete-system-demo.html" class="demo-btn secondary">
                🎯 Complete System
            </a>
        </div>
    </div>
    
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</body>
</html>
