#!/usr/bin/env python3
"""
Tincada AI Fixed Server - Secure & Working
Complete OpenAI integration with multilingual support
"""

from flask import Flask, request, jsonify, send_from_directory
from flask_cors import CORS
import os
import json
import logging
from datetime import datetime
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize Flask app
app = Flask(__name__)
app.config['SECRET_KEY'] = '$c=j0hf5qz@8vod&#p@4*48-nsrzv-rtzoca+o80*!cgy+n5'
CORS(app)

# OpenAI Configuration - Use new API key
OPENAI_API_KEY = '********************************************************************************************************************************************************************'

# Global variables
openai_client = None
OPENAI_AVAILABLE = False

# Initialize OpenAI client
try:
    from openai import OpenAI
    openai_client = OpenAI(api_key=OPENAI_API_KEY)
    # Test the connection
    test_response = openai_client.models.list()
    OPENAI_AVAILABLE = True
    logger.info("✅ OpenAI client initialized and tested successfully with new API key")
except ImportError:
    OPENAI_AVAILABLE = False
    openai_client = None
    logger.warning("⚠️ OpenAI library not installed")
except Exception as e:
    OPENAI_AVAILABLE = False
    openai_client = None
    logger.error(f"❌ OpenAI initialization failed: {e}")

def get_openai_response(message, chat_history=None):
    """Get response from OpenAI API"""
    if not OPENAI_AVAILABLE or not openai_client:
        return None
    
    try:
        # Detect language
        language = detect_language(message)
        
        # Build system message
        system_message = get_system_message(language)
        
        # Build messages
        messages = [{"role": "system", "content": system_message}]
        
        # Add chat history (last 5 messages)
        if chat_history:
            for msg in chat_history[-5:]:
                messages.append({
                    "role": msg.get("role", "user"),
                    "content": msg.get("content", "")
                })
        
        # Add current message
        messages.append({"role": "user", "content": message})
        
        # Make API call
        response = openai_client.chat.completions.create(
            model="gpt-4o-mini",
            messages=messages,
            max_tokens=1500,
            temperature=0.7
        )
        
        return response.choices[0].message.content
        
    except Exception as e:
        logger.error(f"OpenAI API error: {e}")
        return None

def detect_language(message):
    """Simple language detection"""
    message_lower = message.lower()
    
    # Somali indicators
    somali_words = ['waa', 'maxaa', 'sidee', 'marka', 'haddii', 'laakiin', 'waxaan', 'tahay', 'yahay']
    if any(word in message_lower for word in somali_words):
        return 'somali'
    
    # Arabic indicators  
    if any(char in message for char in 'السلامعليكممرحباكيفماذاهل'):
        return 'arabic'
    
    # French indicators
    french_words = ['bonjour', 'comment', 'vous', 'êtes', 'dans', 'avec', 'pour']
    if any(word in message_lower for word in french_words):
        return 'french'
    
    # Spanish indicators
    spanish_words = ['hola', 'cómo', 'está', 'qué', 'para', 'con', 'por']
    if any(word in message_lower for word in spanish_words):
        return 'spanish'
    
    return 'english'

def get_system_message(language):
    """Get system message based on language"""
    messages = {
        'somali': """Waxaad tahay Tincada AI, kaaliye AI ah oo aad u aqoon badan. 
        Waxaad ku hadashaa af-Soomaali si dabiici ah oo qurux badan.
        Waxaad bixisaa jawaabo faa'iido leh, cad, oo sharaxaad leh.
        Haddii la weydiiyo coding ama tiknoolajiyad, bixi tusaalooyin cad.""",
        
        'arabic': """أنت Tincada AI، مساعد ذكي مفيد ومتخصص. 
        تتحدث العربية بطلاقة وتقدم إجابات مفيدة ودقيقة.
        إذا سُئلت عن البرمجة أو التكنولوجيا، قدم أمثلة واضحة.""",
        
        'french': """Vous êtes Tincada AI, un assistant IA utile et compétent. 
        Vous parlez français couramment et fournissez des réponses utiles et précises.
        Si on vous demande de la programmation, donnez des exemples clairs.""",
        
        'spanish': """Eres Tincada AI, un asistente de IA útil y competente. 
        Hablas español con fluidez y proporcionas respuestas útiles y precisas.
        Si te preguntan sobre programación, da ejemplos claros.""",
        
        'english': """You are Tincada AI, a helpful and knowledgeable AI assistant. 
        You speak multiple languages fluently and provide helpful, accurate information.
        If asked about coding or technology, provide clear examples with explanations."""
    }
    
    return messages.get(language, messages['english'])

def get_fallback_response(message):
    """Generate fallback response when OpenAI is unavailable"""
    message_lower = message.lower()
    
    # Detect language and respond appropriately
    if any(word in message_lower for word in ['salam', 'maxaad', 'sidee', 'waa', 'tahay']):
        return """Salam alaykum! 🌟 Waxaan ahay Tincada AI.

Hadda waxaan isticmaalayaa fallback mode maxaa yeelay OpenAI API-gu ma shaqaynayo. 
Laakiin weli waan kaa caawin karaa:

📝 **Su'aalo guud**
💻 **Programming tusaalayaal**  
🔄 **Macluumaad asaasi ah**
📚 **Talo iyo hagitaan**

Fadlan dib ii soo qor su'aashaada si cad!"""
    
    elif any(word in message_lower for word in ['hello', 'hi', 'how', 'what', 'help']):
        return """Hello! 👋 I'm Tincada AI.

I'm currently running in fallback mode because the OpenAI API is unavailable. 
However, I can still help you with:

📝 **General questions**
💻 **Programming examples**
🔄 **Basic information**
📚 **Advice and guidance**

Please rephrase your question and I'll do my best to help!"""
    
    else:
        return """Hello! مرحبا! Salam alaykum! 🌍

I'm Tincada AI, currently in fallback mode. I can help with:
- Programming questions
- General information  
- Multiple languages support

Please ask your question clearly and I'll assist you!"""

@app.route('/')
def index():
    """Serve the main dashboard"""
    try:
        return send_from_directory('.', 'dashboard.html')
    except:
        return """
        <html>
        <head><title>Tincada AI Server</title></head>
        <body>
            <h1>🚀 Tincada AI Server is Running!</h1>
            <p>✅ Server Status: Active</p>
            <p>🔧 API Health: <a href="/api/health">/api/health</a></p>
            <p>💬 Chat API: <a href="/api/chat">/api/chat</a></p>
            <p>📱 Dashboard: <a href="/dashboard.html">dashboard.html</a></p>
        </body>
        </html>
        """

@app.route('/<path:filename>')
def serve_static(filename):
    """Serve static files"""
    try:
        return send_from_directory('.', filename)
    except:
        return jsonify({'error': 'File not found'}), 404

@app.route('/api/health', methods=['GET'])
def health():
    """Health check endpoint"""
    return jsonify({
        'status': 'healthy',
        'service': 'Tincada AI Fixed Server',
        'version': '5.0.0',
        'timestamp': datetime.now().isoformat(),
        'openai_available': OPENAI_AVAILABLE,
        'openai_configured': bool(OPENAI_API_KEY),
        'features': {
            'chat': True,
            'multilingual': True,
            'fallback_responses': True,
            'real_openai_responses': OPENAI_AVAILABLE
        },
        'endpoints': {
            'chat': '/api/chat',
            'health': '/api/health'
        }
    })

@app.route('/api/chat', methods=['POST'])
def chat():
    """Main chat endpoint - ONLY ONE DEFINITION"""
    try:
        # Get request data
        data = request.get_json()
        
        if not data:
            return jsonify({'error': 'No JSON data provided'}), 400
        
        message = data.get('message', '').strip()
        user_id = data.get('user_id', 'anonymous')
        chat_history = data.get('chat_history', [])
        
        if not message:
            return jsonify({'error': 'Message is required'}), 400
        
        logger.info(f"💬 Chat request from {user_id}: {message[:50]}...")
        
        # Try OpenAI first
        ai_response = get_openai_response(message, chat_history)
        
        if ai_response:
            # OpenAI success
            logger.info(f"✅ OpenAI response: {len(ai_response)} characters")
            return jsonify({
                'response': ai_response,
                'source': 'openai',
                'model': 'gpt-4o-mini',
                'timestamp': datetime.now().isoformat(),
                'status': 'success'
            })
        
        else:
            # Fallback response
            logger.warning("⚠️ Using fallback response")
            fallback_response = get_fallback_response(message)
            
            return jsonify({
                'response': fallback_response,
                'source': 'fallback',
                'model': 'tincada-fallback',
                'timestamp': datetime.now().isoformat(),
                'status': 'fallback'
            })
            
    except Exception as e:
        logger.error(f"❌ Chat endpoint error: {str(e)}")
        return jsonify({
            'error': 'Internal server error',
            'message': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500

if __name__ == '__main__':
    print("🚀 Starting Tincada AI Fixed Server v5.0")
    print("=" * 60)
    print(f"🔑 OpenAI API: {'✅ Available' if OPENAI_AVAILABLE else '❌ Not available'}")
    print(f"🌐 Server URL: http://localhost:5002")
    print(f"📱 Dashboard: http://localhost:5002/")
    print(f"🔧 Health Check: http://localhost:5002/api/health")
    print(f"💬 Chat API: http://localhost:5002/api/chat")
    print("=" * 60)
    print("🎯 Features:")
    print("   ✅ Real OpenAI GPT-4o-mini integration")
    print("   ✅ New API key configured")
    print("   ✅ Multilingual support (Somali, English, Arabic, etc.)")
    print("   ✅ Smart fallback responses")
    print("   ✅ Fixed application context")
    print("   ✅ Single chat endpoint (no duplicates)")
    print("=" * 60)
    print("⚡ Press Ctrl+C to stop the server")
    print()
    
    app.run(host='0.0.0.0', port=5002, debug=False)
