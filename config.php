<?php
/**
 * Tincada AI Database Configuration
 * PHP configuration file for database connection
 */

// Database configuration
define('DB_HOST', 'localhost');
define('DB_USERNAME', 'root');
define('DB_PASSWORD', ''); // Change this to your MySQL password
define('DB_NAME', 'tincada_ai_db');
define('DB_CHARSET', 'utf8mb4');

// API Configuration
define('TINCADA_API_URL', 'http://localhost:5001/api');
define('API_TIMEOUT', 30);

// Security settings
define('SESSION_TIMEOUT', 3600); // 1 hour
define('MAX_MESSAGE_LENGTH', 5000);
define('MAX_DAILY_REQUESTS', 1000);

// Error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

/**
 * Database connection class
 */
class Database {
    private $connection;
    private static $instance = null;
    
    private function __construct() {
        try {
            $dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET;
            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
            ];
            
            $this->connection = new PDO($dsn, DB_USERNAME, DB_PASSWORD, $options);
            
        } catch (PDOException $e) {
            die("Database connection failed: " . $e->getMessage());
        }
    }
    
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    public function getConnection() {
        return $this->connection;
    }
    
    /**
     * Execute a prepared statement
     */
    public function execute($sql, $params = []) {
        try {
            $stmt = $this->connection->prepare($sql);
            $stmt->execute($params);
            return $stmt;
        } catch (PDOException $e) {
            error_log("Database error: " . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * Get single row
     */
    public function fetchRow($sql, $params = []) {
        $stmt = $this->execute($sql, $params);
        return $stmt->fetch();
    }
    
    /**
     * Get multiple rows
     */
    public function fetchAll($sql, $params = []) {
        $stmt = $this->execute($sql, $params);
        return $stmt->fetchAll();
    }
    
    /**
     * Insert data and return last insert ID
     */
    public function insert($sql, $params = []) {
        $this->execute($sql, $params);
        return $this->connection->lastInsertId();
    }
    
    /**
     * Get row count
     */
    public function count($sql, $params = []) {
        $stmt = $this->execute($sql, $params);
        return $stmt->rowCount();
    }
}

/**
 * Utility functions
 */
function generateUserId() {
    return 'user_' . time() . '_' . substr(md5(uniqid()), 0, 8);
}

function generateConversationId() {
    return 'conv_' . time() . '_' . substr(md5(uniqid()), 0, 8);
}

function sanitizeInput($input) {
    return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
}

function logActivity($type, $message, $user_id = null) {
    try {
        $db = Database::getInstance();
        $sql = "INSERT INTO system_logs (log_type, message, user_id, ip_address, user_agent) 
                VALUES (?, ?, ?, ?, ?)";
        $params = [
            $type,
            $message,
            $user_id,
            $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
        ];
        $db->execute($sql, $params);
    } catch (Exception $e) {
        error_log("Failed to log activity: " . $e->getMessage());
    }
}

function jsonResponse($data, $status = 200) {
    http_response_code($status);
    header('Content-Type: application/json');
    echo json_encode($data, JSON_UNESCAPED_UNICODE);
    exit;
}

// Start session
session_start();

// Set timezone
date_default_timezone_set('Africa/Mogadishu');

/**
 * User management functions
 */
function createUser($user_id, $username = null, $email = null) {
    $db = Database::getInstance();
    $sql = "INSERT INTO users (user_id, username, email) VALUES (?, ?, ?)
            ON DUPLICATE KEY UPDATE last_active = CURRENT_TIMESTAMP";
    return $db->execute($sql, [$user_id, $username, $email]);
}

function getUserStats($user_id) {
    $db = Database::getInstance();
    $sql = "SELECT * FROM user_stats WHERE user_id = ?";
    return $db->fetchRow($sql, [$user_id]);
}

function updateUserActivity($user_id) {
    $db = Database::getInstance();
    $sql = "UPDATE users SET last_active = CURRENT_TIMESTAMP WHERE user_id = ?";
    return $db->execute($sql, [$user_id]);
}

// Initialize database connection
try {
    $db = Database::getInstance();
    logActivity('info', 'Database connection established');
} catch (Exception $e) {
    logActivity('error', 'Database connection failed: ' . $e->getMessage());
    die("System error. Please try again later.");
}
?>
