// Tincada AI - JavaScript Functionality
class TincadaAI {
    constructor() {
        this.apiKey = '********************************************************************************************************************************************************************';
        this.apiUrl = 'https://api.openai.com/v1/chat/completions';
        this.messageCount = 0;
        this.maxMessages = 1000;
        this.chatHistory = [];
        this.currentFiles = [];
        this.isRecording = false;
        this.currentUser = null;

        this.init();
    }

    init() {
        this.checkAuthentication();
        this.bindEvents();
        this.loadChatHistory();
        this.updateMessageCount();
        this.loadUserProfile();
    }

    checkAuthentication() {
        if (!authManager.isAuthenticated() || !authManager.isSessionValid()) {
            // Redirect to login page
            window.location.href = 'login.html';
            return;
        }

        // Load current user data
        this.currentUser = authManager.getCurrentUser();

        // Refresh session
        authManager.refreshSession();
    }

    loadUserProfile() {
        if (!this.currentUser) return;

        // Update user name in header
        const userNameElement = document.getElementById('userName');
        if (userNameElement) {
            userNameElement.textContent = this.currentUser.fullName;
        }

        // Update user avatar in header
        const userAvatarElement = document.getElementById('userAvatar');
        if (userAvatarElement && this.currentUser.profileImage) {
            userAvatarElement.innerHTML = `<img src="${this.currentUser.profileImage}" alt="${this.currentUser.fullName}">`;
        }

        // Update welcome message to include user name
        this.updateWelcomeMessage();
    }

    updateWelcomeMessage() {
        const welcomeContent = document.querySelector('.welcome-content h2');
        if (welcomeContent && this.currentUser) {
            welcomeContent.textContent = `Soo dhawoow ${this.currentUser.fullName.split(' ')[0]}!`;
        }
    }

    bindEvents() {
        // Navigation buttons
        document.getElementById('newChatBtn').addEventListener('click', () => this.startNewChat());
        document.getElementById('historyBtn').addEventListener('click', () => this.toggleHistory());
        document.getElementById('logoutBtn').addEventListener('click', () => this.logout());
        document.getElementById('closeSidebarBtn').addEventListener('click', () => this.toggleHistory());
        
        // Chat functionality
        document.getElementById('sendBtn').addEventListener('click', () => this.sendMessage());
        document.getElementById('clearChatBtn').addEventListener('click', () => this.clearChat());
        
        // Input handling
        const messageInput = document.getElementById('messageInput');
        messageInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                this.sendMessage();
            }
        });
        
        messageInput.addEventListener('input', () => this.autoResizeTextarea());
        
        // File upload
        document.getElementById('fileUploadBtn').addEventListener('click', () => {
            document.getElementById('fileInput').click();
        });
        
        document.getElementById('fileInput').addEventListener('change', (e) => {
            this.handleFileUpload(e.target.files);
        });
        
        // Voice recording
        document.getElementById('voiceBtn').addEventListener('click', () => this.toggleVoiceRecording());
    }

    startNewChat() {
        document.getElementById('welcomeSection').style.display = 'none';
        document.getElementById('chatContainer').style.display = 'block';
        document.getElementById('messageInput').focus();
        
        // Add welcome message from AI
        this.addMessage('ai', 'Soo dhawoow! Waxaan ahay Tincada AI. Sidee kuu caawin karaa maanta?');
    }

    async sendMessage() {
        const messageInput = document.getElementById('messageInput');
        const message = messageInput.value.trim();
        
        if (!message && this.currentFiles.length === 0) return;
        if (this.messageCount >= this.maxMessages) {
            this.showToast('Waxaad gaartay xadka fariimaha maalinlaha ah (1000)', 'warning');
            return;
        }

        // Add user message
        this.addMessage('user', message, this.currentFiles);
        messageInput.value = '';
        this.currentFiles = [];
        this.hideFilePreview();
        this.autoResizeTextarea();
        
        // Show loading
        this.showLoading(true);
        
        try {
            // Prepare messages for API
            const messages = [
                {
                    role: 'system',
                    content: 'Waxaad tahay Tincada AI, kaaliye AI ah oo af Soomaali ku hadla. Waxaad bixisaa jawaabo faa\'iido leh, qurux badan, oo af Soomaali nadiif ah. Marwalba ku jawaab af Soomaali, haddii aan si gaar ah loogu sheegin luqad kale.'
                },
                ...this.chatHistory.slice(-10).map(msg => ({
                    role: msg.type === 'user' ? 'user' : 'assistant',
                    content: msg.text
                })),
                {
                    role: 'user',
                    content: message || 'Fadlan eeg faylalka aan soo diray'
                }
            ];

            const response = await fetch(this.apiUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${this.apiKey}`
                },
                body: JSON.stringify({
                    model: 'gpt-4o-mini',
                    messages: messages,
                    max_tokens: 1000,
                    temperature: 0.7
                })
            });

            if (!response.ok) {
                throw new Error(`API Error: ${response.status}`);
            }

            const data = await response.json();
            const aiResponse = data.choices[0].message.content;
            
            // Add AI response
            this.addMessage('ai', aiResponse);
            
        } catch (error) {
            console.error('Error:', error);
            this.addMessage('ai', 'Waan ka xumahay, khalad ayaa dhacay. Fadlan mar kale isku day.');
            this.showToast('Khalad ayaa dhacay API-ga', 'error');
        } finally {
            this.showLoading(false);
        }
    }

    addMessage(type, text, files = []) {
        const chatMessages = document.getElementById('chatMessages');
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${type}`;

        const avatar = document.createElement('div');
        avatar.className = 'message-avatar';

        if (type === 'user' && this.currentUser && this.currentUser.profileImage) {
            // Use user's profile image
            avatar.innerHTML = `<img src="${this.currentUser.profileImage}" alt="${this.currentUser.fullName}" style="width: 100%; height: 100%; border-radius: 50%; object-fit: cover;">`;
        } else if (type === 'user') {
            // Fallback to user icon
            avatar.innerHTML = '<i class="fas fa-user"></i>';
        } else {
            // AI avatar
            avatar.innerHTML = '<i class="fas fa-robot"></i>';
        }

        const content = document.createElement('div');
        content.className = 'message-content';
        
        let messageHTML = `<div class="message-text">${this.formatMessage(text)}</div>`;
        
        // Add file previews if any
        if (files && files.length > 0) {
            messageHTML += '<div class="message-files">';
            files.forEach(file => {
                messageHTML += `<div class="file-item">
                    <i class="fas fa-file"></i>
                    <span>${file.name}</span>
                </div>`;
            });
            messageHTML += '</div>';
        }
        
        messageHTML += `<div class="message-time">${this.getCurrentTime()}</div>`;
        content.innerHTML = messageHTML;
        
        messageDiv.appendChild(avatar);
        messageDiv.appendChild(content);
        chatMessages.appendChild(messageDiv);
        
        // Scroll to bottom
        chatMessages.scrollTop = chatMessages.scrollHeight;
        
        // Update chat history
        this.chatHistory.push({
            type,
            text,
            files: files || [],
            timestamp: Date.now()
        });
        
        this.messageCount++;
        this.updateMessageCount();
        this.saveChatHistory();
    }

    formatMessage(text) {
        // Enhanced text formatting with code card support
        let formattedText = text;

        // Process code blocks first (```language ... ```)
        formattedText = formattedText.replace(/```(\w+)?\n?([\s\S]*?)```/g, (match, language, code) => {
            const lang = language || 'text';
            const cleanCode = code.trim();
            const codeId = 'code_' + Math.random().toString(36).substr(2, 9);

            const cardHtml = `<div class="code-card" data-language="${lang}">
                <div class="code-header">
                    <div class="code-language">
                        <i class="fas fa-code"></i>
                        <span>${lang.toUpperCase()}</span>
                    </div>
                    <div class="code-actions">
                        <button class="code-btn copy-btn" onclick="tincadaAI.copyCode('${codeId}')" title="Copy code">
                            <i class="fas fa-copy"></i>
                            Copy
                        </button>
                        <button class="code-btn edit-btn" onclick="tincadaAI.editCode('${codeId}')" title="Edit code">
                            <i class="fas fa-edit"></i>
                            Edit
                        </button>
                    </div>
                </div>
                <div class="code-content">
                    <pre id="${codeId}"><code class="language-${lang}">${this.escapeHtml(cleanCode)}</code></pre>
                </div>
            </div>`;

            // Apply syntax highlighting after DOM insertion
            setTimeout(() => {
                const codeElement = document.getElementById(codeId);
                if (codeElement && window.hljs) {
                    hljs.highlightElement(codeElement.querySelector('code'));
                }
            }, 100);

            return cardHtml;
        });

        // Process inline code (`code`)
        formattedText = formattedText.replace(/`([^`]+)`/g, '<code class="inline-code">$1</code>');

        // Process other formatting
        formattedText = formattedText
            .replace(/\n/g, '<br>')
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            .replace(/\*(.*?)\*/g, '<em>$1</em>')
            .replace(/__(.*?)__/g, '<u>$1</u>')
            .replace(/~~(.*?)~~/g, '<del>$1</del>');

        return formattedText;
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    copyCode(codeId) {
        const codeElement = document.getElementById(codeId);
        const code = codeElement.textContent;

        navigator.clipboard.writeText(code).then(() => {
            this.showToast('Code copied to clipboard!', 'success');

            // Visual feedback
            const copyBtn = codeElement.closest('.code-card').querySelector('.copy-btn');
            const originalText = copyBtn.innerHTML;
            copyBtn.innerHTML = '<i class="fas fa-check"></i> Copied!';
            copyBtn.style.background = '#28a745';

            setTimeout(() => {
                copyBtn.innerHTML = originalText;
                copyBtn.style.background = '';
            }, 2000);
        }).catch(() => {
            this.showToast('Failed to copy code', 'error');
        });
    }

    editCode(codeId) {
        const codeElement = document.getElementById(codeId);
        const codeCard = codeElement.closest('.code-card');
        const language = codeCard.dataset.language;
        const currentCode = codeElement.textContent;

        // Create edit modal
        const modal = document.createElement('div');
        modal.className = 'code-edit-modal';
        modal.innerHTML = `
            <div class="modal-content">
                <div class="modal-header">
                    <h3><i class="fas fa-edit"></i> Edit ${language.toUpperCase()} Code</h3>
                    <button class="close-btn" onclick="this.closest('.code-edit-modal').remove()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <textarea id="editCodeArea" class="code-editor">${currentCode}</textarea>
                </div>
                <div class="modal-footer">
                    <button class="btn-secondary" onclick="this.closest('.code-edit-modal').remove()">Cancel</button>
                    <button class="btn-primary" onclick="tincadaAI.saveEditedCode('${codeId}', this.closest('.code-edit-modal'))">Save Changes</button>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
        document.getElementById('editCodeArea').focus();
    }

    saveEditedCode(codeId, modal) {
        const newCode = modal.querySelector('#editCodeArea').value;
        const codeElement = document.getElementById(codeId);

        codeElement.textContent = newCode;
        modal.remove();

        this.showToast('Code updated successfully!', 'success');
    }

    getCurrentTime() {
        const now = new Date();
        return now.toLocaleTimeString('so-SO', { 
            hour: '2-digit', 
            minute: '2-digit' 
        });
    }

    handleFileUpload(files) {
        if (files.length === 0) return;
        
        this.currentFiles = Array.from(files);
        this.showFilePreview();
    }

    showFilePreview() {
        const preview = document.getElementById('filePreview');
        preview.innerHTML = '';
        
        this.currentFiles.forEach((file, index) => {
            const fileDiv = document.createElement('div');
            fileDiv.className = 'file-preview-item';
            fileDiv.innerHTML = `
                <div class="file-info">
                    <i class="fas fa-file"></i>
                    <span>${file.name}</span>
                    <small>(${this.formatFileSize(file.size)})</small>
                </div>
                <button class="remove-file-btn" onclick="tincadaAI.removeFile(${index})">
                    <i class="fas fa-times"></i>
                </button>
            `;
            preview.appendChild(fileDiv);
        });
        
        preview.style.display = 'block';
    }

    hideFilePreview() {
        document.getElementById('filePreview').style.display = 'none';
    }

    removeFile(index) {
        this.currentFiles.splice(index, 1);
        if (this.currentFiles.length === 0) {
            this.hideFilePreview();
        } else {
            this.showFilePreview();
        }
    }

    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    toggleVoiceRecording() {
        const voiceBtn = document.getElementById('voiceBtn');
        
        if (!this.isRecording) {
            // Start recording
            if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
                navigator.mediaDevices.getUserMedia({ audio: true })
                    .then(stream => {
                        this.isRecording = true;
                        voiceBtn.innerHTML = '<i class="fas fa-stop"></i>';
                        voiceBtn.style.background = 'linear-gradient(45deg, #ff6b6b, #ee5a52)';
                        this.showToast('Codka ayaa la duubayaa...', 'success');
                        
                        // Here you would implement actual voice recording
                        // For now, we'll just simulate it
                        setTimeout(() => {
                            this.stopVoiceRecording();
                        }, 5000);
                    })
                    .catch(err => {
                        console.error('Voice recording error:', err);
                        this.showToast('Lama heli karo microphone-ka', 'error');
                    });
            } else {
                this.showToast('Browser-kaagu ma taageero voice recording', 'error');
            }
        } else {
            this.stopVoiceRecording();
        }
    }

    stopVoiceRecording() {
        this.isRecording = false;
        const voiceBtn = document.getElementById('voiceBtn');
        voiceBtn.innerHTML = '<i class="fas fa-microphone"></i>';
        voiceBtn.style.background = 'none';
        this.showToast('Duubista codka waa la joojiyay', 'success');
    }

    autoResizeTextarea() {
        const textarea = document.getElementById('messageInput');
        textarea.style.height = 'auto';
        textarea.style.height = Math.min(textarea.scrollHeight, 120) + 'px';
    }

    clearChat() {
        if (confirm('Ma hubtaa inaad rabto inaad nadiifiso sheekada?')) {
            document.getElementById('chatMessages').innerHTML = '';
            this.chatHistory = [];
            this.messageCount = 0;
            this.updateMessageCount();
            this.saveChatHistory();
            this.addMessage('ai', 'Sheekada waa la nadiifiyay. Sidee kuu caawin karaa?');
        }
    }

    updateMessageCount() {
        document.getElementById('messageCount').textContent = `${this.messageCount}/${this.maxMessages} fariin`;
    }

    toggleHistory() {
        const sidebar = document.getElementById('historySidebar');
        sidebar.classList.toggle('open');
        
        if (sidebar.classList.contains('open')) {
            this.loadHistoryContent();
        }
    }

    loadHistoryContent() {
        const historyContent = document.getElementById('historyContent');
        
        if (this.chatHistory.length === 0) {
            historyContent.innerHTML = '<p style="color: rgba(255,255,255,0.7); text-align: center; padding: 2rem;">Wali ma jiraan sheekooyin la kaydiyay</p>';
            return;
        }
        
        historyContent.innerHTML = '<div class="history-list"></div>';
        const historyList = historyContent.querySelector('.history-list');
        
        // Group messages by date
        const groupedHistory = this.groupHistoryByDate();
        
        Object.keys(groupedHistory).forEach(date => {
            const dateDiv = document.createElement('div');
            dateDiv.className = 'history-date';
            dateDiv.innerHTML = `<h4>${date}</h4>`;
            historyList.appendChild(dateDiv);
            
            groupedHistory[date].forEach(msg => {
                const msgDiv = document.createElement('div');
                msgDiv.className = `history-message ${msg.type}`;
                msgDiv.innerHTML = `
                    <div class="history-text">${msg.text.substring(0, 100)}${msg.text.length > 100 ? '...' : ''}</div>
                    <div class="history-time">${new Date(msg.timestamp).toLocaleTimeString('so-SO')}</div>
                `;
                historyList.appendChild(msgDiv);
            });
        });
    }

    groupHistoryByDate() {
        const grouped = {};
        
        this.chatHistory.forEach(msg => {
            const date = new Date(msg.timestamp).toLocaleDateString('so-SO');
            if (!grouped[date]) {
                grouped[date] = [];
            }
            grouped[date].push(msg);
        });
        
        return grouped;
    }

    saveChatHistory() {
        localStorage.setItem('tincadaAI_chatHistory', JSON.stringify(this.chatHistory));
        localStorage.setItem('tincadaAI_messageCount', this.messageCount.toString());
    }

    loadChatHistory() {
        const savedHistory = localStorage.getItem('tincadaAI_chatHistory');
        const savedCount = localStorage.getItem('tincadaAI_messageCount');
        
        if (savedHistory) {
            this.chatHistory = JSON.parse(savedHistory);
        }
        
        if (savedCount) {
            this.messageCount = parseInt(savedCount);
        }
    }

    showLoading(show) {
        const overlay = document.getElementById('loadingOverlay');
        overlay.style.display = show ? 'flex' : 'none';
    }

    showToast(message, type = 'success') {
        const container = document.getElementById('toastContainer');
        const toast = document.createElement('div');
        toast.className = `toast ${type}`;
        toast.innerHTML = `
            <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'exclamation-triangle'}"></i>
            <span>${message}</span>
        `;
        
        container.appendChild(toast);
        
        setTimeout(() => {
            toast.remove();
        }, 5000);
    }

    logout() {
        if (confirm('Ma hubtaa inaad rabto inaad ka baxdo?')) {
            // Logout using AuthManager
            authManager.logout();

            // Show logout animation
            document.body.style.opacity = '0';
            document.body.style.transform = 'scale(0.9)';

            setTimeout(() => {
                // Redirect to login page
                window.location.href = 'login.html';
            }, 500);
        }
    }
}

// Initialize Tincada AI when page loads
let tincadaAI;
document.addEventListener('DOMContentLoaded', () => {
    tincadaAI = new TincadaAI();
});
