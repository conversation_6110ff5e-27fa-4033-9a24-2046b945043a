#!/usr/bin/env python3
"""
Final test to verify Tincada AI works like ChatGPT
"""

import requests
import json

def test_ai():
    """Test AI responses"""
    
    base_url = "http://localhost:5001"
    
    tests = [
        "What is artificial intelligence?",
        "Maxaa ka dhigan AI?", 
        "Write a Python function to calculate fibonacci numbers",
        "Hello, how are you?"
    ]
    
    print("🧪 Testing Tincada AI...")
    print("=" * 50)
    
    for i, question in enumerate(tests, 1):
        print(f"\n📝 Test {i}: {question}")
        print("-" * 30)
        
        try:
            response = requests.post(f"{base_url}/api/chat", json={
                "message": question,
                "user_id": "test_user",
                "chat_history": []
            })
            
            if response.status_code == 200:
                data = response.json()
                ai_response = data.get('response', 'No response')
                print(f"✅ AI: {ai_response[:200]}...")
            else:
                print(f"❌ Error: {response.status_code}")
                
        except Exception as e:
            print(f"❌ Request failed: {e}")

if __name__ == "__main__":
    test_ai()
