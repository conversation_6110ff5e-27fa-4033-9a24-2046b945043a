<!DOCTYPE html>
<html lang="so">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Code Block Demo - Tincada AI</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism-tomorrow.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
            padding: 2rem;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        h1 {
            text-align: center;
            font-size: 2.5rem;
            margin-bottom: 2rem;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .demo-section {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 2rem;
            margin-bottom: 2rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .demo-section h2 {
            color: #4ecdc4;
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        /* AI Code Block Styles */
        .ai-code-block {
            background: #1e1e1e;
            border-radius: 12px;
            margin: 1.5rem 0;
            overflow: hidden;
            border: 1px solid #333;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
            position: relative;
        }

        .code-header {
            background: #2d2d2d;
            padding: 12px 16px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid #333;
        }

        .code-language {
            color: #9cdcfe;
            font-size: 0.9rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .code-actions {
            display: flex;
            gap: 8px;
        }

        .code-btn {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: #fff;
            padding: 6px 12px;
            border-radius: 6px;
            font-size: 0.8rem;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .code-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-1px);
        }

        .code-content {
            padding: 0;
            background: #1e1e1e;
            overflow-x: auto;
        }

        .code-content pre {
            margin: 0;
            padding: 16px;
            background: transparent;
            font-family: 'Fira Code', 'Consolas', 'Monaco', monospace;
            font-size: 14px;
            line-height: 1.5;
            color: #d4d4d4;
        }

        /* Custom syntax highlighting */
        .token.keyword {
            color: #569cd6;
            font-weight: bold;
        }

        .token.string {
            color: #ce9178;
        }

        .token.function {
            color: #dcdcaa;
        }

        .token.number {
            color: #b5cea8;
        }

        .token.operator {
            color: #d4d4d4;
        }

        .token.punctuation {
            color: #d4d4d4;
        }

        .token.comment {
            color: #6a9955;
            font-style: italic;
        }

        .token.property {
            color: #9cdcfe;
        }

        .token.selector {
            color: #d7ba7d;
        }

        /* Line numbers */
        .line-numbers .line-numbers-rows {
            border-right: 1px solid #333;
            padding-right: 8px;
            margin-right: 8px;
        }

        .line-numbers-rows > span:before {
            color: #858585;
            font-size: 12px;
        }

        /* Copy success animation */
        .copy-success {
            background: #4caf50 !important;
            color: white !important;
        }

        .ai-response {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            padding: 1.5rem;
            margin: 1rem 0;
            border-left: 4px solid #4ecdc4;
        }

        .ai-response h4 {
            color: #4ecdc4;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .generate-btn {
            background: linear-gradient(45deg, #4ecdc4, #44a08d);
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 25px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 10px;
            margin: 1rem auto;
        }

        .generate-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(78, 205, 196, 0.4);
        }

        @media (max-width: 768px) {
            body { padding: 1rem; }
            .demo-section { padding: 1rem; }
            h1 { font-size: 2rem; }
            .code-content pre { font-size: 12px; }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🤖 AI Code Block Demo</h1>
        
        <div class="demo-section">
            <h2><i class="fas fa-code"></i> JavaScript Code Example</h2>
            
            <div class="ai-response">
                <h4><i class="fas fa-robot"></i> AI Response:</h4>
                <p>Halkan waa JavaScript code oo check gareynaya access token:</p>
            </div>
            
            <div class="ai-code-block">
                <div class="code-header">
                    <span class="code-language">JavaScript</span>
                    <div class="code-actions">
                        <button class="code-btn" onclick="copyCode(this, 'js-code')">
                            <i class="fas fa-copy"></i> Copy
                        </button>
                        <button class="code-btn" onclick="editCode(this)">
                            <i class="fas fa-edit"></i> Edit
                        </button>
                    </div>
                </div>
                <div class="code-content">
                    <pre id="js-code"><code class="language-javascript">const params = new URLSearchParams(window.location.hash);

if (params.get("access_token")) {
    window.location.href = "/dashboard.html";
}

// Store user authentication data
const userData = {
    token: params.get("access_token"),
    user: params.get("user_id"),
    timestamp: new Date().toISOString()
};

localStorage.setItem('auth_data', JSON.stringify(userData));</code></pre>
                </div>
            </div>
        </div>
        
        <div class="demo-section">
            <h2><i class="fas fa-palette"></i> CSS Code Example</h2>
            
            <div class="ai-response">
                <h4><i class="fas fa-robot"></i> AI Response:</h4>
                <p>Halkan waa CSS styles oo sameeynaya modern button:</p>
            </div>
            
            <div class="ai-code-block">
                <div class="code-header">
                    <span class="code-language">CSS</span>
                    <div class="code-actions">
                        <button class="code-btn" onclick="copyCode(this, 'css-code')">
                            <i class="fas fa-copy"></i> Copy
                        </button>
                        <button class="code-btn" onclick="editCode(this)">
                            <i class="fas fa-edit"></i> Edit
                        </button>
                    </div>
                </div>
                <div class="code-content">
                    <pre id="css-code"><code class="language-css">.modern-button {
    background: linear-gradient(45deg, #4ecdc4, #44a08d);
    color: white;
    padding: 12px 24px;
    border: none;
    border-radius: 25px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(78, 205, 196, 0.3);
}

.modern-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(78, 205, 196, 0.4);
}

.modern-button:active {
    transform: translateY(0);
}</code></pre>
                </div>
            </div>
        </div>
        
        <div class="demo-section">
            <h2><i class="fas fa-code"></i> HTML Code Example</h2>
            
            <div class="ai-response">
                <h4><i class="fas fa-robot"></i> AI Response:</h4>
                <p>Halkan waa HTML structure oo sameeynaya login form:</p>
            </div>
            
            <div class="ai-code-block">
                <div class="code-header">
                    <span class="code-language">HTML</span>
                    <div class="code-actions">
                        <button class="code-btn" onclick="copyCode(this, 'html-code')">
                            <i class="fas fa-copy"></i> Copy
                        </button>
                        <button class="code-btn" onclick="editCode(this)">
                            <i class="fas fa-edit"></i> Edit
                        </button>
                    </div>
                </div>
                <div class="code-content">
                    <pre id="html-code"><code class="language-html">&lt;div class="login-container"&gt;
    &lt;h2&gt;🔐 Ku Soo Gal&lt;/h2&gt;
    
    &lt;form class="login-form"&gt;
        &lt;div class="input-group"&gt;
            &lt;input type="email" placeholder="Email" required&gt;
            &lt;i class="fas fa-envelope"&gt;&lt;/i&gt;
        &lt;/div&gt;
        
        &lt;div class="input-group"&gt;
            &lt;input type="password" placeholder="Password" required&gt;
            &lt;i class="fas fa-lock"&gt;&lt;/i&gt;
        &lt;/div&gt;
        
        &lt;button type="submit" class="login-btn"&gt;
            Soo Gal
        &lt;/button&gt;
    &lt;/form&gt;
&lt;/div&gt;</code></pre>
                </div>
            </div>
        </div>
        
        <div class="demo-section">
            <h2><i class="fas fa-magic"></i> Generate New Code</h2>
            <button class="generate-btn" onclick="generateNewCode()">
                <i class="fas fa-robot"></i> Generate AI Code Example
            </button>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-core.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/autoloader/prism-autoloader.min.js"></script>
    
    <script>
        // Copy code functionality
        function copyCode(button, codeId) {
            const codeElement = document.getElementById(codeId);
            const code = codeElement.textContent;
            
            navigator.clipboard.writeText(code).then(() => {
                const originalText = button.innerHTML;
                button.innerHTML = '<i class="fas fa-check"></i> Copied!';
                button.classList.add('copy-success');
                
                setTimeout(() => {
                    button.innerHTML = originalText;
                    button.classList.remove('copy-success');
                }, 2000);
            }).catch(err => {
                console.error('Failed to copy code:', err);
                alert('Failed to copy code to clipboard');
            });
        }

        // Edit code functionality
        function editCode(button) {
            alert('Edit functionality would open code editor');
        }

        // Generate new code examples
        function generateNewCode() {
            const examples = [
                {
                    language: 'Python',
                    description: 'Python function oo calculate gareynaya fibonacci numbers:',
                    code: `def fibonacci(n):
    """Calculate fibonacci sequence up to n terms"""
    if n <= 0:
        return []
    elif n == 1:
        return [0]
    elif n == 2:
        return [0, 1]
    
    sequence = [0, 1]
    for i in range(2, n):
        sequence.append(sequence[i-1] + sequence[i-2])
    
    return sequence

# Example usage
result = fibonacci(10)
print(f"First 10 fibonacci numbers: {result}")`
                },
                {
                    language: 'React',
                    description: 'React component oo sameeynaya user profile card:',
                    code: `import React, { useState } from 'react';

const UserProfile = ({ user }) => {
    const [isFollowing, setIsFollowing] = useState(false);
    
    const handleFollow = () => {
        setIsFollowing(!isFollowing);
    };
    
    return (
        <div className="user-profile-card">
            <img 
                src={user.avatar} 
                alt={user.name}
                className="profile-avatar"
            />
            <h3>{user.name}</h3>
            <p className="user-bio">{user.bio}</p>
            
            <button 
                onClick={handleFollow}
                className={\`follow-btn \${isFollowing ? 'following' : ''}\`}
            >
                {isFollowing ? 'Following' : 'Follow'}
            </button>
        </div>
    );
};

export default UserProfile;`
                },
                {
                    language: 'SQL',
                    description: 'SQL query oo soo bandhigaya user statistics:',
                    code: `-- Get user statistics with post counts and engagement
SELECT 
    u.id,
    u.username,
    u.email,
    COUNT(p.id) as total_posts,
    AVG(p.likes) as avg_likes,
    MAX(p.created_at) as last_post_date,
    CASE 
        WHEN COUNT(p.id) > 10 THEN 'Active'
        WHEN COUNT(p.id) > 5 THEN 'Moderate'
        ELSE 'New'
    END as user_status
FROM users u
LEFT JOIN posts p ON u.id = p.user_id
WHERE u.created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
GROUP BY u.id, u.username, u.email
ORDER BY total_posts DESC, avg_likes DESC
LIMIT 20;`
                }
            ];
            
            const randomExample = examples[Math.floor(Math.random() * examples.length)];
            
            // Create new code block
            const newSection = document.createElement('div');
            newSection.className = 'demo-section';
            newSection.innerHTML = `
                <h2><i class="fas fa-code"></i> ${randomExample.language} Code Example</h2>
                
                <div class="ai-response">
                    <h4><i class="fas fa-robot"></i> AI Response:</h4>
                    <p>${randomExample.description}</p>
                </div>
                
                <div class="ai-code-block">
                    <div class="code-header">
                        <span class="code-language">${randomExample.language}</span>
                        <div class="code-actions">
                            <button class="code-btn" onclick="copyCode(this, 'generated-code-${Date.now()}')">
                                <i class="fas fa-copy"></i> Copy
                            </button>
                            <button class="code-btn" onclick="editCode(this)">
                                <i class="fas fa-edit"></i> Edit
                            </button>
                        </div>
                    </div>
                    <div class="code-content">
                        <pre id="generated-code-${Date.now()}"><code class="language-${randomExample.language.toLowerCase()}">${randomExample.code}</code></pre>
                    </div>
                </div>
            `;
            
            // Insert before the generate button section
            const generateSection = document.querySelector('.demo-section:last-child');
            generateSection.parentNode.insertBefore(newSection, generateSection);
            
            // Re-highlight syntax
            Prism.highlightAll();
            
            // Scroll to new section
            newSection.scrollIntoView({ behavior: 'smooth' });
        }

        // Initialize syntax highlighting
        document.addEventListener('DOMContentLoaded', function() {
            Prism.highlightAll();
        });
    </script>
</body>
</html>
