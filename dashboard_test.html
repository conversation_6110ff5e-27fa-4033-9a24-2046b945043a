<!DOCTYPE html>
<html lang="so">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard Code Cards Test</title>
    <link rel="stylesheet" href="dashboard.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Fira+Code:wght@300;400;500;600&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/styles/monokai.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/highlight.min.js"></script>
</head>
<body>
    <div style="padding: 20px; background: #212121; color: white; min-height: 100vh;">
        <h1>🧪 Dashboard Code Cards Test</h1>
        <p>Testing if code cards display properly in dashboard</p>
        
        <div id="testArea" style="margin: 20px 0;">
            <h3>Test Code Card:</h3>
            <div id="messageContainer"></div>
        </div>
        
        <button onclick="testCodeCard()" style="background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;">
            Test Code Card
        </button>
        
        <button onclick="testAPICall()" style="background: #28a745; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; margin-left: 10px;">
            Test API Call
        </button>
    </div>

    <script src="dashboard.js"></script>
    <script>
        // Create dashboard instance
        const tincadaAI = new TincadaDashboard();
        
        function testCodeCard() {
            const testMessage = `Here's a simple HTML button example:

\`\`\`html
<button type="button">Click Me!</button>
\`\`\`

And some CSS:

\`\`\`css
.button {
    background: #007bff;
    color: white;
    padding: 10px 20px;
    border: none;
    border-radius: 5px;
}
\`\`\``;

            console.log('🧪 Testing code card with message:', testMessage);
            
            // Format the message using dashboard's formatAIMessage
            const formattedMessage = tincadaAI.formatAIMessage(testMessage);
            
            console.log('🎨 Formatted message:', formattedMessage);
            
            // Display in test area
            const container = document.getElementById('messageContainer');
            container.innerHTML = formattedMessage;
            
            // Apply syntax highlighting
            setTimeout(() => {
                document.querySelectorAll('pre code').forEach((block) => {
                    if (window.hljs) {
                        hljs.highlightElement(block);
                    }
                });
            }, 100);
        }
        
        async function testAPICall() {
            console.log('🚀 Testing API call...');
            
            try {
                const response = await fetch('/api/chat', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        message: 'write html button code',
                        user_id: 'dashboard_test_user'
                    })
                });
                
                const data = await response.json();
                console.log('📊 API Response:', data);
                
                const aiResponse = data.response || (data.choices && data.choices[0].message.content);
                
                if (aiResponse) {
                    console.log('🎯 AI Response:', aiResponse);
                    
                    // Format and display
                    const formattedMessage = tincadaAI.formatAIMessage(aiResponse);
                    const container = document.getElementById('messageContainer');
                    container.innerHTML = formattedMessage;
                    
                    // Apply syntax highlighting
                    setTimeout(() => {
                        document.querySelectorAll('pre code').forEach((block) => {
                            if (window.hljs) {
                                hljs.highlightElement(block);
                            }
                        });
                    }, 100);
                } else {
                    console.error('❌ No AI response found');
                }
                
            } catch (error) {
                console.error('❌ API call failed:', error);
            }
        }
        
        // Auto-test on page load
        setTimeout(() => {
            console.log('🔄 Auto-testing code card...');
            testCodeCard();
        }, 1000);
    </script>
</body>
</html>
