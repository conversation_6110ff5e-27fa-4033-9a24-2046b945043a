<!DOCTYPE html>
<html lang="so">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Fix Demo - Tincada AI</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
            padding: 2rem;
            margin: 0;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 3rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        h1 {
            text-align: center;
            font-size: 2.5rem;
            margin-bottom: 1rem;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .subtitle {
            text-align: center;
            font-size: 1.2rem;
            margin-bottom: 3rem;
            opacity: 0.9;
        }
        
        .fix-section {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .fix-section h2 {
            color: #4ecdc4;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .feature-list {
            list-style: none;
            padding: 0;
        }
        
        .feature-list li {
            padding: 0.8rem 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .feature-list li:last-child {
            border-bottom: none;
        }
        
        .feature-icon {
            width: 40px;
            height: 40px;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
        }
        
        .code-block {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 10px;
            padding: 1.5rem;
            margin: 1rem 0;
            font-family: 'Courier New', monospace;
            border-left: 4px solid #4ecdc4;
            overflow-x: auto;
            font-size: 0.9rem;
        }
        
        .success {
            background: rgba(76, 175, 80, 0.2);
            border: 1px solid rgba(76, 175, 80, 0.3);
            border-radius: 10px;
            padding: 1rem;
            margin: 1rem 0;
            border-left: 4px solid #4caf50;
        }
        
        .warning {
            background: rgba(255, 167, 38, 0.2);
            border: 1px solid rgba(255, 167, 38, 0.3);
            border-radius: 10px;
            padding: 1rem;
            margin: 1rem 0;
            border-left: 4px solid #ffa726;
        }
        
        .demo-buttons {
            display: flex;
            gap: 20px;
            justify-content: center;
            margin-top: 3rem;
            flex-wrap: wrap;
        }
        
        .demo-btn {
            background: linear-gradient(45deg, #4ecdc4, #44a08d);
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 25px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 10px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(78, 205, 196, 0.3);
        }
        
        .demo-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(78, 205, 196, 0.4);
        }
        
        .demo-btn.secondary {
            background: linear-gradient(45deg, #ff6b6b, #ee5a52);
            box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
        }
        
        .demo-btn.secondary:hover {
            box-shadow: 0 6px 20px rgba(255, 107, 107, 0.4);
        }
        
        @media (max-width: 768px) {
            body {
                padding: 1rem;
            }
            
            .container {
                padding: 2rem 1rem;
            }
            
            h1 {
                font-size: 2rem;
            }
            
            .demo-buttons {
                flex-direction: column;
                align-items: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 API Fix - Tincada AI</h1>
        <p class="subtitle">API khaladka waa la xaliyay! Mock API iyo Real API labadaba waa shaqaynayaan</p>
        
        <div class="fix-section">
            <h2>✅ Waxaan Xaliyay</h2>
            <ul class="feature-list">
                <li>
                    <div class="feature-icon">🤖</div>
                    <div>
                        <strong>Mock API System</strong><br>
                        <small>Haddii API key-gu ma shaqaynin, mock responses ayaa la isticmaali doonaa</small>
                    </div>
                </li>
                <li>
                    <div class="feature-icon">⚙️</div>
                    <div>
                        <strong>API Settings Modal</strong><br>
                        <small>Settings-ka waxaad ku beddeli kartaa API key-ga iyo mock mode</small>
                    </div>
                </li>
                <li>
                    <div class="feature-icon">🧪</div>
                    <div>
                        <strong>API Key Testing</strong><br>
                        <small>Test API key button si aad u hubiso in uu shaqeeyo</small>
                    </div>
                </li>
                <li>
                    <div class="feature-icon">⚠️</div>
                    <div>
                        <strong>Status Banner</strong><br>
                        <small>Banner oo ku sheegaya haddii mock API la isticmaalayo</small>
                    </div>
                </li>
                <li>
                    <div class="feature-icon">🔄</div>
                    <div>
                        <strong>Automatic Fallback</strong><br>
                        <small>Haddii API fashilmo, automatic mock mode ayaa la daahfuraa</small>
                    </div>
                </li>
            </ul>
        </div>
        
        <div class="fix-section">
            <h2>🚀 Sidee loo isticmaalo</h2>
            
            <h3>1. Mock API Mode (Demo)</h3>
            <div class="success">
                <strong>✅ Hadda shaqaynaya!</strong> Mock API waxay bixisaa jawaabo Soomaali ah oo realistic ah.
            </div>
            
            <h3>2. Real API Mode</h3>
            <p>Haddii aad leedahay OpenAI API key shaqeeya:</p>
            <div class="code-block">
1. Riix sawirkaaga (top-right corner)
2. Dooro "Settings"
3. Geli API key-gaaga cusub
4. Riix "Test API Key"
5. Haddii uu shaqeeyo, riix "Save Settings"
            </div>
            
            <h3>3. API Key Format</h3>
            <div class="code-block">
Valid OpenAI API Key:
sk-proj-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx

Alternative format:
sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
            </div>
        </div>
        
        <div class="fix-section">
            <h2>🎯 Mock API Responses</h2>
            <p>Mock API waxay ku jawaabaysaa af Soomaali oo realistic ah:</p>
            
            <div class="code-block">
Su'aal: "Salam, sidee tahay?"
Jawaab: "Salam alaykum! Waan ku soo dhaweynayaa Tincada AI. Sidee kuu caawin karaa maanta?"

Su'aal: "Maxaad tahay?"
Jawaab: "Waxaan ahay Tincada AI, chatbot casri ah oo af Soomaali ku hadla..."

Su'aal: "Caawimaad"
Jawaab: "Dabcan! Waxaan kaa caawin karaa: Su'aalo guud, Qorista, Tarjumaad..."
            </div>
        </div>
        
        <div class="fix-section">
            <h2>🔧 Troubleshooting</h2>
            
            <div class="warning">
                <strong>⚠️ Haddii wali khalad jiro:</strong>
                <ul style="margin-top: 10px; padding-left: 20px;">
                    <li>Check browser console (F12)</li>
                    <li>Try different browser</li>
                    <li>Clear browser cache</li>
                    <li>Restart server (python server.py)</li>
                </ul>
            </div>
        </div>
        
        <div class="demo-buttons">
            <a href="dashboard.html" class="demo-btn">
                🚀 Bilow Dashboard
            </a>
            <a href="api-test.html" class="demo-btn secondary">
                🧪 Test API Keys
            </a>
            <a href="troubleshooting.md" class="demo-btn secondary">
                📖 Troubleshooting Guide
            </a>
        </div>
    </div>
</body>
</html>
