<!DOCTYPE html>
<html lang="so">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Replicate API Setup Guide - Tincada AI</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
            padding: 2rem;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 3rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        h1 {
            text-align: center;
            font-size: 2.5rem;
            margin-bottom: 1rem;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .subtitle {
            text-align: center;
            font-size: 1.2rem;
            margin-bottom: 3rem;
            opacity: 0.9;
        }

        .status-section {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            border-left: 4px solid #4ecdc4;
        }

        .status-section h2 {
            color: #4ecdc4;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .api-status {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            margin: 2rem 0;
        }

        .status-card {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 1.5rem;
            text-align: center;
        }

        .status-card.working {
            border-color: rgba(76, 175, 80, 0.5);
            background: rgba(76, 175, 80, 0.1);
        }

        .status-card.error {
            border-color: rgba(244, 67, 54, 0.5);
            background: rgba(244, 67, 54, 0.1);
        }

        .status-card.warning {
            border-color: rgba(255, 193, 7, 0.5);
            background: rgba(255, 193, 7, 0.1);
        }

        .status-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
        }

        .status-card h3 {
            margin-bottom: 0.5rem;
        }

        .status-card p {
            opacity: 0.8;
            line-height: 1.5;
        }

        .setup-steps {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            padding: 2rem;
            margin: 2rem 0;
        }

        .step {
            display: flex;
            align-items: flex-start;
            gap: 1rem;
            margin-bottom: 2rem;
            padding: 1.5rem;
            background: rgba(0, 0, 0, 0.2);
            border-radius: 10px;
            border-left: 4px solid #4ecdc4;
        }

        .step-number {
            width: 40px;
            height: 40px;
            background: linear-gradient(45deg, #4ecdc4, #44a08d);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            flex-shrink: 0;
        }

        .step-content h4 {
            color: #4ecdc4;
            margin-bottom: 0.5rem;
        }

        .step-content p {
            line-height: 1.6;
            margin-bottom: 1rem;
        }

        .code-block {
            background: rgba(0, 0, 0, 0.5);
            border-radius: 8px;
            padding: 1rem;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            color: #a8e6cf;
            margin: 0.5rem 0;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .success-box {
            background: rgba(76, 175, 80, 0.1);
            border: 1px solid rgba(76, 175, 80, 0.3);
            border-radius: 10px;
            padding: 1.5rem;
            margin: 2rem 0;
            border-left: 4px solid #4caf50;
        }

        .success-box h3 {
            color: #4caf50;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .warning-box {
            background: rgba(255, 193, 7, 0.1);
            border: 1px solid rgba(255, 193, 7, 0.3);
            border-radius: 10px;
            padding: 1.5rem;
            margin: 2rem 0;
            border-left: 4px solid #ffc107;
        }

        .warning-box h3 {
            color: #ffc107;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .demo-buttons {
            display: flex;
            gap: 20px;
            justify-content: center;
            margin-top: 3rem;
            flex-wrap: wrap;
        }

        .demo-btn {
            background: linear-gradient(45deg, #4ecdc4, #44a08d);
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 25px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 10px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(78, 205, 196, 0.3);
        }

        .demo-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(78, 205, 196, 0.4);
        }

        .demo-btn.secondary {
            background: linear-gradient(45deg, #ff6b6b, #ee5a52);
            box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
        }

        .demo-btn.secondary:hover {
            box-shadow: 0 6px 20px rgba(255, 107, 107, 0.4);
        }

        @media (max-width: 768px) {
            body { padding: 1rem; }
            .container { padding: 2rem 1rem; }
            h1 { font-size: 2rem; }
            .api-status { grid-template-columns: 1fr; }
            .demo-buttons { flex-direction: column; align-items: center; }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Replicate API Setup</h1>
        <p class="subtitle">Configure Replicate API for AI image and video generation</p>
        
        <div class="status-section">
            <h2>📊 Current Replicate Status</h2>
            <div class="api-status">
                <div class="status-card working">
                    <div class="status-icon">✅</div>
                    <h3>API Connection</h3>
                    <p>Connected with token<br>
                    <code>r8_HM5U7xvVYnZrIDz2S...</code></p>
                </div>
                
                <div class="status-card error">
                    <div class="status-icon">💳</div>
                    <h3>Credits Status</h3>
                    <p>Insufficient credits<br>
                    Need to add billing</p>
                </div>
                
                <div class="status-card warning">
                    <div class="status-icon">🎨</div>
                    <h3>Image Generation</h3>
                    <p>Ready but needs credits<br>
                    SDXL model configured</p>
                </div>
                
                <div class="status-card warning">
                    <div class="status-icon">🎬</div>
                    <h3>Video Generation</h3>
                    <p>Model needs permission<br>
                    Alternative models available</p>
                </div>
            </div>
        </div>
        
        <div class="setup-steps">
            <h3 style="color: #4ecdc4; margin-bottom: 2rem;">🚀 How to Setup Replicate API</h3>
            
            <div class="step">
                <div class="step-number">1</div>
                <div class="step-content">
                    <h4>Create Replicate Account</h4>
                    <p>Visit Replicate and create an account or sign in:</p>
                    <div class="code-block">https://replicate.com</div>
                </div>
            </div>
            
            <div class="step">
                <div class="step-number">2</div>
                <div class="step-content">
                    <h4>Get API Token</h4>
                    <p>Go to your account settings and create an API token:</p>
                    <div class="code-block">Account → API Tokens → Create token</div>
                </div>
            </div>
            
            <div class="step">
                <div class="step-number">3</div>
                <div class="step-content">
                    <h4>Add Billing Information</h4>
                    <p>Add payment method and credits for model usage:</p>
                    <div class="code-block">Account → Billing → Add payment method</div>
                    <p><strong>Pricing:</strong></p>
                    <ul style="margin: 1rem 0; padding-left: 2rem; line-height: 1.8;">
                        <li>Image Generation: ~$0.01-0.05 per image</li>
                        <li>Video Generation: ~$0.10-0.50 per video</li>
                        <li>Minimum credit: $10</li>
                    </ul>
                </div>
            </div>
            
            <div class="step">
                <div class="step-number">4</div>
                <div class="step-content">
                    <h4>Update Environment</h4>
                    <p>Your API token is already configured:</p>
                    <div class="code-block">REPLICATE_API_TOKEN=****************************************</div>
                </div>
            </div>
            
            <div class="step">
                <div class="step-number">5</div>
                <div class="step-content">
                    <h4>Test Generation</h4>
                    <p>Once credits are added, test image and video generation:</p>
                    <div class="code-block">python test_replicate.py</div>
                </div>
            </div>
        </div>
        
        <div class="success-box">
            <h3>✅ System Configuration Complete</h3>
            <p><strong>What's Working:</strong></p>
            <ul style="margin: 1rem 0; padding-left: 2rem; line-height: 1.8;">
                <li><strong>✅ API Connection:</strong> Token authenticated successfully</li>
                <li><strong>✅ Image Generation:</strong> SDXL model configured</li>
                <li><strong>✅ Video Generation:</strong> Alternative models ready</li>
                <li><strong>✅ Error Handling:</strong> Proper credit/billing detection</li>
                <li><strong>✅ UI Integration:</strong> Demo pages ready</li>
            </ul>
        </div>
        
        <div class="warning-box">
            <h3>⚠️ Next Steps Required</h3>
            <p><strong>To Enable Full Functionality:</strong></p>
            <ul style="margin: 1rem 0; padding-left: 2rem; line-height: 1.8;">
                <li><strong>💳 Add Credits:</strong> Visit replicate.com/account/billing</li>
                <li><strong>💰 Minimum $10:</strong> Recommended starting amount</li>
                <li><strong>⏱️ Wait Time:</strong> Credits may take a few minutes to activate</li>
                <li><strong>🧪 Test Models:</strong> Try different models if some don't work</li>
            </ul>
        </div>
        
        <div class="status-section">
            <h2>🎯 Current Implementation</h2>
            <div style="background: rgba(255,255,255,0.05); padding: 1.5rem; border-radius: 10px; margin-top: 1rem;">
                <h4 style="color: #4ecdc4; margin-bottom: 1rem;">🎨 Image Generation:</h4>
                <div class="code-block">Model: stability-ai/sdxl
Resolution: 1024x1024, 1792x1024, 1024x1792
Features: Style enhancement, prompt optimization</div>
                
                <h4 style="color: #4ecdc4; margin: 1.5rem 0 1rem;">🎬 Video Generation:</h4>
                <div class="code-block">Model: anotherjesse/zeroscope-v2-xl (alternative)
Features: Image-to-video, motion control, duration settings</div>
                
                <h4 style="color: #4ecdc4; margin: 1.5rem 0 1rem;">🔧 Error Handling:</h4>
                <div class="code-block">✅ Credit detection
✅ Authentication errors
✅ Model permission issues
✅ Rate limit handling</div>
            </div>
        </div>
        
        <div class="demo-buttons">
            <a href="image-generation-demo.html" class="demo-btn">
                🎨 Test Image Generation
            </a>
            <a href="video-generation-demo.html" class="demo-btn">
                🎬 Test Video Generation
            </a>
            <a href="complete-ai-system-demo.html" class="demo-btn secondary">
                🚀 Complete System
            </a>
        </div>
    </div>

    <script>
        // Check Replicate status on page load
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🔧 Replicate Setup Guide loaded');
            
            // Test server connection
            fetch('http://localhost:5001/api/health')
                .then(response => response.json())
                .then(data => {
                    console.log('Server status:', data);
                    updateReplicateStatus(data);
                })
                .catch(error => {
                    console.log('Server not connected:', error);
                });
        });

        function updateReplicateStatus(data) {
            // Update status cards based on server response
            const statusCards = document.querySelectorAll('.status-card');
            
            if (data && data.replicate_available) {
                // Update connection status to working
                statusCards[0].className = 'status-card working';
                statusCards[0].querySelector('h3').textContent = 'API Connection ✅';
            } else {
                // Update connection status to error
                statusCards[0].className = 'status-card error';
                statusCards[0].querySelector('h3').textContent = 'API Connection ❌';
            }
        }
    </script>
</body>
</html>
