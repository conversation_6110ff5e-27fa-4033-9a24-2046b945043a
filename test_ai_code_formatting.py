#!/usr/bin/env python3
"""
🧪 Test AI Code Formatting
Test script to verify that Tincada AI properly formats code responses
"""

import requests
import json
import time

def test_ai_code_response():
    """Test AI response with code formatting"""
    
    print("🧪 Testing AI Code Formatting")
    print("=" * 50)
    
    # Test cases for different programming languages
    test_cases = [
        {
            "name": "HTML Code Request",
            "message": "Create a simple HTML page with a form. Show me the complete code.",
            "expected_language": "html"
        },
        {
            "name": "CSS Styling Request", 
            "message": "Write CSS code for a beautiful button with hover effects.",
            "expected_language": "css"
        },
        {
            "name": "JavaScript Function Request",
            "message": "Write a JavaScript function to validate email addresses.",
            "expected_language": "javascript"
        },
        {
            "name": "Python Class Request",
            "message": "Create a Python class for a simple calculator with basic operations.",
            "expected_language": "python"
        },
        {
            "name": "Multiple Languages Request",
            "message": "Create a complete web component with HTML, CSS, and JavaScript. Show all three files separately.",
            "expected_language": "multiple"
        }
    ]
    
    api_url = "http://localhost:5001/api/chat"
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📝 Test {i}: {test_case['name']}")
        print("-" * 40)
        
        try:
            # Send request to AI
            payload = {
                "message": test_case["message"],
                "user_id": f"test_user_{i}_{int(time.time())}"
            }
            
            print(f"❓ Question: {test_case['message']}")
            print("🚀 Sending request to AI...")
            
            response = requests.post(
                api_url,
                json=payload,
                headers={"Content-Type": "application/json"},
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                
                if 'choices' in data and data['choices']:
                    ai_response = data['choices'][0]['message']['content']
                    source = data.get('source', 'unknown')
                    tokens = data.get('tokens_used', 0)
                    
                    print(f"✅ Response received!")
                    print(f"📊 Source: {source}")
                    print(f"🔢 Tokens: {tokens}")
                    print(f"📏 Length: {len(ai_response)} characters")
                    
                    # Check for code blocks
                    code_blocks = ai_response.count('```')
                    if code_blocks >= 2:  # At least one complete code block
                        print(f"🎯 Code blocks found: {code_blocks // 2}")
                        
                        # Extract languages used
                        import re
                        languages = re.findall(r'```(\w+)', ai_response)
                        if languages:
                            print(f"💻 Languages detected: {', '.join(set(languages))}")
                        else:
                            print("💻 Languages detected: Generic code blocks")
                        
                        print("✅ SUCCESS: AI provided properly formatted code!")
                        
                        # Show preview of response
                        preview = ai_response[:200] + "..." if len(ai_response) > 200 else ai_response
                        print(f"👀 Preview: {preview}")
                        
                    else:
                        print("⚠️ WARNING: No code blocks found in response")
                        print(f"📄 Response: {ai_response[:100]}...")
                        
                else:
                    print("❌ ERROR: Invalid response format")
                    print(f"📄 Response: {data}")
                    
            else:
                print(f"❌ ERROR: HTTP {response.status_code}")
                print(f"📄 Response: {response.text}")
                
        except requests.exceptions.RequestException as e:
            print(f"❌ ERROR: Request failed - {e}")
        except Exception as e:
            print(f"❌ ERROR: Unexpected error - {e}")
        
        # Wait between requests
        if i < len(test_cases):
            print("⏳ Waiting 2 seconds...")
            time.sleep(2)
    
    print("\n" + "=" * 50)
    print("🎉 Code formatting tests completed!")
    print("\n💡 To see the formatted code cards:")
    print("1. Open http://localhost:5001 in your browser")
    print("2. Ask AI for code examples")
    print("3. Code will appear in interactive cards with:")
    print("   - Syntax highlighting")
    print("   - Copy button")
    print("   - Edit button")
    print("   - Language labels")

def test_code_card_features():
    """Test specific code card features"""
    
    print("\n🎨 Testing Code Card Features")
    print("=" * 50)
    
    # Test message with multiple code blocks
    test_message = """Here's a complete web form example:

```html
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Contact Form</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <form id="contactForm" class="contact-form">
        <h2>Contact Us</h2>
        <input type="text" id="name" placeholder="Your Name" required>
        <input type="email" id="email" placeholder="Your Email" required>
        <textarea id="message" placeholder="Your Message" required></textarea>
        <button type="submit">Send Message</button>
    </form>
    <script src="script.js"></script>
</body>
</html>
```

```css
.contact-form {
    max-width: 500px;
    margin: 50px auto;
    padding: 30px;
    background: white;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.contact-form h2 {
    text-align: center;
    color: #333;
    margin-bottom: 20px;
}

.contact-form input,
.contact-form textarea {
    width: 100%;
    padding: 12px;
    margin: 10px 0;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 16px;
}

.contact-form button {
    width: 100%;
    padding: 12px;
    background: #007bff;
    color: white;
    border: none;
    border-radius: 5px;
    font-size: 16px;
    cursor: pointer;
    transition: background 0.3s ease;
}

.contact-form button:hover {
    background: #0056b3;
}
```

```javascript
document.getElementById('contactForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const name = document.getElementById('name').value;
    const email = document.getElementById('email').value;
    const message = document.getElementById('message').value;
    
    // Simple validation
    if (!name || !email || !message) {
        alert('Please fill in all fields');
        return;
    }
    
    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
        alert('Please enter a valid email address');
        return;
    }
    
    // Success message
    alert('Thank you! Your message has been sent.');
    this.reset();
});
```

This creates a complete contact form with validation!"""
    
    print("📝 Testing multi-language code formatting...")
    print("💻 Expected: HTML, CSS, and JavaScript code cards")
    print("🎯 Features to test:")
    print("   - Multiple code blocks in one response")
    print("   - Different language syntax highlighting")
    print("   - Copy/Edit buttons for each block")
    print("   - Proper language labels")
    
    # Count code blocks
    code_blocks = test_message.count('```')
    languages = ['html', 'css', 'javascript']
    
    print(f"✅ Code blocks found: {code_blocks // 2}")
    print(f"✅ Languages included: {', '.join(languages)}")
    print("✅ Multi-language formatting ready!")

if __name__ == "__main__":
    print("🤖 Tincada AI Code Formatting Test")
    print("🎯 Testing AI responses with code cards")
    print("📅 " + time.strftime("%Y-%m-%d %H:%M:%S"))
    print()
    
    # Test AI code responses
    test_ai_code_response()
    
    # Test code card features
    test_code_card_features()
    
    print("\n🎉 All tests completed!")
    print("🌐 Open http://localhost:5001 to see code cards in action!")
