<!DOCTYPE html>
<html lang="so">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>No Auto-Redirect Login Demo - Tincada AI</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
            padding: 2rem;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .container {
            max-width: 500px;
            width: 100%;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 3rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
            text-align: center;
        }

        h1 {
            font-size: 2.5rem;
            margin-bottom: 1rem;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .subtitle {
            margin-bottom: 3rem;
            opacity: 0.9;
            font-size: 1.1rem;
        }

        .login-section {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
        }

        .social-btn {
            width: 100%;
            padding: 15px;
            border-radius: 12px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 12px;
            border: none;
            margin-bottom: 1rem;
        }

        .google-btn {
            background: #ffffff;
            color: #333;
            border: 1px solid rgba(0, 0, 0, 0.1);
        }

        .google-btn:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
        }

        .apple-btn {
            background: #000000;
            color: #ffffff;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .apple-btn:hover:not(:disabled) {
            transform: translateY(-2px);
            background: #1a1a1a;
        }

        .social-btn:disabled {
            opacity: 0.7;
            cursor: not-allowed;
            transform: none !important;
        }

        .google-icon {
            width: 20px;
            height: 20px;
            background: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTIyLjU2IDEyLjI1QzIyLjU2IDExLjQ3IDIyLjQ5IDEwLjcyIDIyLjM2IDEwSDEyVjE0LjI2SDE3LjkyQzE3LjY2IDE1LjYgMTYuOTIgMTYuNzQgMTUuODQgMTcuNVYyMC4yNkgxOS4yOEMyMS4zNiAxOC40MyAyMi41NiAxNS42IDIyLjU2IDEyLjI1WiIgZmlsbD0iIzQyODVGNCIvPgo8cGF0aCBkPSJNMTIgMjNDMTUuMjQgMjMgMTcuOTUgMjEuOTIgMTkuMjggMjAuMjZMMTUuODQgMTcuNUMxNC43OCAxOC4xMyAxMy40NyAxOC41IDEyIDE4LjVDOC44NyAxOC41IDYuMjIgMTYuNjQgNS4yNyAxMy45NEgyLjc2VjE2Ljc0QzQuMDUgMTkuMyA3Ljc5IDIzIDEyIDIzWiIgZmlsbD0iIzM0QTg1MyIvPgo8cGF0aCBkPSJNNS4yNyAxMy45NEM1LjAyIDEzLjMxIDQuODkgMTIuNjYgNC44OSAxMkM0Ljg5IDExLjM0IDUuMDIgMTAuNjkgNS4yNyAxMC4wNlY3LjI2SDIuNzZDMi4xIDguNTkgMS43NSAxMC4yNSAxLjc1IDEyQzEuNzUgMTMuNzUgMi4xIDE1LjQxIDIuNzYgMTYuNzRMNS4yNyAxMy45NFoiIGZpbGw9IiNGQkJDMDQiLz4KPHBhdGggZD0iTTEyIDUuNUMxMy42MiA1LjUgMTUuMDYgNi4wOSAxNi4yIDcuMkwxOS4xOCA0LjIyQzE3Ljk1IDMuMDkgMTUuMjQgMi4yNSAxMiAyLjI1QzcuNzkgMi4yNSA0LjA1IDUuOTUgMi43NiA4LjUxTDUuMjcgMTEuMzFDNi4yMiA4LjYxIDguODcgNi43NSAxMiA2Ljc1WiIgZmlsbD0iI0VBNDMzNSIvPgo8L3N2Zz4K') no-repeat center;
            background-size: contain;
        }

        .status-display {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            text-align: left;
        }

        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.8rem 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .status-item:last-child {
            border-bottom: none;
        }

        .status-label {
            color: #cccccc;
            font-weight: 500;
        }

        .status-value {
            font-family: monospace;
            font-weight: 600;
        }

        .status-success { color: #4caf50; }
        .status-error { color: #f44336; }
        .status-info { color: #2196f3; }

        .navigation-section {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            padding: 2rem;
            margin-top: 2rem;
        }

        .navigation-section h3 {
            color: #4ecdc4;
            margin-bottom: 1rem;
            text-align: center;
        }

        .nav-buttons {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
        }

        .nav-btn {
            background: linear-gradient(45deg, #4ecdc4, #44a08d);
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 25px;
            font-weight: 600;
            cursor: pointer;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
        }

        .nav-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(78, 205, 196, 0.4);
        }

        .message {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 10px;
            color: white;
            font-weight: 500;
            z-index: 10000;
            max-width: 350px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
            transition: all 0.3s ease;
            display: none;
        }

        .message.success { background: linear-gradient(45deg, #4caf50, #45a049); }
        .message.error { background: linear-gradient(45deg, #f44336, #d32f2f); }
        .message.info { background: linear-gradient(45deg, #2196f3, #1976d2); }

        @media (max-width: 768px) {
            body { padding: 1rem; }
            .container { padding: 2rem 1rem; }
            h1 { font-size: 2rem; }
            .nav-buttons { flex-direction: column; }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 No Auto-Redirect Login</h1>
        <p class="subtitle">Login successful - manual navigation required</p>
        
        <div class="login-section">
            <button class="social-btn google-btn" onclick="simulateGoogleLogin()">
                <div class="google-icon"></div>
                <span>Demo Google Sign In</span>
            </button>

            <button class="social-btn apple-btn" onclick="simulateAppleLogin()">
                <i class="fab fa-apple" style="font-size: 1.2rem;"></i>
                <span>Demo Apple Sign In</span>
            </button>
        </div>
        
        <div class="status-display">
            <div class="status-item">
                <span class="status-label">Auto Redirect:</span>
                <span class="status-value status-error">❌ Disabled</span>
            </div>
            <div class="status-item">
                <span class="status-label">Login Status:</span>
                <span class="status-value" id="loginStatus">⏳ Not logged in</span>
            </div>
            <div class="status-item">
                <span class="status-label">User Data:</span>
                <span class="status-value" id="userData">None</span>
            </div>
            <div class="status-item">
                <span class="status-label">Navigation:</span>
                <span class="status-value status-info">📱 Manual</span>
            </div>
        </div>
        
        <div class="navigation-section">
            <h3>🧭 Manual Navigation</h3>
            <div class="nav-buttons">
                <a href="dashboard.html" class="nav-btn">
                    🏠 Dashboard
                </a>
                <a href="login.html" class="nav-btn">
                    🔐 Login Page
                </a>
                <a href="index.html" class="nav-btn">
                    🏡 Home
                </a>
            </div>
        </div>
    </div>

    <div class="message" id="messageDisplay"></div>

    <script>
        // Message display function
        function showMessage(message, type = 'info') {
            const messageElement = document.getElementById('messageDisplay');
            messageElement.textContent = message;
            messageElement.className = `message ${type}`;
            messageElement.style.display = 'block';
            
            setTimeout(() => {
                messageElement.style.display = 'none';
            }, 4000);
        }

        // Update status display
        function updateStatus() {
            const loginStatus = document.getElementById('loginStatus');
            const userData = document.getElementById('userData');
            
            const existingUser = localStorage.getItem('tincada_user');
            if (existingUser) {
                try {
                    const user = JSON.parse(existingUser);
                    loginStatus.textContent = '✅ Logged in';
                    loginStatus.className = 'status-value status-success';
                    userData.textContent = `${user.name} (${user.provider})`;
                    userData.className = 'status-value status-success';
                } catch (error) {
                    loginStatus.textContent = '❌ Error';
                    loginStatus.className = 'status-value status-error';
                    userData.textContent = 'Invalid data';
                    userData.className = 'status-value status-error';
                }
            } else {
                loginStatus.textContent = '⏳ Not logged in';
                loginStatus.className = 'status-value status-info';
                userData.textContent = 'None';
                userData.className = 'status-value';
            }
        }

        // Simulate Google login
        function simulateGoogleLogin() {
            const btn = event.target.closest('.google-btn');
            btn.innerHTML = 'Logging in...';
            btn.disabled = true;
            
            setTimeout(() => {
                // Store demo user data
                const userData = {
                    id: 'google-demo-' + Date.now(),
                    name: 'Ahmed Mohamed',
                    email: '<EMAIL>',
                    picture: 'https://via.placeholder.com/100',
                    provider: 'google',
                    loginTime: new Date().toISOString()
                };
                
                localStorage.setItem('tincada_user', JSON.stringify(userData));
                
                showMessage('✅ Google Sign In successful - No auto redirect!', 'success');
                updateStatus();
                
                // Reset button
                btn.innerHTML = '<div class="google-icon"></div><span>Demo Google Sign In</span>';
                btn.disabled = false;
            }, 2000);
        }

        // Simulate Apple login
        function simulateAppleLogin() {
            const btn = event.target.closest('.apple-btn');
            btn.innerHTML = 'Logging in...';
            btn.disabled = true;
            
            setTimeout(() => {
                // Store demo user data
                const userData = {
                    id: 'apple-demo-' + Date.now(),
                    name: 'Fatima Ali',
                    email: '<EMAIL>',
                    provider: 'apple',
                    loginTime: new Date().toISOString()
                };
                
                localStorage.setItem('tincada_user', JSON.stringify(userData));
                
                showMessage('✅ Apple Sign In successful - No auto redirect!', 'success');
                updateStatus();
                
                // Reset button
                btn.innerHTML = '<i class="fab fa-apple" style="font-size: 1.2rem;"></i><span>Demo Apple Sign In</span>';
                btn.disabled = false;
            }, 2000);
        }

        // Clear login data
        function clearLogin() {
            localStorage.removeItem('tincada_user');
            showMessage('🗑️ Login data cleared', 'info');
            updateStatus();
        }

        // Initialize
        window.addEventListener('load', function() {
            console.log('🔐 No Auto-Redirect Login Demo loaded');
            updateStatus();
            showMessage('Demo: Login without auto-redirect', 'info');
            
            // Add clear button
            const navButtons = document.querySelector('.nav-buttons');
            const clearBtn = document.createElement('button');
            clearBtn.className = 'nav-btn';
            clearBtn.style.background = 'linear-gradient(45deg, #f44336, #d32f2f)';
            clearBtn.innerHTML = '🗑️ Clear Login';
            clearBtn.onclick = clearLogin;
            navButtons.appendChild(clearBtn);
        });
    </script>
</body>
</html>
