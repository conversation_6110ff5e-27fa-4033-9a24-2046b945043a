#!/usr/bin/env python3
"""
Your Original Code - Fixed Version
Ku saabsan koodh<PERSON> asalka ah oo la hagaajiyay
"""

from openai import OpenAI
import base64

# API Key (ka soo qaad koodhkaaga)
client = OpenAI(
    api_key="********************************************************************************************************************************************************************"
)

def generate_single_image():
    """
    Your original code - fixed version
    """
    
    print("🎨 Your Original Code - Fixed")
    print("=" * 40)
    
    try:
        # Fixed version of your code
        response = client.images.generate(
            model="dall-e-3",  # Correct model name
            prompt="A gray tabby cat hugging an otter with an orange scarf",  # Your prompt
            n=1,
            size="1024x1024",
            response_format="b64_json"  # Get base64 like you wanted
        )

        # Extract base64 data (fixed)
        image_b64 = response.data[0].b64_json

        # Save image exactly like your code
        with open("otter.png", "wb") as f:
            f.write(base64.b64decode(image_b64))

        print("✅ Sawirka waa la keydiyay: otter.png")
        return True
        
    except Exception as e:
        print(f"❌ Khalad: {e}")
        return False

def generate_realistic_version():
    """
    Your follow-up realistic version
    """
    
    print("\n🎨 Realistic Version")
    print("-" * 30)
    
    try:
        # Realistic version (new prompt instead of follow-up)
        response = client.images.generate(
            model="dall-e-3",
            prompt="A photorealistic gray tabby cat hugging an otter with an orange scarf, professional photography, detailed fur texture, natural lighting, high quality",
            n=1,
            size="1024x1024",
            response_format="b64_json"
        )

        image_b64 = response.data[0].b64_json

        with open("cat_and_otter_realistic.png", "wb") as f:
            f.write(base64.b64decode(image_b64))

        print("✅ Realistic sawirka waa la keydiyay: cat_and_otter_realistic.png")
        return True
        
    except Exception as e:
        print(f"❌ Realistic version khalad: {e}")
        return False

def test_permissions():
    """
    Test what permissions we have
    """
    
    print("🔍 Testing API Permissions...")
    
    try:
        # Test basic API access
        models = client.models.list()
        print("✅ Basic API access: OK")
        
        # Test image generation permission
        try:
            response = client.images.generate(
                model="dall-e-3",
                prompt="A simple test image",
                n=1,
                size="1024x1024",
                response_format="url"  # Try URL first (less data)
            )
            print("✅ Image generation permission: OK")
            return True
            
        except Exception as img_error:
            print(f"❌ Image generation permission: {img_error}")
            return False
            
    except Exception as e:
        print(f"❌ Basic API access: {e}")
        return False

def main():
    """
    Main function - test your code
    """
    
    print("🧪 Testing Your Fixed Code")
    print("=" * 50)
    
    # Test permissions first
    if not test_permissions():
        print("\n❌ API permissions issue. Cannot generate images.")
        print("\nPossible solutions:")
        print("1. Check if API key has image generation permissions")
        print("2. Make sure you have billing set up")
        print("3. Check if you're in the correct organization/project")
        return
    
    # Test your original code
    print("\n" + "=" * 50)
    success1 = generate_single_image()
    
    # Test realistic version if first succeeds
    if success1:
        success2 = generate_realistic_version()
        
        print("\n" + "=" * 50)
        print("📊 Results:")
        print(f"✅ Original image: {'SUCCESS' if success1 else 'FAILED'}")
        print(f"✅ Realistic image: {'SUCCESS' if success2 else 'FAILED'}")
        
        if success1 or success2:
            print("\n🎉 Your code is working!")
            print("📁 Check the PNG files in this folder:")
            if success1:
                print("   - otter.png")
            if success2:
                print("   - cat_and_otter_realistic.png")
        
    else:
        print("\n❌ Image generation failed.")
        print("Check API key permissions and billing.")

if __name__ == "__main__":
    main()
