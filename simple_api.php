<?php
/**
 * 🚀 TINCADA AI SIMPLE API
 * Lightweight API without database requirements
 * Fallback system for when database is not available
 */

// Enable CORS
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');
header('Content-Type: application/json');

// Handle preflight OPTIONS requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Get request method and path
$method = $_SERVER['REQUEST_METHOD'];
$path = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
$path = str_replace('/simple_api.php', '', $path);

// Get input data
$input = null;
if ($method === 'POST' || $method === 'PUT') {
    $input = json_decode(file_get_contents('php://input'), true);
}

/**
 * Detect language from text
 */
function detectLanguage($text) {
    $text = strtolower($text);
    
    // Somali keywords
    if (preg_match('/\b(waa|maxaa|sidee|halkan|waxaan|tahay|ahay|salaan|mahadsanid|fiican|caawin)\b/', $text)) {
        return 'so';
    }
    
    // Arabic keywords
    if (preg_match('/[\x{0600}-\x{06FF}]/u', $text)) {
        return 'ar';
    }
    
    // French keywords
    if (preg_match('/\b(bonjour|comment|merci|oui|non|je|tu|il|elle|nous|vous)\b/', $text)) {
        return 'fr';
    }
    
    // Spanish keywords
    if (preg_match('/\b(hola|como|gracias|si|no|yo|tu|el|ella|nosotros)\b/', $text)) {
        return 'es';
    }
    
    // German keywords
    if (preg_match('/\b(hallo|wie|danke|ja|nein|ich|du|er|sie|wir)\b/', $text)) {
        return 'de';
    }
    
    // Default to English
    return 'en';
}

/**
 * Generate AI response with OpenAI API
 */
function generateAIResponse($message, $language = 'auto') {
    // Detect language if auto
    if ($language === 'auto') {
        $language = detectLanguage($message);
    }
    
    // System messages for different languages
    $systemMessages = [
        'so' => 'Waxaad tahay Tincada AI, kaaliye AI ah oo ku hadla af Soomaali. Waxaad si fiican ugu jawaabaysaa su\'aalaha, waxaadna bixisaa macluumaad saxan oo faa\'iido leh. Haddii lagaa weydiiyo code, waxaad bixisaa code nadiif ah oo la sharaxo.',
        'en' => 'You are Tincada AI, a helpful AI assistant. You provide accurate, helpful information and can generate clean, well-commented code when requested. Always format code in proper code blocks.',
        'ar' => 'أنت تينكادا AI، مساعد ذكي مفيد. تقدم معلومات دقيقة ومفيدة ويمكنك إنشاء كود نظيف ومعلق عليه عند الطلب.',
        'fr' => 'Vous êtes Tincada AI, un assistant IA utile. Vous fournissez des informations précises et utiles et pouvez générer du code propre et bien commenté sur demande.',
        'es' => 'Eres Tincada AI, un asistente de IA útil. Proporcionas información precisa y útil y puedes generar código limpio y bien comentado cuando se solicite.',
        'de' => 'Sie sind Tincada AI, ein hilfreicher KI-Assistent. Sie liefern genaue, hilfreiche Informationen und können sauberen, gut kommentierten Code generieren, wenn Sie darum gebeten werden.'
    ];
    
    $systemMessage = $systemMessages[$language] ?? $systemMessages['en'];
    
    // Try OpenAI API first
    $openai_api_key = '********************************************************************************************************************************************************************';
    
    if (!empty($openai_api_key) && $openai_api_key !== 'your-api-key-here') {
        try {
            $data = [
                'model' => 'gpt-4o-mini',
                'messages' => [
                    ['role' => 'system', 'content' => $systemMessage],
                    ['role' => 'user', 'content' => $message]
                ],
                'max_tokens' => 1500,
                'temperature' => 0.7
            ];
            
            $ch = curl_init('https://api.openai.com/v1/chat/completions');
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Content-Type: application/json',
                'Authorization: Bearer ' . $openai_api_key
            ]);
            curl_setopt($ch, CURLOPT_TIMEOUT, 30);
            
            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);
            
            if ($httpCode === 200 && $response) {
                $result = json_decode($response, true);
                if (isset($result['choices'][0]['message']['content'])) {
                    return [
                        'response' => $result['choices'][0]['message']['content'],
                        'tokens_used' => $result['usage']['total_tokens'] ?? 0,
                        'source' => 'openai',
                        'language' => $language,
                        'success' => true
                    ];
                }
            }
        } catch (Exception $e) {
            error_log("OpenAI API Error: " . $e->getMessage());
        }
    }
    
    // Enhanced fallback responses with code examples
    $fallbackResponses = [
        'so' => [
            'greeting' => 'Salaan! Waxaan ahay Tincada AI. Sidee kaa caawin karaa maanta?',
            'code_html' => 'Waxaan kuu sameyn karaa HTML code. Halkan waa tusaale:\n\n```html\n<!DOCTYPE html>\n<html lang="so">\n<head>\n    <meta charset="UTF-8">\n    <title>Tincada AI</title>\n</head>\n<body>\n    <div class="container">\n        <h1>Salaan!</h1>\n        <p>Ku soo dhawoow Tincada AI</p>\n        <button onclick="showMessage()">Riix</button>\n    </div>\n</body>\n</html>\n```',
            'code_css' => 'Waxaan kuu sameyn karaa CSS code. Halkan waa tusaale:\n\n```css\n.container {\n    max-width: 800px;\n    margin: 0 auto;\n    padding: 20px;\n    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n    border-radius: 15px;\n    color: white;\n    text-align: center;\n}\n\nbutton {\n    background: #4CAF50;\n    color: white;\n    padding: 12px 24px;\n    border: none;\n    border-radius: 8px;\n    cursor: pointer;\n    font-size: 16px;\n}\n\nbutton:hover {\n    background: #45a049;\n    transform: scale(1.05);\n}\n```',
            'code_js' => 'Waxaan kuu sameyn karaa JavaScript code. Halkan waa tusaale:\n\n```javascript\nfunction showMessage() {\n    alert("Salaan! Tincada AI ayaa ku caawinaya!");\n}\n\n// Event listener\ndocument.addEventListener("DOMContentLoaded", function() {\n    console.log("Tincada AI waa diyaar!");\n    \n    // Add click event to all buttons\n    const buttons = document.querySelectorAll("button");\n    buttons.forEach(button => {\n        button.addEventListener("click", showMessage);\n    });\n});\n```',
            'help' => 'Waan ku caawin karaa! Su\'aal kasta weydii. Waxaan kaa caawin karaa:\n• HTML, CSS, JavaScript code\n• Su\'aalaha guud\n• Turjumaad\n• Macluumaad',
            'default' => 'Waan fahmay su\'aashaada. Maxaad kale rabaa in aan ku caawiyo? Waxaan kaa caawin karaa code generation, su\'aalaha, iyo wax kasta oo kale.'
        ],
        'en' => [
            'greeting' => 'Hello! I am Tincada AI. How can I help you today?',
            'code_html' => 'I can generate HTML code for you. Here\'s an example:\n\n```html\n<!DOCTYPE html>\n<html lang="en">\n<head>\n    <meta charset="UTF-8">\n    <meta name="viewport" content="width=device-width, initial-scale=1.0">\n    <title>Tincada AI Demo</title>\n</head>\n<body>\n    <div class="container">\n        <h1>Welcome to Tincada AI</h1>\n        <p>Your intelligent coding assistant</p>\n        <button class="btn-primary" onclick="handleClick()">Get Started</button>\n    </div>\n</body>\n</html>\n```',
            'code_css' => 'I can create CSS styles for you. Here\'s an example:\n\n```css\n.container {\n    max-width: 1200px;\n    margin: 0 auto;\n    padding: 40px 20px;\n    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n    border-radius: 20px;\n    box-shadow: 0 10px 30px rgba(0,0,0,0.2);\n    text-align: center;\n    color: white;\n}\n\n.btn-primary {\n    background: #4CAF50;\n    color: white;\n    padding: 15px 30px;\n    border: none;\n    border-radius: 10px;\n    cursor: pointer;\n    font-size: 18px;\n    transition: all 0.3s ease;\n}\n\n.btn-primary:hover {\n    background: #45a049;\n    transform: translateY(-2px);\n    box-shadow: 0 5px 15px rgba(0,0,0,0.3);\n}\n```',
            'code_js' => 'I can write JavaScript code for you. Here\'s an example:\n\n```javascript\nfunction handleClick() {\n    alert("Welcome to Tincada AI!");\n}\n\n// Modern JavaScript with async/await\nasync function fetchData() {\n    try {\n        const response = await fetch("/api/data");\n        const data = await response.json();\n        console.log("Data received:", data);\n        return data;\n    } catch (error) {\n        console.error("Error fetching data:", error);\n    }\n}\n\n// DOM manipulation\ndocument.addEventListener("DOMContentLoaded", function() {\n    const buttons = document.querySelectorAll(".btn-primary");\n    buttons.forEach(button => {\n        button.addEventListener("click", handleClick);\n    });\n});\n```',
            'help' => 'I\'m here to help! Ask me anything. I can assist with:\n• HTML, CSS, JavaScript code generation\n• General questions and explanations\n• Code debugging and optimization\n• Best practices and tutorials',
            'default' => 'I understand your question. What else can I help you with? I can generate code, answer questions, and provide detailed explanations on various topics.'
        ],
        'ar' => [
            'greeting' => 'مرحبا! أنا تينكادا AI. كيف يمكنني مساعدتك اليوم؟',
            'code_html' => 'يمكنني إنشاء كود HTML لك. إليك مثال:\n\n```html\n<!DOCTYPE html>\n<html lang="ar" dir="rtl">\n<head>\n    <meta charset="UTF-8">\n    <title>تينكادا AI</title>\n</head>\n<body>\n    <div class="container">\n        <h1>مرحبا بك في تينكادا AI</h1>\n        <p>مساعدك الذكي في البرمجة</p>\n        <button onclick="showAlert()">ابدأ الآن</button>\n    </div>\n</body>\n</html>\n```',
            'help' => 'أنا هنا للمساعدة! اسأل أي شيء. يمكنني المساعدة في:\n• إنشاء كود HTML, CSS, JavaScript\n• الأسئلة العامة والشروحات\n• حل مشاكل الكود\n• أفضل الممارسات',
            'default' => 'أفهم سؤالك. بماذا يمكنني المساعدة أيضا؟ يمكنني إنشاء الكود وإجابة الأسئلة وتقديم شروحات مفصلة.'
        ]
    ];
    
    $langResponses = $fallbackResponses[$language] ?? $fallbackResponses['en'];
    
    // Determine response type based on message content
    $message_lower = strtolower($message);
    
    if (strpos($message_lower, 'hello') !== false || strpos($message_lower, 'salaan') !== false || strpos($message_lower, 'مرحبا') !== false || strpos($message_lower, 'bonjour') !== false) {
        $response = $langResponses['greeting'];
    } elseif (strpos($message_lower, 'html') !== false) {
        $response = $langResponses['code_html'] ?? $langResponses['default'];
    } elseif (strpos($message_lower, 'css') !== false) {
        $response = $langResponses['code_css'] ?? $langResponses['default'];
    } elseif (strpos($message_lower, 'javascript') !== false || strpos($message_lower, 'js') !== false) {
        $response = $langResponses['code_js'] ?? $langResponses['default'];
    } elseif (strpos($message_lower, 'help') !== false || strpos($message_lower, 'caawin') !== false || strpos($message_lower, 'مساعدة') !== false) {
        $response = $langResponses['help'];
    } else {
        $response = $langResponses['default'];
    }
    
    return [
        'response' => $response,
        'tokens_used' => ceil(strlen($response) / 4), // Rough estimate
        'source' => 'fallback',
        'language' => $language,
        'success' => true
    ];
}

/**
 * Send JSON response
 */
function sendJsonResponse($data, $status = 200) {
    http_response_code($status);
    echo json_encode($data);
    exit;
}

/**
 * Send error response
 */
function sendError($message, $status = 400) {
    sendJsonResponse(['error' => $message, 'status' => $status, 'success' => false], $status);
}

// Route handling
switch ($path) {
    case '/chat':
        if ($method === 'POST') {
            if (!$input || !isset($input['message'])) {
                sendError('Message is required');
            }
            
            $message = trim($input['message']);
            $language = $input['language'] ?? 'auto';
            $session_id = $input['session_id'] ?? 'session_' . time();
            
            if (empty($message)) {
                sendError('Message cannot be empty');
            }
            
            // Generate AI response
            $aiResult = generateAIResponse($message, $language);
            
            sendJsonResponse([
                'success' => true,
                'response' => $aiResult['response'],
                'session_id' => $session_id,
                'language' => $aiResult['language'],
                'tokens_used' => $aiResult['tokens_used'],
                'source' => $aiResult['source'],
                'timestamp' => date('Y-m-d H:i:s')
            ]);
        }
        break;
    
    case '/health':
        sendJsonResponse([
            'success' => true,
            'status' => 'healthy',
            'message' => 'Tincada AI Simple API is working!',
            'features' => [
                'multilingual_support' => true,
                'code_generation' => true,
                'openai_integration' => true,
                'fallback_responses' => true
            ],
            'supported_languages' => ['so', 'en', 'ar', 'fr', 'es', 'de'],
            'timestamp' => date('Y-m-d H:i:s'),
            'version' => '2.0.0'
        ]);
        break;
    
    case '/languages':
        sendJsonResponse([
            'success' => true,
            'supported_languages' => [
                'so' => 'Somali',
                'en' => 'English', 
                'ar' => 'Arabic',
                'fr' => 'French',
                'es' => 'Spanish',
                'de' => 'German'
            ]
        ]);
        break;
    
    default:
        sendError('Endpoint not found. Available endpoints: /chat, /health, /languages', 404);
}
?>
