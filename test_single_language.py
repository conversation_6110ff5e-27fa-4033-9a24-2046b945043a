import requests
import json

def test_single_language():
    """Test one language at a time"""
    
    languages = [
        {"name": "Spanish", "message": "¡Hola! ¿Cómo estás? ¿Qué puedes hacer?"},
        {"name": "French", "message": "Bonjour! Comment allez-vous?"},
        {"name": "German", "message": "<PERSON>o! Wie geht es dir?"},
        {"name": "Italian", "message": "Ciao! Come stai?"}
    ]
    
    for lang in languages:
        print(f"\n🌍 Testing {lang['name']}")
        print(f"📤 Message: {lang['message']}")
        
        try:
            data = {
                "message": lang['message'],
                "user_id": "test_user",
                "chat_history": []
            }
            
            response = requests.post(
                'http://localhost:5001/api/chat',
                json=data,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ Success!")
                print(f"📊 Source: {result['source']}")
                print(f"🔢 Tokens: {result['tokens_used']}")
                print(f"📝 Response: {result['response'][:150]}...")
            else:
                print(f"❌ Failed: {response.status_code}")
                
        except Exception as e:
            print(f"❌ Error: {e}")
        
        print("-" * 40)

if __name__ == "__main__":
    test_single_language()
