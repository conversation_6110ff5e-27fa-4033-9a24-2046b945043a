<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎯 Final Code Cards Demo - Tincada AI</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .header h1 {
            color: #333;
            margin-bottom: 10px;
            font-size: 2.5em;
        }
        
        .demo-section {
            margin-bottom: 40px;
            padding: 25px;
            background: #f8f9fa;
            border-radius: 12px;
            border-left: 5px solid #007bff;
        }
        
        .demo-section h2 {
            color: #007bff;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .ask-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 10px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            margin: 10px 5px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }
        
        .ask-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.3);
        }
        
        .response-area {
            margin-top: 20px;
            padding: 20px;
            background: white;
            border-radius: 10px;
            border: 2px solid #e9ecef;
            min-height: 100px;
        }
        
        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
            font-size: 18px;
        }
        
        .loading i {
            animation: spin 1s linear infinite;
            margin-right: 10px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .success-msg {
            background: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            border: 1px solid #c3e6cb;
        }
        
        .error-msg {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            border: 1px solid #f5c6cb;
        }
        
        .features-list {
            background: #e3f2fd;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
        
        .features-list h3 {
            color: #1976d2;
            margin-bottom: 15px;
        }
        
        .features-list ul {
            list-style: none;
            padding: 0;
        }
        
        .features-list li {
            padding: 8px 0;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .features-list li::before {
            content: "✅";
            font-size: 16px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 Code Cards Demo</h1>
            <p>Tincada AI with Interactive Code Formatting</p>
        </div>
        
        <div class="features-list">
            <h3>🚀 New Code Card Features:</h3>
            <ul>
                <li>Interactive code cards with syntax highlighting</li>
                <li>Copy button for easy code copying</li>
                <li>Edit button for code modification</li>
                <li>Language detection and labeling</li>
                <li>Professional dark theme design</li>
                <li>Support for HTML, CSS, JavaScript, Python, and more</li>
            </ul>
        </div>
        
        <div class="demo-section">
            <h2><i class="fas fa-code"></i> HTML Code Example</h2>
            <p>Ask AI to create HTML code and see it in a beautiful code card</p>
            <button class="ask-btn" onclick="askForHtml()">
                <i class="fas fa-file-code"></i> Ask for HTML Code
            </button>
            <div id="htmlResponse" class="response-area"></div>
        </div>
        
        <div class="demo-section">
            <h2><i class="fas fa-paint-brush"></i> CSS Styling Example</h2>
            <p>Request CSS code and see professional styling cards</p>
            <button class="ask-btn" onclick="askForCss()">
                <i class="fas fa-palette"></i> Ask for CSS Code
            </button>
            <div id="cssResponse" class="response-area"></div>
        </div>
        
        <div class="demo-section">
            <h2><i class="fas fa-bolt"></i> JavaScript Function Example</h2>
            <p>Get JavaScript code in interactive cards with copy functionality</p>
            <button class="ask-btn" onclick="askForJs()">
                <i class="fab fa-js-square"></i> Ask for JavaScript Code
            </button>
            <div id="jsResponse" class="response-area"></div>
        </div>
        
        <div class="demo-section">
            <h2><i class="fas fa-layer-group"></i> Complete Web Component</h2>
            <p>Request a full web component with HTML, CSS, and JavaScript</p>
            <button class="ask-btn" onclick="askForComplete()">
                <i class="fas fa-cubes"></i> Ask for Complete Component
            </button>
            <div id="completeResponse" class="response-area"></div>
        </div>
        
        <div class="demo-section">
            <h2><i class="fas fa-external-link-alt"></i> Try Live Chat</h2>
            <p>Experience code cards in the main Tincada AI interface</p>
            <button class="ask-btn" onclick="openMainChat()">
                <i class="fas fa-comments"></i> Open Main Chat
            </button>
        </div>
    </div>

    <script>
        // Mock TincadaAI formatting function
        function formatMessage(text) {
            let formattedText = text;
            
            // Process code blocks first (```language ... ```)
            formattedText = formattedText.replace(/```(\w+)?\n?([\s\S]*?)```/g, (match, language, code) => {
                const lang = language || 'text';
                const cleanCode = code.trim();
                const codeId = 'code_' + Math.random().toString(36).substr(2, 9);
                
                const cardHtml = `<div class="code-card" data-language="${lang}">
                    <div class="code-header">
                        <div class="code-language">
                            <i class="fas fa-code"></i>
                            <span>${lang.toUpperCase()}</span>
                        </div>
                        <div class="code-actions">
                            <button class="code-btn copy-btn" onclick="copyCode('${codeId}')" title="Copy code">
                                <i class="fas fa-copy"></i>
                                Copy
                            </button>
                            <button class="code-btn edit-btn" onclick="editCode('${codeId}')" title="Edit code">
                                <i class="fas fa-edit"></i>
                                Edit
                            </button>
                        </div>
                    </div>
                    <div class="code-content">
                        <pre id="${codeId}"><code class="language-${lang}">${escapeHtml(cleanCode)}</code></pre>
                    </div>
                </div>`;
                
                return cardHtml;
            });
            
            // Process inline code (`code`)
            formattedText = formattedText.replace(/`([^`]+)`/g, '<code class="inline-code">$1</code>');
            
            // Process other formatting
            formattedText = formattedText
                .replace(/\n/g, '<br>')
                .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
                .replace(/\*(.*?)\*/g, '<em>$1</em>');
            
            return formattedText;
        }
        
        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }
        
        function copyCode(codeId) {
            const codeElement = document.getElementById(codeId);
            const code = codeElement.textContent;
            
            navigator.clipboard.writeText(code).then(() => {
                showMessage('Code copied to clipboard!', 'success');
                
                // Visual feedback
                const copyBtn = codeElement.closest('.code-card').querySelector('.copy-btn');
                const originalText = copyBtn.innerHTML;
                copyBtn.innerHTML = '<i class="fas fa-check"></i> Copied!';
                copyBtn.style.background = '#28a745';
                
                setTimeout(() => {
                    copyBtn.innerHTML = originalText;
                    copyBtn.style.background = '';
                }, 2000);
            }).catch(() => {
                showMessage('Failed to copy code', 'error');
            });
        }
        
        function editCode(codeId) {
            showMessage('Edit functionality - would open modal editor', 'success');
        }
        
        function showMessage(message, type) {
            const msgDiv = document.createElement('div');
            msgDiv.className = type === 'success' ? 'success-msg' : 'error-msg';
            msgDiv.textContent = message;
            document.body.appendChild(msgDiv);
            
            setTimeout(() => {
                msgDiv.remove();
            }, 3000);
        }
        
        async function askAI(question, responseElementId) {
            const responseDiv = document.getElementById(responseElementId);
            responseDiv.innerHTML = '<div class="loading"><i class="fas fa-spinner"></i>Getting AI response...</div>';
            
            try {
                const response = await fetch('http://localhost:5001/api/chat', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        message: question,
                        user_id: 'demo_user_' + Date.now()
                    })
                });
                
                const data = await response.json();
                
                if (data.response) {
                    const formattedResponse = formatMessage(data.response);
                    responseDiv.innerHTML = formattedResponse;
                    showMessage('AI response with code cards generated!', 'success');
                } else {
                    responseDiv.innerHTML = '<div class="error-msg">❌ Failed to get AI response</div>';
                }
                
            } catch (error) {
                console.error('Error:', error);
                responseDiv.innerHTML = '<div class="error-msg">❌ Error connecting to AI API</div>';
            }
        }
        
        function askForHtml() {
            askAI('Create a simple HTML page with a contact form. Include proper structure and form elements.', 'htmlResponse');
        }
        
        function askForCss() {
            askAI('Write CSS code for a beautiful gradient button with hover effects and animations.', 'cssResponse');
        }
        
        function askForJs() {
            askAI('Create a JavaScript function to validate form inputs with error handling.', 'jsResponse');
        }
        
        function askForComplete() {
            askAI('Create a complete interactive card component with HTML structure, CSS styling, and JavaScript functionality. Show all three files separately.', 'completeResponse');
        }
        
        function openMainChat() {
            window.open('http://localhost:5001', '_blank');
        }
    </script>
    
    <!-- Include the code card styles -->
    <style>
        /* Code Card Styles */
        .code-card {
            background: #1e1e1e;
            border-radius: 12px;
            margin: 15px 0;
            overflow: hidden;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
            border: 1px solid #333;
            font-family: 'Fira Code', 'Monaco', 'Consolas', monospace;
        }

        .code-header {
            background: linear-gradient(135deg, #2d2d2d 0%, #1a1a1a 100%);
            padding: 12px 16px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid #333;
        }

        .code-language {
            display: flex;
            align-items: center;
            gap: 8px;
            color: #61dafb;
            font-weight: 600;
            font-size: 14px;
            text-transform: uppercase;
        }

        .code-language i {
            font-size: 16px;
        }

        .code-actions {
            display: flex;
            gap: 8px;
        }

        .code-btn {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: #fff;
            padding: 6px 12px;
            border-radius: 6px;
            font-size: 12px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 4px;
            transition: all 0.3s ease;
        }

        .code-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-1px);
        }

        .copy-btn:hover {
            background: #28a745;
        }

        .edit-btn:hover {
            background: #007bff;
        }

        .code-content {
            background: #1e1e1e;
            overflow-x: auto;
        }

        .code-content pre {
            margin: 0;
            padding: 20px;
            background: transparent;
            color: #f8f8f2;
            font-size: 14px;
            line-height: 1.6;
            overflow-x: auto;
        }

        .code-content code {
            font-family: 'Fira Code', 'Monaco', 'Consolas', monospace;
            background: transparent;
            color: inherit;
        }

        /* Inline code */
        .inline-code {
            background: rgba(0, 0, 0, 0.1);
            color: #e6db74;
            padding: 2px 6px;
            border-radius: 4px;
            font-family: 'Fira Code', 'Monaco', 'Consolas', monospace;
            font-size: 0.9em;
            border: 1px solid rgba(0, 0, 0, 0.2);
        }
    </style>
    
    <!-- Font Awesome for icons -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</body>
</html>
