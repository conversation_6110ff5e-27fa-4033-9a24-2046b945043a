<!DOCTYPE html>
<html lang="so">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OpenAI API Key Setup Guide - Tincada AI</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
            padding: 2rem;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 3rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        h1 {
            text-align: center;
            font-size: 2.5rem;
            margin-bottom: 1rem;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .subtitle {
            text-align: center;
            font-size: 1.2rem;
            margin-bottom: 3rem;
            opacity: 0.9;
        }

        .status-section {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            border-left: 4px solid #4ecdc4;
        }

        .status-section h2 {
            color: #4ecdc4;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .api-status {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            margin: 2rem 0;
        }

        .status-card {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 1.5rem;
            text-align: center;
        }

        .status-card.working {
            border-color: rgba(76, 175, 80, 0.5);
            background: rgba(76, 175, 80, 0.1);
        }

        .status-card.error {
            border-color: rgba(244, 67, 54, 0.5);
            background: rgba(244, 67, 54, 0.1);
        }

        .status-card.warning {
            border-color: rgba(255, 193, 7, 0.5);
            background: rgba(255, 193, 7, 0.1);
        }

        .status-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
        }

        .status-card h3 {
            margin-bottom: 0.5rem;
        }

        .status-card p {
            opacity: 0.8;
            line-height: 1.5;
        }

        .setup-steps {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            padding: 2rem;
            margin: 2rem 0;
        }

        .step {
            display: flex;
            align-items: flex-start;
            gap: 1rem;
            margin-bottom: 2rem;
            padding: 1.5rem;
            background: rgba(0, 0, 0, 0.2);
            border-radius: 10px;
            border-left: 4px solid #4ecdc4;
        }

        .step-number {
            width: 40px;
            height: 40px;
            background: linear-gradient(45deg, #4ecdc4, #44a08d);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            flex-shrink: 0;
        }

        .step-content h4 {
            color: #4ecdc4;
            margin-bottom: 0.5rem;
        }

        .step-content p {
            line-height: 1.6;
            margin-bottom: 1rem;
        }

        .code-block {
            background: rgba(0, 0, 0, 0.5);
            border-radius: 8px;
            padding: 1rem;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            color: #a8e6cf;
            margin: 0.5rem 0;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .warning-box {
            background: rgba(255, 193, 7, 0.1);
            border: 1px solid rgba(255, 193, 7, 0.3);
            border-radius: 10px;
            padding: 1.5rem;
            margin: 2rem 0;
            border-left: 4px solid #ffc107;
        }

        .warning-box h3 {
            color: #ffc107;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .demo-buttons {
            display: flex;
            gap: 20px;
            justify-content: center;
            margin-top: 3rem;
            flex-wrap: wrap;
        }

        .demo-btn {
            background: linear-gradient(45deg, #4ecdc4, #44a08d);
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 25px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 10px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(78, 205, 196, 0.3);
        }

        .demo-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(78, 205, 196, 0.4);
        }

        .demo-btn.secondary {
            background: linear-gradient(45deg, #ff6b6b, #ee5a52);
            box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
        }

        .demo-btn.secondary:hover {
            box-shadow: 0 6px 20px rgba(255, 107, 107, 0.4);
        }

        @media (max-width: 768px) {
            body { padding: 1rem; }
            .container { padding: 2rem 1rem; }
            h1 { font-size: 2rem; }
            .api-status { grid-template-columns: 1fr; }
            .demo-buttons { flex-direction: column; align-items: center; }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔑 OpenAI API Key Setup</h1>
        <p class="subtitle">Configure your OpenAI API key for image generation and enhanced AI features</p>
        
        <div class="status-section">
            <h2>📊 Current API Status</h2>
            <div class="api-status">
                <div class="status-card working">
                    <div class="status-icon">✅</div>
                    <h3>Chat API</h3>
                    <p>Working with current API key<br>
                    <code>sk-proj-sayejx...</code></p>
                </div>
                
                <div class="status-card error">
                    <div class="status-icon">❌</div>
                    <h3>Image Generation</h3>
                    <p>Billing limit reached<br>
                    Need credits for DALL-E</p>
                </div>
                
                <div class="status-card warning">
                    <div class="status-icon">⚠️</div>
                    <h3>Provided Key</h3>
                    <p>Invalid API key format<br>
                    <code>sk-OfAYphzG0Rzg...</code></p>
                </div>
            </div>
        </div>
        
        <div class="setup-steps">
            <h3 style="color: #4ecdc4; margin-bottom: 2rem;">🚀 How to Get Valid OpenAI API Key</h3>
            
            <div class="step">
                <div class="step-number">1</div>
                <div class="step-content">
                    <h4>Visit OpenAI Platform</h4>
                    <p>Go to OpenAI's official platform and create an account or sign in:</p>
                    <div class="code-block">https://platform.openai.com</div>
                </div>
            </div>
            
            <div class="step">
                <div class="step-number">2</div>
                <div class="step-content">
                    <h4>Navigate to API Keys</h4>
                    <p>Once logged in, go to the API Keys section in your dashboard:</p>
                    <div class="code-block">Dashboard → API Keys → Create new secret key</div>
                </div>
            </div>
            
            <div class="step">
                <div class="step-number">3</div>
                <div class="step-content">
                    <h4>Add Billing Information</h4>
                    <p>For image generation, you need to add payment method and credits:</p>
                    <div class="code-block">Settings → Billing → Add payment method</div>
                    <p><strong>Note:</strong> DALL-E 3 costs ~$0.04 per image (1024x1024)</p>
                </div>
            </div>
            
            <div class="step">
                <div class="step-number">4</div>
                <div class="step-content">
                    <h4>Update .env File</h4>
                    <p>Replace the API key in your .env file:</p>
                    <div class="code-block">OPENAI_API_KEY=sk-your-new-valid-key-here</div>
                </div>
            </div>
            
            <div class="step">
                <div class="step-number">5</div>
                <div class="step-content">
                    <h4>Restart Server</h4>
                    <p>Restart the Flask server to load the new API key:</p>
                    <div class="code-block">python flask_server.py</div>
                </div>
            </div>
        </div>
        
        <div class="warning-box">
            <h3>⚠️ API Key Issues</h3>
            <p><strong>Current Issues:</strong></p>
            <ul style="margin: 1rem 0; padding-left: 2rem; line-height: 1.8;">
                <li><strong>sk-OfAYphzG0Rzg...</strong> - Invalid format (401 error)</li>
                <li><strong>sk-proj-sayejx...</strong> - Valid but no image credits</li>
                <li><strong>Image Generation:</strong> Requires billing setup</li>
                <li><strong>Chat:</strong> Working with current key</li>
            </ul>
            <p><strong>Solution:</strong> Get a valid API key with billing enabled from OpenAI Platform</p>
        </div>
        
        <div class="status-section">
            <h2>🎨 What Works Now</h2>
            <div style="background: rgba(255,255,255,0.05); padding: 1.5rem; border-radius: 10px; margin-top: 1rem;">
                <h4 style="color: #4ecdc4; margin-bottom: 1rem;">✅ Currently Working:</h4>
                <ul style="line-height: 1.8; padding-left: 2rem;">
                    <li><strong>🤖 AI Chat:</strong> Real OpenAI responses in multiple languages</li>
                    <li><strong>🌍 Multilingual:</strong> 7 languages with auto-detection</li>
                    <li><strong>🛠️ AI Tools:</strong> Think Longer, Deep Research, Web Search, Canvas</li>
                    <li><strong>🔐 Social Login:</strong> Google and Apple Sign In</li>
                    <li><strong>📱 Interface:</strong> Modern, responsive design</li>
                </ul>
                
                <h4 style="color: #ffc107; margin: 1.5rem 0 1rem;">⚠️ Needs Valid API Key:</h4>
                <ul style="line-height: 1.8; padding-left: 2rem;">
                    <li><strong>🎨 Image Generation:</strong> DALL-E 3 integration ready</li>
                    <li><strong>💳 Billing Required:</strong> ~$0.04 per image</li>
                    <li><strong>🔑 Valid Key Format:</strong> Must start with sk- and be valid</li>
                </ul>
            </div>
        </div>
        
        <div class="demo-buttons">
            <a href="image-generation-demo.html" class="demo-btn">
                🎨 Test Image Generation
            </a>
            <a href="multilingual-demo.html" class="demo-btn">
                🌍 Test Multilingual Chat
            </a>
            <a href="dashboard.html" class="demo-btn secondary">
                🏠 Main Dashboard
            </a>
        </div>
    </div>

    <script>
        // Check API status on page load
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🔑 API Key Setup Guide loaded');
            
            // Test server connection
            fetch('http://localhost:5001/api/health')
                .then(response => response.json())
                .then(data => {
                    console.log('Server status:', data);
                    updateStatusDisplay(data);
                })
                .catch(error => {
                    console.log('Server not connected:', error);
                });
        });

        function updateStatusDisplay(data) {
            // Update status cards based on server response
            const statusCards = document.querySelectorAll('.status-card');
            
            if (data.openai_available) {
                // Update chat status to working
                statusCards[0].className = 'status-card working';
                statusCards[0].querySelector('h3').textContent = 'Chat API ✅';
            } else {
                // Update chat status to error
                statusCards[0].className = 'status-card error';
                statusCards[0].querySelector('h3').textContent = 'Chat API ❌';
            }
        }
    </script>
</body>
</html>
