<?php
/**
 * Dashboard API for retrieving database statistics and data
 */

require_once 'config.php';

class DashboardAPI {
    private $db;
    
    public function __construct() {
        $this->db = Database::getInstance();
    }
    
    /**
     * Get overview statistics
     */
    public function getOverviewStats() {
        try {
            // Get basic statistics
            $stats = [];
            
            // Total users
            $result = $this->db->fetchRow("SELECT COUNT(*) as count FROM users");
            $stats['total_users'] = $result['count'] ?? 0;
            
            // Total messages
            $result = $this->db->fetchRow("SELECT COUNT(*) as count FROM chat_messages");
            $stats['total_messages'] = $result['count'] ?? 0;
            
            // Total conversations
            $result = $this->db->fetchRow("SELECT COUNT(*) as count FROM conversations");
            $stats['total_conversations'] = $result['count'] ?? 0;
            
            // Total tokens used
            $result = $this->db->fetchRow("SELECT SUM(total_tokens_used) as total FROM users");
            $stats['total_tokens'] = $result['total'] ?? 0;
            
            // Recent activity
            $recent_activity = $this->db->fetchAll("SELECT * FROM recent_activity LIMIT 10");
            
            return [
                'success' => true,
                'stats' => $stats,
                'recent_activity' => $recent_activity
            ];
            
        } catch (Exception $e) {
            logActivity('error', 'Dashboard overview error: ' . $e->getMessage());
            return [
                'success' => false,
                'error' => 'Failed to load overview data'
            ];
        }
    }
    
    /**
     * Get users data
     */
    public function getUsersData() {
        try {
            $users = $this->db->fetchAll("SELECT * FROM user_stats ORDER BY created_at DESC LIMIT 50");
            
            return [
                'success' => true,
                'users' => $users
            ];
            
        } catch (Exception $e) {
            logActivity('error', 'Dashboard users error: ' . $e->getMessage());
            return [
                'success' => false,
                'error' => 'Failed to load users data'
            ];
        }
    }
    
    /**
     * Get messages data
     */
    public function getMessagesData() {
        try {
            $sql = "SELECT cm.*, u.username, c.title as conversation_title 
                    FROM chat_messages cm
                    LEFT JOIN users u ON cm.user_id = u.user_id
                    LEFT JOIN conversations c ON cm.conversation_id = c.conversation_id
                    ORDER BY cm.created_at DESC 
                    LIMIT 100";
            
            $messages = $this->db->fetchAll($sql);
            
            return [
                'success' => true,
                'messages' => $messages
            ];
            
        } catch (Exception $e) {
            logActivity('error', 'Dashboard messages error: ' . $e->getMessage());
            return [
                'success' => false,
                'error' => 'Failed to load messages data'
            ];
        }
    }
    
    /**
     * Get analytics data
     */
    public function getAnalyticsData() {
        try {
            $analytics = [];
            
            // Messages per day (last 7 days)
            $sql = "SELECT DATE(created_at) as date, COUNT(*) as count 
                    FROM chat_messages 
                    WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
                    GROUP BY DATE(created_at)
                    ORDER BY date DESC";
            $analytics['messages_per_day'] = $this->db->fetchAll($sql);
            
            // API usage statistics
            $sql = "SELECT api_type, COUNT(*) as total_requests, 
                           SUM(tokens_used) as total_tokens,
                           AVG(tokens_used) as avg_tokens,
                           SUM(CASE WHEN success = 1 THEN 1 ELSE 0 END) as successful_requests
                    FROM api_usage 
                    GROUP BY api_type";
            $analytics['api_usage'] = $this->db->fetchAll($sql);
            
            // Top users by activity
            $sql = "SELECT u.user_id, u.username, u.total_messages, u.total_tokens_used
                    FROM users u
                    WHERE u.total_messages > 0
                    ORDER BY u.total_messages DESC
                    LIMIT 10";
            $analytics['top_users'] = $this->db->fetchAll($sql);
            
            // Response source distribution
            $sql = "SELECT response_source, COUNT(*) as count
                    FROM chat_messages 
                    WHERE message_type = 'ai'
                    GROUP BY response_source";
            $analytics['response_sources'] = $this->db->fetchAll($sql);
            
            return [
                'success' => true,
                'analytics' => $analytics
            ];
            
        } catch (Exception $e) {
            logActivity('error', 'Dashboard analytics error: ' . $e->getMessage());
            return [
                'success' => false,
                'error' => 'Failed to load analytics data'
            ];
        }
    }
    
    /**
     * Get system health status
     */
    public function getSystemHealth() {
        try {
            $health = [];
            
            // Database connection test
            $health['database'] = 'connected';
            
            // Check Tincada AI API
            $api_url = TINCADA_API_URL . '/health';
            $context = stream_context_create([
                'http' => [
                    'timeout' => 5,
                    'method' => 'GET'
                ]
            ]);
            
            $api_response = @file_get_contents($api_url, false, $context);
            $health['tincada_api'] = $api_response ? 'connected' : 'disconnected';
            
            // Recent error count
            $result = $this->db->fetchRow(
                "SELECT COUNT(*) as count FROM system_logs 
                 WHERE log_type = 'error' AND created_at >= DATE_SUB(NOW(), INTERVAL 1 HOUR)"
            );
            $health['recent_errors'] = $result['count'] ?? 0;
            
            // Disk usage (if available)
            $health['disk_usage'] = disk_free_space('.') ? 
                round((1 - disk_free_space('.') / disk_total_space('.')) * 100, 2) : 'unknown';
            
            return [
                'success' => true,
                'health' => $health
            ];
            
        } catch (Exception $e) {
            logActivity('error', 'System health check error: ' . $e->getMessage());
            return [
                'success' => false,
                'error' => 'Failed to check system health'
            ];
        }
    }
}

// Handle API requests
$action = $_GET['action'] ?? '';
$api = new DashboardAPI();

switch ($action) {
    case 'overview':
        $result = $api->getOverviewStats();
        break;
        
    case 'users':
        $result = $api->getUsersData();
        break;
        
    case 'messages':
        $result = $api->getMessagesData();
        break;
        
    case 'analytics':
        $result = $api->getAnalyticsData();
        break;
        
    case 'health':
        $result = $api->getSystemHealth();
        break;
        
    default:
        $result = [
            'success' => false,
            'error' => 'Invalid action'
        ];
}

// Log API access
logActivity('info', "Dashboard API accessed: $action");

// Return JSON response
jsonResponse($result);
?>
