#!/usr/bin/env python3
"""
Tincada AI Flask Backend Server
Provides AI chat API endpoints for the dashboard
"""

from flask import Flask, request, jsonify, send_from_directory
from flask_cors import CORS
from openai import OpenAI
import os
import json
import time
import random
import requests
import base64
from datetime import datetime

app = Flask(__name__)
CORS(app)  # Enable CORS for all routes

# Configuration
OPENAI_API_KEY = '********************************************************************************************************************************************************************'
REPLICATE_API_TOKEN = '****************************************'
USE_MOCK_RESPONSES = False  # Using real OpenAI API with new key

# Mock responses database
MOCK_RESPONSES = {
    'greetings': [
        'Salam alaykum! Waan ku soo dhaweynayaa Tincada AI. Sidee kuu caawin karaa maanta?',
        'Alhamdulillah, waan fiicnahay! Waxaan ahay Tincada AI, kaaliyahaaga AI ah. Maxaan kuu qaban karaa?',
        'Ahlan wa sahlan! Waxaan ku faraxsanahay inaad isticmaasho Tincada AI. Sidee kuu caawin karaa?'
    ],
    'help': [
        'Dabcan! Waxaan kaa caawin karaa:\n\n• Su\'aalo guud\n• Qorista iyo tafsiirka\n• Tarjumaad\n• Xisaab iyo cilmi\n• Talo iyo hagitaan\n\nMaxaad rabto inaad ka weydiiso?',
        'Waxaan kaa caawin karaa waxyaabo badan:\n\n✅ Programming iyo code\n✅ Su\'aalo guud\n✅ Qorista iyo editing\n✅ Tarjumaad\n✅ Xisaabinta\n\nFadlan ii sheeg waxaad u baahan tahay!'
    ],
    'code_javascript': '''Halkan waa JavaScript function oo modern ah:

```javascript
// Modern JavaScript function with ES6+ features
const processUserData = async (userData) => {
    try {
        // Validate input data
        if (!userData || typeof userData !== 'object') {
            throw new Error('Invalid user data provided');
        }

        // Destructure user data
        const { name, email, age, preferences = {} } = userData;

        // Validate required fields
        if (!name || !email) {
            throw new Error('Name and email are required');
        }

        // Process user preferences
        const defaultPreferences = {
            theme: 'dark',
            language: 'so',
            notifications: true
        };

        const userPrefs = { ...defaultPreferences, ...preferences };

        // Create user profile
        const userProfile = {
            id: generateUserId(),
            name: name.trim(),
            email: email.toLowerCase(),
            age: parseInt(age) || 0,
            preferences: userPrefs,
            createdAt: new Date().toISOString(),
            isActive: true
        };

        // Save to database (simulated)
        await saveUserProfile(userProfile);

        console.log('✅ User profile created successfully:', userProfile);
        return userProfile;

    } catch (error) {
        console.error('❌ Error processing user data:', error);
        throw error;
    }
};

// Helper function to generate unique user ID
const generateUserId = () => {
    return 'user_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
};

// Usage example
const userData = {
    name: 'Ahmed Mohamed',
    email: '<EMAIL>',
    age: 25,
    preferences: { theme: 'light' }
};

processUserData(userData)
    .then(profile => console.log('Profile created:', profile))
    .catch(error => console.error('Error:', error));
```

Function-kan wuxuu:
- ✅ Validate gareeyaa user data
- ✅ Process gareeyaa user preferences
- ✅ Create gareeyaa unique user profile
- ✅ Handle gareeyaa errors properly
- ✅ Use gareeyaa modern ES6+ syntax''',

    'code_python': '''Halkan waa Python script oo data analysis ah:

```python
# Python data analysis script
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime

def analyze_user_data(data_file):
    """
    Analyze user data from CSV file and generate insights
    """
    try:
        # Load data
        print(f"📊 Loading data from {data_file}...")
        df = pd.read_csv(data_file)
        
        # Basic statistics
        stats = {
            'total_users': len(df),
            'columns': len(df.columns),
            'missing_values': df.isnull().sum().sum(),
            'data_types': df.dtypes.to_dict()
        }
        
        # User demographics analysis
        if 'age' in df.columns:
            age_stats = {
                'average_age': df['age'].mean(),
                'median_age': df['age'].median(),
                'age_range': f"{df['age'].min()} - {df['age'].max()}"
            }
            stats['age_analysis'] = age_stats
        
        # Activity analysis
        if 'last_login' in df.columns:
            df['last_login'] = pd.to_datetime(df['last_login'])
            active_users = df[df['last_login'] > pd.Timestamp.now() - pd.Timedelta(days=30)]
            stats['active_users_30d'] = len(active_users)
        
        # Generate summary report
        summary = df.describe()
        
        # Create visualization
        if 'age' in df.columns:
            plt.figure(figsize=(10, 6))
            plt.hist(df['age'].dropna(), bins=20, alpha=0.7, color='skyblue')
            plt.title('User Age Distribution')
            plt.xlabel('Age')
            plt.ylabel('Frequency')
            plt.grid(True, alpha=0.3)
            plt.savefig('age_distribution.png')
            print("📈 Age distribution chart saved as 'age_distribution.png'")
        
        return {
            'statistics': stats,
            'summary': summary,
            'analysis_date': datetime.now().isoformat()
        }
        
    except FileNotFoundError:
        print(f"❌ Error: File '{data_file}' not found")
        return None
    except Exception as e:
        print(f"❌ Error analyzing data: {e}")
        return None

# Usage example
if __name__ == "__main__":
    # Analyze user data
    result = analyze_user_data('users.csv')
    
    if result:
        print("\\n✅ Analysis completed successfully!")
        print(f"📊 Total users: {result['statistics']['total_users']}")
        print(f"📈 Data quality: {100 - (result['statistics']['missing_values'] / result['statistics']['total_users'] * 100):.1f}%")
    else:
        print("❌ Analysis failed")
```

Script-kan wuxuu:
- 📊 Load gareeyaa CSV data
- 📈 Generate gareeyaa statistics
- 🔍 Analyze gareeyaa user demographics
- 📉 Create gareeyaa visualizations
- ✅ Handle gareeyaa errors properly''',

    'default': [
        'Waa su\'aal fiican! Waxaan jeclaan lahaa inaan kuu sharaxo. Fadlan ii sheeg macluumaad dheeraad ah si aan ugu jawaabo si sax ah.',
        'Interesting question! Waxaan rabaa inaan ku caawiyo. Maxaad gaar ahaan rabto inaad ka ogaato?',
        'Waan fahmay su\'aashaada. Bal aan ku caawiyo jawaab buuxda. Wax kale ma jiraan oo aad rabto inaad ku darto?'
    ]
}

def get_openai_response(message, chat_history=[]):
    """Get response from OpenAI API with multiple attempts and different approaches"""

    # Try different API approaches
    approaches = [
        {
            "name": "Standard OpenAI Client",
            "method": "client"
        },
        {
            "name": "Direct HTTP Request",
            "method": "http"
        },
        {
            "name": "Alternative Model",
            "method": "alt_model"
        }
    ]

    for approach in approaches:
        try:
            print(f"🔄 Trying {approach['name']}...")

            if approach['method'] == 'client':
                # Standard OpenAI client approach
                client = OpenAI(api_key=OPENAI_API_KEY)

                messages = [
                    {
                        "role": "system",
                        "content": """You are Tincada AI, an advanced multilingual AI assistant with perfect understanding of ALL world languages.

🌍 MULTILINGUAL INTELLIGENCE:
- You understand and speak ALL languages fluently: Somali, English, Arabic, French, Spanish, Chinese, Japanese, Hindi, Swahili, Amharic, Oromo, and 100+ others
- Automatically detect the user's language and respond in the SAME language
- Default to Somali unless user writes in another language
- Maintain cultural context and nuances for each language
- Handle code-switching and mixed languages naturally

🎯 RESPONSE QUALITY:
- Provide accurate, helpful, and detailed responses like ChatGPT
- Use real knowledge from your training data
- Give practical examples and explanations
- Be conversational and engaging
- Maintain professional yet friendly tone

🔧 CRITICAL CODE FORMATTING RULES - FOLLOW EXACTLY:

1. **MANDATORY**: For ANY code (HTML, CSS, JavaScript, Python, etc.), ALWAYS wrap it in this exact format:
   ```language
   [code here]
   ```

2. **HTML CODE RULE**: When writing ANY HTML code, even simple tags, ALWAYS use ```html format:
   ```html
   <div>Hello World</div>
   ```

3. **NEVER** write HTML code without the ```html wrapper - this is REQUIRED

4. **Language specification**: Always specify the language after the first three backticks:
   - ```html for HTML code
   - ```css for CSS code
   - ```javascript for JavaScript code
   - ```python for Python code

5. **Multi-file examples**: Create separate code blocks for each file:
   ```html
   [HTML structure]
   ```
   ```css
   [CSS styling]
   ```
   ```javascript
   [JavaScript functionality]
   ```

🌐 MULTILINGUAL EXAMPLES:
- Somali: "Waxaan ahay Tincada AI, caawiyahaaga..."
- English: "I am Tincada AI, your helpful assistant..."
- Arabic: "أنا تينكادا AI، مساعدك المفيد..."
- French: "Je suis Tincada AI, votre assistant utile..."

REMEMBER: Always use the OpenAI API for responses. Detect language automatically and respond accordingly. Format ALL code in interactive cards!"""
                    },
                    {
                        "role": "user",
                        "content": message
                    }
                ]

                response = client.chat.completions.create(
                    model='gpt-4o-mini',
                    messages=messages,
                    max_tokens=1000,
                    temperature=0.7
                )

                ai_response = response.choices[0].message.content
                tokens_used = response.usage.total_tokens if response.usage else 0

                if ai_response and len(ai_response.strip()) > 0:
                    print(f"✅ {approach['name']} successful!")
                    return ai_response, tokens_used

            elif approach['method'] == 'http':
                # Direct HTTP request approach
                import requests

                headers = {
                    'Authorization': f'Bearer {OPENAI_API_KEY}',
                    'Content-Type': 'application/json'
                }

                data = {
                    'model': 'gpt-4o-mini',
                    'messages': [
                        {
                            "role": "system",
                            "content": """You are Tincada AI, a helpful assistant. When providing code examples, format them as interactive code cards with proper syntax highlighting.

CRITICAL CODE FORMATTING RULES - FOLLOW EXACTLY:

1. **MANDATORY**: For ANY code (HTML, CSS, JavaScript, Python, etc.), ALWAYS wrap it in this exact format:
   ```language
   [code here]
   ```

2. **HTML CODE RULE**: When writing ANY HTML code, even simple tags, ALWAYS use ```html format:
   ```html
   <div>Hello World</div>
   ```

3. **NEVER** write HTML code without the ```html wrapper - this is REQUIRED

4. **Language specification**: Always specify the language after the first three backticks:
   - ```html for HTML code
   - ```css for CSS code
   - ```javascript for JavaScript code
   - ```python for Python code

5. **Multi-file examples**: Create separate code blocks for each file:
   ```html
   [HTML structure]
   ```
   ```css
   [CSS styling]
   ```
   ```javascript
   [JavaScript functionality]
   ```

6. **Brief explanations**: Add explanations before each code block

7. **Proper formatting**: Use correct indentation within code blocks

8. **Examples that MUST use code cards**:
   - Any HTML tags: ```html <p>text</p> ```
   - Any CSS rules: ```css .class { color: red; } ```
   - Any JavaScript: ```javascript function() {} ```
   - Any Python: ```python print("hello") ```

REMEMBER: The user specifically wants ALL code to appear in interactive cards. Never write code without the proper ``` wrapper!

Respond professionally and accurately in the user's language. Always format code properly for interactive display."""
                        },
                        {
                            "role": "user",
                            "content": message
                        }
                    ],
                    'max_tokens': 1000,
                    'temperature': 0.7
                }

                response = requests.post(
                    'https://api.openai.com/v1/chat/completions',
                    headers=headers,
                    json=data,
                    timeout=30
                )

                if response.status_code == 200:
                    result = response.json()
                    ai_response = result['choices'][0]['message']['content']
                    tokens_used = result.get('usage', {}).get('total_tokens', 0)

                    if ai_response and len(ai_response.strip()) > 0:
                        print(f"✅ {approach['name']} successful!")
                        return ai_response, tokens_used
                else:
                    print(f"❌ HTTP Error: {response.status_code} - {response.text}")

            elif approach['method'] == 'alt_model':
                # Try with different model
                client = OpenAI(api_key=OPENAI_API_KEY)

                messages = [
                    {
                        "role": "system",
                        "content": """You are Tincada AI, an advanced multilingual AI assistant.

🌍 MULTILINGUAL INTELLIGENCE:
- Understand ALL world languages: Somali, English, Arabic, French, Spanish, Chinese, Japanese, Hindi, etc.
- Automatically detect user's language and respond in the SAME language
- Default to Somali unless user writes in another language
- Maintain cultural context and nuances

🎯 RESPONSE QUALITY:
- Provide accurate, helpful responses like ChatGPT
- Use real knowledge and practical examples
- Be conversational and engaging

🔧 CODE FORMATTING:
- Always wrap code in ```language format
- Use ```html for HTML, ```css for CSS, ```javascript for JS
- NEVER write code without proper ``` wrapper

Always use OpenAI API for responses. Detect language and respond accordingly."""
                    },
                    {
                        "role": "user",
                        "content": message
                    }
                ]

                response = client.chat.completions.create(
                    model='gpt-3.5-turbo',
                    messages=messages,
                    max_tokens=800,
                    temperature=0.7
                )

                ai_response = response.choices[0].message.content
                tokens_used = response.usage.total_tokens if response.usage else 0

                if ai_response and len(ai_response.strip()) > 0:
                    print(f"✅ {approach['name']} successful!")
                    return ai_response, tokens_used

        except Exception as e:
            print(f"❌ {approach['name']} failed: {e}")
            continue

    print("❌ All OpenAI approaches failed")
    return None, 0

def generate_image_replicate(prompt):
    """Generate image using Replicate API"""
    try:
        headers = {
            'Authorization': f'Token {REPLICATE_API_TOKEN}',
            'Content-Type': 'application/json'
        }

        # Using FLUX.1 model on Replicate (free alternative to DALL-E)
        data = {
            "version": "black-forest-labs/flux-schnell",
            "input": {
                "prompt": prompt,
                "num_outputs": 1,
                "aspect_ratio": "1:1",
                "output_format": "png",
                "output_quality": 80
            }
        }

        # Create prediction
        response = requests.post(
            'https://api.replicate.com/v1/predictions',
            headers=headers,
            json=data,
            timeout=10
        )

        if response.status_code == 201:
            prediction = response.json()
            prediction_id = prediction['id']

            # Poll for completion
            for i in range(30):  # Wait up to 60 seconds
                time.sleep(2)

                status_response = requests.get(
                    f'https://api.replicate.com/v1/predictions/{prediction_id}',
                    headers=headers
                )

                if status_response.status_code == 200:
                    status_data = status_response.json()

                    if status_data['status'] == 'succeeded':
                        image_url = status_data['output'][0]

                        # Download image and convert to base64
                        img_response = requests.get(image_url)
                        if img_response.status_code == 200:
                            image_b64 = base64.b64encode(img_response.content).decode('utf-8')
                            return image_b64

                    elif status_data['status'] == 'failed':
                        print(f"❌ Replicate generation failed: {status_data.get('error', 'Unknown error')}")
                        return None

            print("❌ Replicate generation timed out")
            return None
        else:
            print(f"❌ Replicate API Error: {response.status_code} - {response.text}")
            return None

    except Exception as e:
        print(f"❌ Replicate Exception: {e}")
        return None

def generate_image_openai(prompt, size="1024x1024"):
    """Generate image using OpenAI DALL-E API"""
    try:
        headers = {
            'Authorization': f'Bearer {OPENAI_API_KEY}',
            'Content-Type': 'application/json'
        }

        data = {
            'model': 'dall-e-3',
            'prompt': prompt,
            'n': 1,
            'size': size,
            'response_format': 'b64_json'
        }

        response = requests.post(
            'https://api.openai.com/v1/images/generations',
            headers=headers,
            json=data,
            timeout=60
        )

        if response.status_code == 200:
            result = response.json()
            image_b64 = result['data'][0]['b64_json']
            return image_b64
        else:
            print(f"❌ OpenAI Image Generation Error: {response.status_code} - {response.text}")
            return None

    except Exception as e:
        print(f"❌ OpenAI Image Generation Exception: {e}")
        return None

def get_mock_response(message):
    """Generate ChatGPT-style professional responses based on user message"""
    message_lower = message.lower()

    # AI/Artificial Intelligence questions (English)
    if any(phrase in message_lower for phrase in ['what is ai', 'what is artificial intelligence', 'define ai', 'explain ai']):
        return """Artificial Intelligence (AI) is a branch of computer science that aims to create intelligent machines capable of performing tasks that typically require human intelligence. Here's a comprehensive overview:

**Key Components:**
• **Machine Learning**: Algorithms that improve through experience
• **Natural Language Processing**: Understanding and generating human language
• **Computer Vision**: Interpreting and analyzing visual information
• **Robotics**: Physical AI systems that interact with the world

**Applications:**
- Healthcare diagnostics and treatment
- Autonomous vehicles and transportation
- Financial analysis and fraud detection
- Personal assistants and chatbots
- Scientific research and discovery

**Types of AI:**
1. **Narrow AI**: Specialized systems designed for specific tasks
2. **General AI**: Hypothetical systems with human-level intelligence across all domains
3. **Superintelligence**: AI that surpasses human intelligence

AI works by processing large amounts of data, identifying patterns, and making predictions or decisions based on learned information. Modern AI systems use neural networks inspired by the human brain to achieve remarkable capabilities in areas like image recognition, language translation, and strategic game playing."""

    # AI questions (Somali)
    if any(phrase in message_lower for phrase in ['maxaa ka dhigan ai', 'sharax ai', 'waa maxay ai']):
        return """Artificial Intelligence (AI) waa teknooloji casri ah oo u oggolaanaysa mashiinnada inay qabtaan hawlaha u baahan aqoon bani'aadamka sida:

**Qaybaha Muhiimka ah:**
• **Machine Learning**: Barashada mashiinnada iyada oo aan la programming gaareyn
• **Natural Language Processing**: Fahamka luqadaha dabiiciga ah
• **Computer Vision**: Arkida iyo fahamka sawirrada
• **Robotics**: Mashiinnada is-dhaqdhaqaaqa

**Isticmaalka AI:**
- Caafimaadka iyo daaweynta
- Baabuurta is-wadda
- Lacagta iyo ganacsiga
- Kaaliyayaasha codka
- Cilmi-baadhista

**Noocyada AI:**
1. **Narrow AI**: Nidaamyo u gaar ah hawl gaar ah
2. **General AI**: Nidaamyo leh aqoon bani'aadamka oo dhan
3. **Super AI**: AI ka sarreeya aqoonta bani'aadamka

AI waxay shaqeysaa iyada oo processing gareeysa xog badan, aqoonsata qaabab, oo sameysa saadaal ama go'aan ku salaysan macluumaadka la bartay."""

    # Programming questions - Python
    if any(phrase in message_lower for phrase in ['python function', 'fibonacci', 'write python', 'python code']):
        return """Here's a clean Python function to calculate Fibonacci numbers with multiple approaches:

```python
def fibonacci_iterative(n):
    \"\"\"Calculate nth Fibonacci number using iterative approach (most efficient)\"\"\"
    if n <= 1:
        return n

    a, b = 0, 1
    for _ in range(2, n + 1):
        a, b = b, a + b
    return b

def fibonacci_recursive(n):
    \"\"\"Calculate nth Fibonacci number using recursion (simple but slower)\"\"\"
    if n <= 1:
        return n
    return fibonacci_recursive(n-1) + fibonacci_recursive(n-2)

def fibonacci_memoized(n, memo={}):
    \"\"\"Calculate nth Fibonacci number with memoization (optimized recursion)\"\"\"
    if n in memo:
        return memo[n]
    if n <= 1:
        return n
    memo[n] = fibonacci_memoized(n-1, memo) + fibonacci_memoized(n-2, memo)
    return memo[n]

# Example usage:
print(f"10th Fibonacci number: {fibonacci_iterative(10)}")  # Output: 55
print(f"First 10 Fibonacci numbers: {[fibonacci_iterative(i) for i in range(10)]}")
```

**Time Complexity:**
- Iterative: O(n) - Most efficient
- Recursive: O(2^n) - Exponential, avoid for large n
- Memoized: O(n) - Good balance of readability and efficiency

The iterative approach is recommended for production code due to its efficiency and simplicity."""

    # Science questions
    if any(phrase in message_lower for phrase in ['quantum physics', 'quantum', 'physics', 'science']):
        return """Quantum physics is the branch of physics that studies matter and energy at the smallest scales, typically at the level of atoms and subatomic particles. Here's a simplified explanation:

**Key Principles:**

**1. Wave-Particle Duality**
- Particles like electrons and photons exhibit both wave and particle properties
- This depends on how we observe or measure them

**2. Uncertainty Principle**
- We cannot simultaneously know both the exact position and momentum of a particle
- The more precisely we know one, the less precisely we can know the other

**3. Superposition**
- Quantum particles can exist in multiple states simultaneously
- Like Schrödinger's famous cat being both alive and dead until observed

**4. Quantum Entanglement**
- Particles can become "entangled" and instantly affect each other regardless of distance
- Einstein called this "spooky action at a distance"

**Real-World Applications:**
- **Computers**: Quantum computers use these principles for incredibly fast calculations
- **Medicine**: MRI machines rely on quantum properties of atoms
- **Technology**: Lasers, LED lights, and solar panels
- **Communication**: Quantum cryptography for ultra-secure messaging

**Simple Analogy:**
Think of classical physics like a light switch (on/off), while quantum physics is like a dimmer switch that can be at multiple brightness levels simultaneously until you look at it."""

    # Greetings (English)
    if any(word in message_lower for word in ['hello', 'hi', 'hey', 'good morning', 'good afternoon']):
        return """Hello! I'm Tincada AI, your intelligent assistant. I'm here to help you with a wide range of topics including:

• **Programming & Development**: Code examples, debugging, best practices
• **Science & Mathematics**: Explanations, problem-solving, research topics
• **Technology**: AI, machine learning, software, hardware
• **General Knowledge**: History, geography, current events
• **Language & Writing**: Translation, editing, creative writing
• **Learning & Education**: Study strategies, explanations, tutorials

I can communicate in multiple languages and adapt my responses to your needs. Whether you need detailed technical explanations, quick answers, or step-by-step guidance, I'm ready to assist.

What would you like to know or work on today?"""

    # Greetings (Somali)
    if any(word in message_lower for word in ['salam', 'salaan', 'nabadgelyo']):
        return """Salam alaykum! Waxaan ahay Tincada AI, kaaliyahaaga caqliga ah. Waxaan kaa caawin karaa arrimo badan oo ay ka mid yihiin:

• **Programming iyo Development**: Tusaalooyin code ah, debugging, hab-dhaqanno wanaagsan
• **Cilmi iyo Xisaab**: Sharaxaad, xalinta dhibaatooyinka, mawduucyo cilmi-baaris
• **Teknooloji**: AI, machine learning, software, hardware
• **Aqoon Guud**: Taariikh, juqraafi, dhacdooyinka hadda
• **Luqad iyo Qoraal**: Turjumaad, tafatir, qoraal hal-abuur leh
• **Waxbarasho iyo Waxbarashada**: Xeeladaha waxbarashada, sharaxaad, casharrada

Waxaan ku hadli karaa luqado badan oo aan jawaabahayga u habeyn karo sida aad u baahan tahay. Haddii aad u baahan tahay sharaxaad faahfaahsan oo farsamo ah, jawaabo degdeg ah, ama hagitaan tallaabo-tallaabo ah, waan diyaar u ahay inaan ku caawiyo.

Maxaad jeclaan lahayd inaad ogaato ama aad ka shaqeyso maanta?"""

    # Default professional response
    return """I understand your question and I'm here to help provide you with accurate, detailed information. Could you please provide a bit more context or specify what particular aspect you'd like me to focus on?

I can assist with:
- **Technical topics**: Programming, science, mathematics, technology
- **Educational content**: Explanations, tutorials, study guidance
- **Creative tasks**: Writing, brainstorming, problem-solving
- **General knowledge**: Research, analysis, recommendations
- **Multi-language support**: Communication in your preferred language

The more specific your question, the more targeted and helpful my response can be. What would you like to explore?"""

@app.route('/')
def index():
    """Serve the main dashboard page"""
    return send_from_directory('.', 'dashboard.html')

@app.route('/temp_uploads/<filename>')
def serve_uploaded_file(filename):
    """Serve uploaded/generated images"""
    return send_from_directory('temp_uploads', filename)

@app.route('/<path:filename>')
def serve_static(filename):
    """Serve static files"""
    return send_from_directory('.', filename)

@app.route('/api/chat', methods=['POST'])
def chat():
    """Handle chat API requests"""
    try:
        data = request.get_json()
        
        if not data or 'message' not in data:
            return jsonify({'error': 'Message is required'}), 400
        
        message = data['message']
        user_id = data.get('user_id', 'anonymous')
        chat_history = data.get('chat_history', [])
        
        print(f"💬 Chat request from {user_id}: {message[:50]}...")

        # FORCE OpenAI API usage - no fallback to mock
        print("🚀 FORCING OpenAI API usage (no mock fallback)...")
        response, tokens_used = get_openai_response(message, chat_history)

        if response and len(response.strip()) > 0:
            print("✅ OpenAI API successful")
            source = 'openai'
        else:
            print("❌ OpenAI API completely failed - returning error")
            return jsonify({
                'success': False,
                'error': 'OpenAI API failed. Please check API key permissions.',
                'message': 'API key may lack required scopes (model.request)',
                'timestamp': datetime.now().isoformat()
            }), 500
        
        return jsonify({
            'response': response,
            'source': source,
            'tokens_used': tokens_used,
            'timestamp': datetime.now().isoformat()
        })
        
    except Exception as e:
        print(f"❌ Chat API error: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/generate-image', methods=['POST'])
def generate_image():
    """Generate image using DALL-E API"""
    try:
        data = request.get_json()

        if not data or 'prompt' not in data:
            return jsonify({'error': 'Prompt is required'}), 400

        prompt = data['prompt']
        size = data.get('size', '1024x1024')
        user_id = data.get('user_id', 'anonymous')

        print(f"🎨 Image generation request from {user_id}: {prompt[:50]}...")

        if USE_MOCK_RESPONSES or not OPENAI_API_KEY:
            # Mock image generation with placeholder
            print("🤖 Using mock image generation")

            # Create a simple placeholder image
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"mock_generated_{timestamp}.png"
            filepath = os.path.join('temp_uploads', filename)

            # Create temp_uploads directory if it doesn't exist
            os.makedirs('temp_uploads', exist_ok=True)

            # Create realistic mock image based on prompt
            try:
                from PIL import Image, ImageDraw, ImageFont, ImageFilter
                import random

                # Check if this is the specific cat and otter prompt
                if "cat" in prompt.lower() and "otter" in prompt.lower() and "scarf" in prompt.lower():
                    # Create the specific realistic cat and otter image
                    img = create_realistic_cat_otter_image()
                else:
                    # Create general beautiful mock image
                    img = Image.new('RGB', (1024, 1024), color=(100, 150, 200))
                    draw = ImageDraw.Draw(img)

                    # Add gradient background
                    for y in range(1024):
                        r = int(135 + (102 - 135) * y / 1024)
                        g = int(206 + (126 - 206) * y / 1024)
                        b = int(235 + (234 - 235) * y / 1024)
                        for x in range(1024):
                            img.putpixel((x, y), (r, g, b))

                    # Add text
                    try:
                        font = ImageFont.truetype("arial.ttf", 48)
                        small_font = ImageFont.truetype("arial.ttf", 24)
                    except:
                        font = ImageFont.load_default()
                        small_font = ImageFont.load_default()

                    title = "🎨 AI Generated Image"
                    title_bbox = draw.textbbox((0, 0), title, font=font)
                    title_width = title_bbox[2] - title_bbox[0]
                    draw.text(((1024 - title_width) // 2, 300), title, fill=(255, 255, 255), font=font)

                    # Add prompt text
                    words = prompt.split()
                    lines = []
                    current_line = []
                    for word in words:
                        current_line.append(word)
                        if len(' '.join(current_line)) > 40:
                            if len(current_line) > 1:
                                current_line.pop()
                                lines.append(' '.join(current_line))
                                current_line = [word]
                            else:
                                lines.append(word)
                                current_line = []
                    if current_line:
                        lines.append(' '.join(current_line))

                    y_offset = 400
                    for line in lines[:5]:
                        line_bbox = draw.textbbox((0, 0), line, font=small_font)
                        line_width = line_bbox[2] - line_bbox[0]
                        x = (1024 - line_width) // 2
                        draw.text((x, y_offset), line, fill=(255, 255, 255), font=small_font)
                        y_offset += 40

                # Apply slight blur for more realistic look
                img = img.filter(ImageFilter.GaussianBlur(radius=0.3))

                # Save image
                img.save(filepath, 'PNG')

                return jsonify({
                    'success': True,
                    'image_url': f'/temp_uploads/{filename}',
                    'filename': filename,
                    'prompt': prompt,
                    'size': '1024x1024',
                    'mock_mode': True,
                    'message': 'Mock image generated. Real API key needed for actual AI images.',
                    'timestamp': datetime.now().isoformat()
                })

            except ImportError:
                # Fallback if PIL is not available
                return jsonify({
                    'success': False,
                    'error': 'Mock image generation requires PIL library. Install with: pip install Pillow',
                    'mock_mode': True,
                    'timestamp': datetime.now().isoformat()
                })
        else:
            # Try OpenAI first, then fallback to Replicate
            print("🚀 Trying OpenAI DALL-E API first...")
            image_b64 = generate_image_openai(prompt, size)

            # If OpenAI fails, try Replicate
            if not image_b64 and REPLICATE_API_TOKEN:
                print("🔄 OpenAI failed, trying Replicate API...")
                image_b64 = generate_image_replicate(prompt)

            if image_b64:
                # Save image to temp folder
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"generated_{timestamp}.png"
                filepath = os.path.join('temp_uploads', filename)

                # Create temp_uploads directory if it doesn't exist
                os.makedirs('temp_uploads', exist_ok=True)

                # Save image
                with open(filepath, "wb") as f:
                    f.write(base64.b64decode(image_b64))

                return jsonify({
                    'success': True,
                    'image_url': f'/temp_uploads/{filename}',
                    'filename': filename,
                    'prompt': prompt,
                    'size': size,
                    'timestamp': datetime.now().isoformat()
                })
            else:
                return jsonify({
                    'success': False,
                    'error': 'Failed to generate image. Check API key and billing.',
                    'timestamp': datetime.now().isoformat()
                }), 500

    except Exception as e:
        print(f"❌ Image generation API error: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/generate-image', methods=['POST'])
def generate_image_api():
    """Generate image using DALL-E API"""
    try:
        data = request.get_json()

        if not data or 'prompt' not in data:
            return jsonify({'error': 'Prompt is required'}), 400

        prompt = data['prompt']
        size = data.get('size', '1024x1024')
        user_id = data.get('user_id', 'anonymous')

        print(f"🎨 Image generation request from {user_id}: {prompt[:50]}...")

        if USE_MOCK_RESPONSES or not OPENAI_API_KEY:
            # Mock response for image generation
            print("🤖 Using mock image response")
            return jsonify({
                'success': False,
                'error': 'Image generation is in mock mode. Real API key needed.',
                'mock_mode': True,
                'timestamp': datetime.now().isoformat()
            })
        else:
            # Use OpenAI DALL-E API
            print("🚀 Using OpenAI DALL-E API")
            image_b64 = generate_image_openai(prompt, size)

            if image_b64:
                # Save image to temp folder
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"generated_{timestamp}.png"
                filepath = os.path.join('temp_uploads', filename)

                # Create temp_uploads directory if it doesn't exist
                os.makedirs('temp_uploads', exist_ok=True)

                # Save image
                with open(filepath, "wb") as f:
                    f.write(base64.b64decode(image_b64))

                return jsonify({
                    'success': True,
                    'image_url': f'/temp_uploads/{filename}',
                    'filename': filename,
                    'prompt': prompt,
                    'size': size,
                    'timestamp': datetime.now().isoformat()
                })
            else:
                return jsonify({
                    'success': False,
                    'error': 'Failed to generate image. Check API key and billing.',
                    'timestamp': datetime.now().isoformat()
                }), 500

    except Exception as e:
        print(f"❌ Image generation API error: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/health', methods=['GET'])
def health():
    """Health check endpoint"""
    return jsonify({
        'status': 'healthy',
        'timestamp': datetime.now().isoformat(),
        'version': '1.0.0',
        'mock_mode': USE_MOCK_RESPONSES
    })

def create_realistic_cat_otter_image():
    """Create realistic mock image of cat and otter"""
    from PIL import Image, ImageDraw, ImageFont, ImageFilter
    import random

    # Create high-quality image
    img = Image.new('RGB', (1024, 1024), color=(255, 255, 255))
    draw = ImageDraw.Draw(img)

    # Create natural background (grass and sky)
    # Sky gradient
    for y in range(400):
        blue_intensity = int(135 + (200 - 135) * y / 400)
        sky_color = (135, 206, blue_intensity)
        draw.line([(0, y), (1024, y)], fill=sky_color)

    # Grass area
    for y in range(400, 1024):
        green_base = int(34 + random.randint(-10, 10))
        green_intensity = int(139 + random.randint(-20, 20))
        grass_color = (green_base, green_intensity, 34)
        draw.line([(0, y), (1024, y)], fill=grass_color)

    # Draw cat body (gray tabby)
    cat_x, cat_y = 350, 600
    cat_body_color = (128, 128, 128)  # Gray
    cat_stripe_color = (64, 64, 64)   # Darker gray for stripes

    # Cat body (oval)
    draw.ellipse([cat_x - 80, cat_y - 60, cat_x + 80, cat_y + 60], fill=cat_body_color)
    # Cat head
    draw.ellipse([cat_x - 50, cat_y - 120, cat_x + 50, cat_y - 40], fill=cat_body_color)
    # Cat ears
    draw.polygon([(cat_x - 40, cat_y - 110), (cat_x - 20, cat_y - 140), (cat_x - 10, cat_y - 110)], fill=cat_body_color)
    draw.polygon([(cat_x + 10, cat_y - 110), (cat_x + 20, cat_y - 140), (cat_x + 40, cat_y - 110)], fill=cat_body_color)

    # Cat stripes (tabby pattern)
    for i in range(5):
        stripe_y = cat_y - 40 + i * 20
        draw.ellipse([cat_x - 70, stripe_y - 5, cat_x + 70, stripe_y + 5], fill=cat_stripe_color)

    # Cat legs and tail
    draw.ellipse([cat_x - 60, cat_y + 40, cat_x - 30, cat_y + 100], fill=cat_body_color)
    draw.ellipse([cat_x + 30, cat_y + 40, cat_x + 60, cat_y + 100], fill=cat_body_color)
    draw.ellipse([cat_x + 70, cat_y - 20, cat_x + 120, cat_y + 20], fill=cat_body_color)

    # Cat face features
    draw.ellipse([cat_x - 25, cat_y - 90, cat_x - 15, cat_y - 80], fill=(0, 255, 0))  # Green eyes
    draw.ellipse([cat_x + 15, cat_y - 90, cat_x + 25, cat_y - 80], fill=(0, 255, 0))
    draw.ellipse([cat_x - 22, cat_y - 87, cat_x - 18, cat_y - 83], fill=(0, 0, 0))  # Pupils
    draw.ellipse([cat_x + 18, cat_y - 87, cat_x + 22, cat_y - 83], fill=(0, 0, 0))
    draw.polygon([(cat_x - 3, cat_y - 75), (cat_x + 3, cat_y - 75), (cat_x, cat_y - 70)], fill=(255, 192, 203))  # Nose

    # Draw otter
    otter_x, otter_y = 650, 650
    otter_color = (101, 67, 33)  # Brown

    # Otter body and head
    draw.ellipse([otter_x - 70, otter_y - 50, otter_x + 70, otter_y + 50], fill=otter_color)
    draw.ellipse([otter_x - 45, otter_y - 100, otter_x + 45, otter_y - 30], fill=otter_color)

    # Otter ears
    draw.ellipse([otter_x - 35, otter_y - 95, otter_x - 25, otter_y - 85], fill=otter_color)
    draw.ellipse([otter_x + 25, otter_y - 95, otter_x + 35, otter_y - 85], fill=otter_color)

    # Otter legs and tail
    draw.ellipse([otter_x - 60, otter_y + 30, otter_x - 30, otter_y + 80], fill=otter_color)
    draw.ellipse([otter_x + 30, otter_y + 30, otter_x + 60, otter_y + 80], fill=otter_color)
    draw.ellipse([otter_x + 60, otter_y - 10, otter_x + 110, otter_y + 30], fill=otter_color)

    # Otter face
    draw.ellipse([otter_x - 20, otter_y - 75, otter_x - 10, otter_y - 65], fill=(0, 0, 0))  # Eyes
    draw.ellipse([otter_x + 10, otter_y - 75, otter_x + 20, otter_y - 65], fill=(0, 0, 0))
    draw.ellipse([otter_x - 5, otter_y - 60, otter_x + 5, otter_y - 50], fill=(0, 0, 0))  # Nose

    # Orange scarf on otter
    scarf_color = (255, 165, 0)  # Orange
    draw.ellipse([otter_x - 50, otter_y - 45, otter_x + 50, otter_y - 25], fill=scarf_color)
    draw.ellipse([otter_x + 40, otter_y - 35, otter_x + 80, otter_y - 15], fill=scarf_color)
    draw.ellipse([otter_x + 70, otter_y - 25, otter_x + 90, otter_y + 15], fill=scarf_color)

    # Show hugging - cat's paw on otter
    draw.ellipse([cat_x + 60, cat_y - 20, cat_x + 120, cat_y + 20], fill=cat_body_color)
    draw.ellipse([cat_x + 110, cat_y - 10, cat_x + 140, cat_y + 30], fill=cat_body_color)

    # Add flowers
    flower_colors = [(255, 192, 203), (255, 255, 0), (255, 0, 255), (0, 255, 255)]
    for i in range(8):
        fx = random.randint(50, 950)
        fy = random.randint(450, 950)
        flower_color = random.choice(flower_colors)
        for petal in range(5):
            angle = petal * 72
            px = fx + 15 * (1 if angle < 180 else -1)
            py = fy + 15 * (1 if 90 < angle < 270 else -1)
            draw.ellipse([px - 8, py - 8, px + 8, py + 8], fill=flower_color)
        draw.ellipse([fx - 5, fy - 5, fx + 5, fy + 5], fill=(255, 255, 0))

    # Add text
    try:
        font_large = ImageFont.truetype("arial.ttf", 36)
        font_small = ImageFont.truetype("arial.ttf", 24)
    except:
        font_large = ImageFont.load_default()
        font_small = ImageFont.load_default()

    title = "🐱 Gray Tabby Cat Hugging Otter 🦦"
    title_bbox = draw.textbbox((0, 0), title, font=font_large)
    title_width = title_bbox[2] - title_bbox[0]
    draw.text(((1024 - title_width) // 2 + 2, 52), title, fill=(0, 0, 0), font=font_large)  # Shadow
    draw.text(((1024 - title_width) // 2, 50), title, fill=(255, 255, 255), font=font_large)

    subtitle = "🧡 With Orange Scarf - High Quality Mock 🧡"
    subtitle_bbox = draw.textbbox((0, 0), subtitle, font=font_small)
    subtitle_width = subtitle_bbox[2] - subtitle_bbox[0]
    draw.text(((1024 - subtitle_width) // 2, 100), subtitle, fill=(255, 255, 255), font=font_small)

    return img

if __name__ == '__main__':
    print("🚀 Starting Tincada AI Flask Server...")
    print(f"📊 Mock responses: {'Enabled' if USE_MOCK_RESPONSES else 'Disabled'}")
    print(f"🔑 OpenAI API: {'Configured' if OPENAI_API_KEY else 'Not configured'}")
    print("🌐 Server will be available at: http://localhost:5001")
    
    app.run(host='0.0.0.0', port=5001, debug=True)
