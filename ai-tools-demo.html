<!DOCTYPE html>
<html lang="so">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Tools Demo - Tincada AI</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
            padding: 2rem;
            margin: 0;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 3rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        h1 {
            text-align: center;
            font-size: 2.5rem;
            margin-bottom: 1rem;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .subtitle {
            text-align: center;
            font-size: 1.2rem;
            margin-bottom: 3rem;
            opacity: 0.9;
        }
        
        .tools-showcase {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 2rem;
            margin-bottom: 3rem;
        }
        
        .tool-card {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 2rem;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .tool-card:hover {
            transform: translateY(-5px);
            border-color: rgba(255, 255, 255, 0.2);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);
        }
        
        .tool-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 4px;
            height: 100%;
            background: linear-gradient(45deg, #10a37f, #0d8f6f);
            transform: scaleY(0);
            transition: transform 0.3s ease;
        }
        
        .tool-card:hover::before {
            transform: scaleY(1);
        }
        
        .tool-icon {
            width: 70px;
            height: 70px;
            background: linear-gradient(45deg, #10a37f, #0d8f6f);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.8rem;
            margin-bottom: 1.5rem;
            color: white;
            animation: toolIconFloat 3s ease-in-out infinite;
        }
        
        @keyframes toolIconFloat {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-5px); }
        }
        
        .tool-card h3 {
            color: #ffffff;
            font-size: 1.3rem;
            margin-bottom: 1rem;
        }
        
        .tool-card p {
            color: #cccccc;
            line-height: 1.6;
            margin-bottom: 1.5rem;
        }
        
        .tool-features {
            list-style: none;
            padding: 0;
            margin-bottom: 1.5rem;
        }
        
        .tool-features li {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 8px;
            color: #cccccc;
            font-size: 0.9rem;
        }
        
        .tool-features li i {
            color: #10a37f;
            font-size: 0.8rem;
        }
        
        .demo-section {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            border-left: 4px solid #4ecdc4;
        }
        
        .demo-section h2 {
            color: #4ecdc4;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .tools-button-demo {
            display: flex;
            justify-content: center;
            margin: 2rem 0;
        }
        
        .demo-tools-btn {
            background: linear-gradient(45deg, #10a37f, #0d8f6f);
            color: white;
            border: 2px solid rgba(16, 163, 127, 0.3);
            border-radius: 8px;
            padding: 12px 20px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 10px;
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            position: relative;
            overflow: hidden;
        }
        
        .demo-tools-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
            transition: left 0.6s ease;
        }
        
        .demo-tools-btn:hover::before {
            left: 100%;
        }
        
        .demo-tools-btn:hover {
            background: linear-gradient(45deg, #0d8f6f, #0a7a5e);
            transform: translateY(-4px) scale(1.1);
            box-shadow: 
                0 10px 35px rgba(16, 163, 127, 0.8),
                0 0 20px rgba(16, 163, 127, 0.5);
            border-color: rgba(16, 163, 127, 0.8);
            animation: demoGlow 1.5s ease infinite alternate;
        }
        
        @keyframes demoGlow {
            0% {
                box-shadow: 0 10px 35px rgba(16, 163, 127, 0.8), 0 0 20px rgba(16, 163, 127, 0.5);
            }
            100% {
                box-shadow: 0 12px 40px rgba(16, 163, 127, 0.9), 0 0 25px rgba(16, 163, 127, 0.7);
            }
        }
        
        .demo-tools-btn i {
            transition: all 0.3s ease;
        }
        
        .demo-tools-btn:hover i {
            transform: rotate(15deg) scale(1.1);
            text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
        }
        
        .success {
            background: rgba(76, 175, 80, 0.2);
            border: 1px solid rgba(76, 175, 80, 0.3);
            border-radius: 10px;
            padding: 1.5rem;
            margin: 2rem 0;
            border-left: 4px solid #4caf50;
            text-align: center;
        }
        
        .demo-buttons {
            display: flex;
            gap: 20px;
            justify-content: center;
            margin-top: 3rem;
            flex-wrap: wrap;
        }
        
        .demo-btn {
            background: linear-gradient(45deg, #4ecdc4, #44a08d);
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 25px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 10px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(78, 205, 196, 0.3);
        }
        
        .demo-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(78, 205, 196, 0.4);
        }
        
        .demo-btn.secondary {
            background: linear-gradient(45deg, #ff6b6b, #ee5a52);
            box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
        }
        
        .demo-btn.secondary:hover {
            box-shadow: 0 6px 20px rgba(255, 107, 107, 0.4);
        }
        
        @media (max-width: 768px) {
            body { padding: 1rem; }
            .container { padding: 2rem 1rem; }
            h1 { font-size: 2rem; }
            .tools-showcase { grid-template-columns: 1fr; }
            .demo-buttons { flex-direction: column; align-items: center; }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🛠️ AI Tools System</h1>
        <p class="subtitle">Professional AI tools oo leh enhanced hover effects iyo comprehensive functionality</p>
        
        <div class="tools-showcase">
            <div class="tool-card">
                <div class="tool-icon">
                    <i class="fas fa-lightbulb"></i>
                </div>
                <h3>Think Longer</h3>
                <p>Extended reasoning iyo deep analysis for complex problems with multi-perspective thinking.</p>
                <ul class="tool-features">
                    <li><i class="fas fa-check"></i> Step-by-step analysis</li>
                    <li><i class="fas fa-check"></i> Multiple perspectives</li>
                    <li><i class="fas fa-check"></i> Comprehensive solutions</li>
                    <li><i class="fas fa-check"></i> Analysis depth control</li>
                </ul>
            </div>
            
            <div class="tool-card">
                <div class="tool-icon">
                    <i class="fas fa-search"></i>
                </div>
                <h3>Deep Research</h3>
                <p>Comprehensive research oo leh multiple sources, citations, iyo detailed analysis.</p>
                <ul class="tool-features">
                    <li><i class="fas fa-check"></i> Multiple source types</li>
                    <li><i class="fas fa-check"></i> Academic papers</li>
                    <li><i class="fas fa-check"></i> Industry reports</li>
                    <li><i class="fas fa-check"></i> Current trends</li>
                </ul>
            </div>
            
            <div class="tool-card">
                <div class="tool-icon">
                    <i class="fas fa-image"></i>
                </div>
                <h3>Create Image</h3>
                <p>AI-powered image generation from text descriptions oo leh multiple styles.</p>
                <ul class="tool-features">
                    <li><i class="fas fa-check"></i> Text-to-image generation</li>
                    <li><i class="fas fa-check"></i> Multiple art styles</li>
                    <li><i class="fas fa-check"></i> Custom dimensions</li>
                    <li><i class="fas fa-check"></i> High-quality output</li>
                </ul>
            </div>
            
            <div class="tool-card">
                <div class="tool-icon">
                    <i class="fas fa-globe"></i>
                </div>
                <h3>Web Search</h3>
                <p>Real-time internet search oo leh current information iyo multiple search types.</p>
                <ul class="tool-features">
                    <li><i class="fas fa-check"></i> Real-time search</li>
                    <li><i class="fas fa-check"></i> News & articles</li>
                    <li><i class="fas fa-check"></i> Academic sources</li>
                    <li><i class="fas fa-check"></i> Regional filtering</li>
                </ul>
            </div>
            
            <div class="tool-card">
                <div class="tool-icon">
                    <i class="fas fa-paint-brush"></i>
                </div>
                <h3>Canvas</h3>
                <p>Interactive workspace for visual collaboration, document editing, iyo creative projects.</p>
                <ul class="tool-features">
                    <li><i class="fas fa-check"></i> Real-time collaboration</li>
                    <li><i class="fas fa-check"></i> Multiple canvas types</li>
                    <li><i class="fas fa-check"></i> Export options</li>
                    <li><i class="fas fa-check"></i> Version history</li>
                </ul>
            </div>
        </div>
        
        <div class="demo-section">
            <h2>🎨 Enhanced Tools Button</h2>
            <p>Tools button oo leh special hover effects iyo animations:</p>
            
            <div class="tools-button-demo">
                <button class="demo-tools-btn">
                    <i class="fas fa-tools"></i>
                    <span>AI Tools</span>
                </button>
            </div>
            
            <div style="background: rgba(255,255,255,0.05); padding: 1.5rem; border-radius: 10px; margin-top: 1rem;">
                <h4 style="color: #10a37f; margin-bottom: 1rem;">✨ Hover Effects:</h4>
                <ul style="color: #cccccc; line-height: 1.8; list-style: none; padding: 0;">
                    <li>🌟 <strong>Glow Animation:</strong> Pulsing glow effect</li>
                    <li>🎭 <strong>Scale Transform:</strong> Button grows on hover</li>
                    <li>💫 <strong>Shimmer Effect:</strong> Light sweep animation</li>
                    <li>🔄 <strong>Icon Rotation:</strong> Tools icon rotates</li>
                    <li>📈 <strong>Elevation:</strong> 3D lift effect</li>
                    <li>🎨 <strong>Color Transition:</strong> Smooth gradient change</li>
                </ul>
            </div>
        </div>
        
        <div class="success">
            <h3>🎉 AI Tools System Complete!</h3>
            <p><strong>5 Professional Tools:</strong> Think Longer, Deep Research, Create Image, Web Search, Canvas</p>
            <p><strong>Enhanced UI:</strong> Beautiful hover effects, animations, iyo professional styling</p>
            <p><strong>Full Functionality:</strong> Complete forms, validation, iyo tool execution</p>
        </div>
        
        <div class="demo-buttons">
            <a href="dashboard.html" class="demo-btn">
                🛠️ Test AI Tools
            </a>
            <a href="help-customize-demo.html" class="demo-btn secondary">
                🛠️ Help & Customize
            </a>
            <a href="features-demo.html" class="demo-btn secondary">
                🚀 All Features
            </a>
        </div>
    </div>
</body>
</html>
