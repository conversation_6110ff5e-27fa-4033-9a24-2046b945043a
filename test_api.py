#!/usr/bin/env python3
"""
Test script for Tincada AI API
Tests chat functionality and AI tools
"""

import requests
import json
import time

# API base URL
BASE_URL = "http://localhost:5001"

def test_health_check():
    """Test health check endpoint"""
    print("🔧 Testing health check...")
    try:
        response = requests.get(f"{BASE_URL}/api/health")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Health check passed")
            print(f"   Status: {data['status']}")
            print(f"   OpenAI Available: {data['openai_available']}")
            return True
        else:
            print(f"❌ Health check failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Health check error: {e}")
        return False

def test_chat_api():
    """Test chat API endpoint"""
    print("\n💬 Testing chat API...")
    
    test_messages = [
        "Hello, how are you?",
        "Maxaad ka fikirtaa AI-ga?",
        "Can you help me with Python programming?",
        "Waxaan rabaa in aan barto JavaScript"
    ]
    
    for i, message in enumerate(test_messages, 1):
        print(f"\n📝 Test {i}: {message}")
        try:
            response = requests.post(f"{BASE_URL}/api/chat", 
                json={
                    "message": message,
                    "user_id": "test_user_123"
                },
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ Response received")
                print(f"   Source: {data.get('source', 'unknown')}")
                print(f"   Response: {data['response'][:100]}...")
                if 'tokens_used' in data:
                    print(f"   Tokens: {data['tokens_used']}")
            else:
                print(f"❌ Chat API failed: {response.status_code}")
                print(f"   Error: {response.text}")
                
        except Exception as e:
            print(f"❌ Chat API error: {e}")
        
        time.sleep(1)  # Rate limiting

def test_tools_api():
    """Test AI tools API endpoints"""
    print("\n🛠️ Testing AI tools...")
    
    tools_tests = [
        {
            "tool": "think-longer",
            "data": {
                "prompt": "How can we solve climate change?",
                "depth": "comprehensive",
                "user_id": "test_user_123"
            }
        },
        {
            "tool": "deep-research", 
            "data": {
                "topic": "Artificial Intelligence trends 2024",
                "scope": "detailed",
                "sources": "all",
                "user_id": "test_user_123"
            }
        },
        {
            "tool": "create-image",
            "data": {
                "prompt": "A beautiful sunset over mountains",
                "style": "realistic",
                "size": "square",
                "user_id": "test_user_123"
            }
        },
        {
            "tool": "web-search",
            "data": {
                "query": "latest AI developments",
                "type": "general",
                "region": "global",
                "user_id": "test_user_123"
            }
        },
        {
            "tool": "canvas",
            "data": {
                "type": "document",
                "title": "Test Canvas",
                "description": "Testing canvas creation",
                "user_id": "test_user_123"
            }
        }
    ]
    
    for test in tools_tests:
        tool_name = test["tool"]
        print(f"\n🔧 Testing {tool_name}...")
        
        try:
            response = requests.post(f"{BASE_URL}/api/tools/{tool_name}",
                json=test["data"],
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ {tool_name} tool worked")
                print(f"   Response: {data['response'][:100]}...")
            else:
                print(f"❌ {tool_name} tool failed: {response.status_code}")
                print(f"   Error: {response.text}")
                
        except Exception as e:
            print(f"❌ {tool_name} tool error: {e}")
        
        time.sleep(1)  # Rate limiting

def test_rate_limiting():
    """Test rate limiting functionality"""
    print("\n⚡ Testing rate limiting...")
    
    # Send multiple requests quickly
    for i in range(5):
        try:
            response = requests.post(f"{BASE_URL}/api/chat",
                json={
                    "message": f"Test message {i+1}",
                    "user_id": "rate_limit_test_user"
                },
                timeout=10
            )
            
            if response.status_code == 200:
                print(f"✅ Request {i+1} successful")
            elif response.status_code == 429:
                print(f"⚠️ Request {i+1} rate limited (expected)")
                break
            else:
                print(f"❌ Request {i+1} failed: {response.status_code}")
                
        except Exception as e:
            print(f"❌ Rate limit test error: {e}")

def main():
    """Run all tests"""
    print("🚀 Starting Tincada AI API Tests")
    print("=" * 50)
    
    # Test health check first
    if not test_health_check():
        print("❌ Health check failed. Make sure server is running on port 5001")
        return
    
    # Test chat API
    test_chat_api()
    
    # Test tools API
    test_tools_api()
    
    # Test rate limiting
    test_rate_limiting()
    
    print("\n" + "=" * 50)
    print("🎉 API tests completed!")
    print("\n📋 Summary:")
    print("   - Health check: API status and OpenAI availability")
    print("   - Chat API: Multiple language support and responses")
    print("   - Tools API: All 5 AI tools functionality")
    print("   - Rate limiting: Protection against abuse")
    print("\n💡 Next steps:")
    print("   1. Configure OpenAI API key for enhanced responses")
    print("   2. Test with real API key")
    print("   3. Deploy to production server")

if __name__ == "__main__":
    main()
