<?php
/**
 * 🧪 TINCADA AI COMPLETE SYSTEM TEST
 * Test all components of the restored system
 */

echo "<h1>🧪 Tincada AI System Test</h1>";
echo "<style>
body { font-family: Arial, sans-serif; margin: 20px; }
.test { margin: 10px 0; padding: 10px; border-radius: 5px; }
.pass { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
.fail { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
.info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
</style>";

// Test 1: Check if files exist
echo "<h2>📁 File Existence Test</h2>";

$required_files = [
    'dashboard.php' => 'PHP Dashboard',
    'database_config.php' => 'Database Configuration',
    'api_endpoints.php' => 'API Endpoints',
    'simple_api.php' => 'Simple API Fallback',
    'tincada_database.sql' => 'Database Schema',
    '.htaccess' => 'URL Routing',
    'index.html' => 'Main Interface',
    'styles.css' => 'Styling',
    'script.js' => 'Frontend Logic'
];

foreach ($required_files as $file => $description) {
    if (file_exists($file)) {
        echo "<div class='test pass'>✅ $description ($file) - EXISTS</div>";
    } else {
        echo "<div class='test fail'>❌ $description ($file) - MISSING</div>";
    }
}

// Test 2: PHP Configuration
echo "<h2>🔧 PHP Configuration Test</h2>";

$php_version = phpversion();
echo "<div class='test info'>📋 PHP Version: $php_version</div>";

$required_extensions = ['pdo', 'pdo_mysql', 'curl', 'json'];
foreach ($required_extensions as $ext) {
    if (extension_loaded($ext)) {
        echo "<div class='test pass'>✅ PHP Extension: $ext - LOADED</div>";
    } else {
        echo "<div class='test fail'>❌ PHP Extension: $ext - NOT LOADED</div>";
    }
}

// Test 3: Database Connection Test
echo "<h2>🗄️ Database Connection Test</h2>";

try {
    require_once 'database_config.php';
    
    if (isset($database) && $database !== null) {
        echo "<div class='test pass'>✅ Database Connection - SUCCESS</div>";
        
        // Test database tables
        $tables = ['users', 'conversations', 'chat_messages', 'payments'];
        foreach ($tables as $table) {
            try {
                $stmt = $db->query("SELECT COUNT(*) FROM $table");
                $count = $stmt->fetchColumn();
                echo "<div class='test pass'>✅ Table '$table' - EXISTS ($count records)</div>";
            } catch (Exception $e) {
                echo "<div class='test fail'>❌ Table '$table' - ERROR: " . $e->getMessage() . "</div>";
            }
        }
        
    } else {
        echo "<div class='test fail'>❌ Database Connection - FAILED</div>";
        echo "<div class='test info'>💡 Using fallback mode without database</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='test fail'>❌ Database Connection - ERROR: " . $e->getMessage() . "</div>";
    echo "<div class='test info'>💡 This is normal if MySQL is not running</div>";
}

// Test 4: API Endpoints Test
echo "<h2>🔌 API Endpoints Test</h2>";

// Test simple API health
try {
    $health_url = 'http://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . '/simple_api.php/health';
    
    $ch = curl_init($health_url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 5);
    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($http_code === 200 && $response) {
        $data = json_decode($response, true);
        if ($data && isset($data['success']) && $data['success']) {
            echo "<div class='test pass'>✅ Simple API Health - SUCCESS</div>";
        } else {
            echo "<div class='test fail'>❌ Simple API Health - INVALID RESPONSE</div>";
        }
    } else {
        echo "<div class='test fail'>❌ Simple API Health - HTTP $http_code</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='test fail'>❌ Simple API Health - ERROR: " . $e->getMessage() . "</div>";
}

// Test 5: File Permissions
echo "<h2>🔐 File Permissions Test</h2>";

$writable_dirs = ['temp_uploads', 'images'];
foreach ($writable_dirs as $dir) {
    if (is_dir($dir)) {
        if (is_writable($dir)) {
            echo "<div class='test pass'>✅ Directory '$dir' - WRITABLE</div>";
        } else {
            echo "<div class='test fail'>❌ Directory '$dir' - NOT WRITABLE</div>";
        }
    } else {
        echo "<div class='test info'>📁 Directory '$dir' - DOES NOT EXIST (will be created if needed)</div>";
    }
}

// Test 6: OpenAI API Configuration
echo "<h2>🤖 OpenAI API Test</h2>";

if (file_exists('simple_api.php')) {
    $api_content = file_get_contents('simple_api.php');
    if (strpos($api_content, 'sk-proj-') !== false) {
        echo "<div class='test pass'>✅ OpenAI API Key - CONFIGURED</div>";
    } else {
        echo "<div class='test fail'>❌ OpenAI API Key - NOT CONFIGURED</div>";
    }
} else {
    echo "<div class='test fail'>❌ Simple API File - NOT FOUND</div>";
}

// Test 7: Frontend Files Test
echo "<h2>🎨 Frontend Files Test</h2>";

$frontend_files = [
    'index.html' => 'Main Interface',
    'styles.css' => 'Styling',
    'script.js' => 'JavaScript Logic'
];

foreach ($frontend_files as $file => $description) {
    if (file_exists($file)) {
        $size = filesize($file);
        echo "<div class='test pass'>✅ $description ($file) - EXISTS (" . number_format($size) . " bytes)</div>";
    } else {
        echo "<div class='test fail'>❌ $description ($file) - MISSING</div>";
    }
}

// Test 8: Server Configuration
echo "<h2>⚙️ Server Configuration Test</h2>";

echo "<div class='test info'>📋 Server Software: " . $_SERVER['SERVER_SOFTWARE'] . "</div>";
echo "<div class='test info'>📋 Document Root: " . $_SERVER['DOCUMENT_ROOT'] . "</div>";
echo "<div class='test info'>📋 Current Directory: " . getcwd() . "</div>";

if (function_exists('apache_get_modules')) {
    $modules = apache_get_modules();
    if (in_array('mod_rewrite', $modules)) {
        echo "<div class='test pass'>✅ Apache mod_rewrite - ENABLED</div>";
    } else {
        echo "<div class='test fail'>❌ Apache mod_rewrite - DISABLED</div>";
    }
} else {
    echo "<div class='test info'>📋 Apache modules check - NOT AVAILABLE</div>";
}

// Test 9: Memory and Performance
echo "<h2>⚡ Performance Test</h2>";

$memory_limit = ini_get('memory_limit');
$max_execution_time = ini_get('max_execution_time');
$upload_max_filesize = ini_get('upload_max_filesize');

echo "<div class='test info'>📋 Memory Limit: $memory_limit</div>";
echo "<div class='test info'>📋 Max Execution Time: {$max_execution_time}s</div>";
echo "<div class='test info'>📋 Upload Max Filesize: $upload_max_filesize</div>";

// Test 10: System Summary
echo "<h2>📊 System Summary</h2>";

$total_tests = 0;
$passed_tests = 0;

// Count tests (this is a simplified count)
$test_results = [
    'Files' => file_exists('dashboard.php') && file_exists('database_config.php'),
    'PHP' => version_compare(PHP_VERSION, '7.4.0', '>='),
    'Extensions' => extension_loaded('pdo') && extension_loaded('curl'),
    'Frontend' => file_exists('index.html') && file_exists('styles.css'),
    'API' => file_exists('api_endpoints.php') && file_exists('simple_api.php')
];

foreach ($test_results as $test => $result) {
    $total_tests++;
    if ($result) {
        $passed_tests++;
    }
}

$success_rate = round(($passed_tests / $total_tests) * 100, 1);

if ($success_rate >= 80) {
    echo "<div class='test pass'>🎉 System Status: READY ($success_rate% tests passed)</div>";
    echo "<div class='test pass'>✅ System is ready for use!</div>";
} elseif ($success_rate >= 60) {
    echo "<div class='test info'>⚠️ System Status: PARTIAL ($success_rate% tests passed)</div>";
    echo "<div class='test info'>💡 Some features may not work properly</div>";
} else {
    echo "<div class='test fail'>❌ System Status: NEEDS SETUP ($success_rate% tests passed)</div>";
    echo "<div class='test fail'>🔧 Please fix the issues above</div>";
}

// Test 11: Quick Start Instructions
echo "<h2>🚀 Quick Start Instructions</h2>";

echo "<div class='test info'>
<strong>To start using Tincada AI:</strong><br>
1. <strong>For PHP/MySQL:</strong> Install XAMPP, start Apache+MySQL, import database<br>
2. <strong>For Simple Mode:</strong> Use Python HTTP server: <code>python -m http.server 8000</code><br>
3. <strong>Access Main Chat:</strong> <a href='index.html' target='_blank'>index.html</a><br>
4. <strong>Access Dashboard:</strong> <a href='dashboard.php' target='_blank'>dashboard.php</a><br>
5. <strong>Test API:</strong> <a href='simple_api.php/health' target='_blank'>simple_api.php/health</a>
</div>";

echo "<div class='test info'>
<strong>Troubleshooting:</strong><br>
• If database fails: Use simple_api.php for basic functionality<br>
• If PHP fails: Use Python server for HTML/JS only<br>
• Check browser console for JavaScript errors<br>
• Verify file permissions for uploads
</div>";

echo "<h2>✅ Test Complete!</h2>";
echo "<p><strong>Tincada AI system test completed at " . date('Y-m-d H:i:s') . "</strong></p>";
?>
