#!/usr/bin/env python3
"""
Test professional AI responses with accurate information
"""

import requests
import json

def test_professional_ai():
    """Test AI for professional, accurate responses"""
    
    base_url = "http://localhost:5001"
    
    tests = [
        {
            "question": "What is artificial intelligence?",
            "expected_keywords": ["machine learning", "computer science", "intelligent", "algorithms"],
            "language": "English"
        },
        {
            "question": "Maxaa ka dhigan AI?",
            "expected_keywords": ["teknooloji", "mashiinnada", "barashada", "aqoon"],
            "language": "Somali"
        },
        {
            "question": "Write a Python function to calculate fibonacci numbers",
            "expected_keywords": ["def", "fibonacci", "python", "function", "iterative"],
            "language": "English"
        },
        {
            "question": "Explain quantum physics in simple terms",
            "expected_keywords": ["quantum", "physics", "particles", "wave", "uncertainty"],
            "language": "English"
        },
        {
            "question": "Hello, how can you help me?",
            "expected_keywords": ["programming", "science", "help", "assist", "topics"],
            "language": "English"
        }
    ]
    
    print("🧪 Testing Professional AI Responses")
    print("=" * 60)
    
    passed_tests = 0
    total_tests = len(tests)
    
    for i, test in enumerate(tests, 1):
        print(f"\n📝 Test {i}: {test['question']}")
        print(f"🌐 Expected Language: {test['language']}")
        print("-" * 50)
        
        try:
            response = requests.post(f"{base_url}/api/chat", json={
                "message": test['question'],
                "user_id": "test_user",
                "chat_history": []
            })
            
            if response.status_code == 200:
                data = response.json()
                ai_response = data.get('response', 'No response')
                
                # Check response length (professional responses should be detailed)
                if len(ai_response) > 100:
                    print("✅ Response is detailed and comprehensive")
                    
                    # Check for expected keywords
                    response_lower = ai_response.lower()
                    found_keywords = [kw for kw in test['expected_keywords'] if kw.lower() in response_lower]
                    
                    if len(found_keywords) >= 2:  # At least 2 keywords should match
                        print(f"✅ Contains relevant keywords: {found_keywords}")
                        print(f"✅ Response preview: {ai_response[:150]}...")
                        passed_tests += 1
                        print("🎯 TEST PASSED")
                    else:
                        print(f"⚠️ Missing expected keywords. Found: {found_keywords}")
                        print(f"📝 Response: {ai_response[:200]}...")
                        print("❌ TEST FAILED")
                else:
                    print("❌ Response too short - not professional enough")
                    print(f"📝 Response: {ai_response}")
                    print("❌ TEST FAILED")
                    
            else:
                print(f"❌ API Error: {response.status_code}")
                print("❌ TEST FAILED")
                
        except Exception as e:
            print(f"❌ Request failed: {e}")
            print("❌ TEST FAILED")
    
    print("\n" + "=" * 60)
    print("📊 FINAL RESULTS:")
    print(f"✅ Passed: {passed_tests}/{total_tests}")
    print(f"❌ Failed: {total_tests - passed_tests}/{total_tests}")
    print(f"📈 Success Rate: {(passed_tests/total_tests)*100:.1f}%")
    
    if passed_tests == total_tests:
        print("🎉 ALL TESTS PASSED! AI provides professional, accurate responses!")
    elif passed_tests >= total_tests * 0.8:
        print("✅ MOSTLY SUCCESSFUL! AI provides good professional responses!")
    else:
        print("⚠️ NEEDS IMPROVEMENT! AI responses need to be more professional!")
    
    return passed_tests == total_tests

if __name__ == "__main__":
    success = test_professional_ai()
    if success:
        print("\n🎯 AI is ready for professional use with accurate information!")
    else:
        print("\n🔧 AI needs adjustments to provide better responses!")
