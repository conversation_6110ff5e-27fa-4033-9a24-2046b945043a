<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tincada AI - Database Dashboard</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .nav-tabs {
            display: flex;
            background: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
        }
        
        .nav-tab {
            flex: 1;
            padding: 15px 20px;
            text-align: center;
            background: none;
            border: none;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s ease;
        }
        
        .nav-tab.active {
            background: white;
            border-bottom: 3px solid #4facfe;
            color: #4facfe;
        }
        
        .nav-tab:hover {
            background: #e9ecef;
        }
        
        .tab-content {
            padding: 30px;
            min-height: 500px;
        }
        
        .tab-pane {
            display: none;
        }
        
        .tab-pane.active {
            display: block;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 25px;
            border-radius: 10px;
            text-align: center;
        }
        
        .stat-card h3 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .stat-card p {
            font-size: 1.1em;
            opacity: 0.9;
        }
        
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .data-table th {
            background: #4facfe;
            color: white;
            padding: 15px;
            text-align: left;
            font-weight: 600;
        }
        
        .data-table td {
            padding: 12px 15px;
            border-bottom: 1px solid #eee;
        }
        
        .data-table tr:hover {
            background: #f8f9fa;
        }
        
        .chat-interface {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .chat-messages {
            height: 300px;
            overflow-y: auto;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            background: white;
            margin-bottom: 15px;
        }
        
        .message {
            margin-bottom: 15px;
            padding: 10px;
            border-radius: 8px;
        }
        
        .message.user {
            background: #e3f2fd;
            margin-left: 20%;
        }
        
        .message.ai {
            background: #f3e5f5;
            margin-right: 20%;
        }
        
        .chat-input {
            display: flex;
            gap: 10px;
        }
        
        .chat-input input {
            flex: 1;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
        }
        
        .chat-input button {
            padding: 12px 25px;
            background: #4facfe;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        
        .chat-input button:hover {
            background: #3d8bfe;
        }
        
        .loading {
            text-align: center;
            padding: 20px;
            color: #666;
        }
        
        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        
        .success {
            background: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 Tincada AI</h1>
            <p>Database Dashboard & Analytics</p>
        </div>
        
        <div class="nav-tabs">
            <button class="nav-tab active" onclick="showTab('overview')">📊 Overview</button>
            <button class="nav-tab" onclick="showTab('users')">👥 Users</button>
            <button class="nav-tab" onclick="showTab('messages')">💬 Messages</button>
            <button class="nav-tab" onclick="showTab('analytics')">📈 Analytics</button>
            <button class="nav-tab" onclick="showTab('chat')">🗣️ Live Chat</button>
        </div>
        
        <div class="tab-content">
            <!-- Overview Tab -->
            <div id="overview" class="tab-pane active">
                <h2>📊 System Overview</h2>
                <div class="stats-grid" id="statsGrid">
                    <div class="loading">Loading statistics...</div>
                </div>
                
                <h3>🕒 Recent Activity</h3>
                <div id="recentActivity">
                    <div class="loading">Loading recent activity...</div>
                </div>
            </div>
            
            <!-- Users Tab -->
            <div id="users" class="tab-pane">
                <h2>👥 User Management</h2>
                <div id="usersData">
                    <div class="loading">Loading users data...</div>
                </div>
            </div>
            
            <!-- Messages Tab -->
            <div id="messages" class="tab-pane">
                <h2>💬 Chat Messages</h2>
                <div id="messagesData">
                    <div class="loading">Loading messages data...</div>
                </div>
            </div>
            
            <!-- Analytics Tab -->
            <div id="analytics" class="tab-pane">
                <h2>📈 Analytics & Reports</h2>
                <div id="analyticsData">
                    <div class="loading">Loading analytics data...</div>
                </div>
            </div>
            
            <!-- Live Chat Tab -->
            <div id="chat" class="tab-pane">
                <h2>🗣️ Live Chat Test</h2>
                <div class="chat-interface">
                    <div class="chat-messages" id="chatMessages">
                        <div class="message ai">
                            <strong>Tincada AI:</strong> Salam alaykum! Welcome to the database dashboard. You can test the chat functionality here.
                        </div>
                    </div>
                    <div class="chat-input">
                        <input type="text" id="messageInput" placeholder="Type your message here..." onkeypress="handleKeyPress(event)">
                        <button onclick="sendMessage()">Send</button>
                    </div>
                </div>
                
                <div id="chatStats">
                    <h3>💾 Database Storage</h3>
                    <p>All messages are automatically stored in the database with full analytics.</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentUserId = 'user_dashboard_' + Date.now();
        let currentConversationId = null;
        
        // Tab switching
        function showTab(tabName) {
            // Hide all tabs
            document.querySelectorAll('.tab-pane').forEach(pane => {
                pane.classList.remove('active');
            });
            
            // Remove active class from all nav tabs
            document.querySelectorAll('.nav-tab').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // Show selected tab
            document.getElementById(tabName).classList.add('active');
            event.target.classList.add('active');
            
            // Load data for the selected tab
            loadTabData(tabName);
        }
        
        // Load data for specific tab
        function loadTabData(tabName) {
            switch(tabName) {
                case 'overview':
                    loadOverviewData();
                    break;
                case 'users':
                    loadUsersData();
                    break;
                case 'messages':
                    loadMessagesData();
                    break;
                case 'analytics':
                    loadAnalyticsData();
                    break;
            }
        }
        
        // Load overview data
        function loadOverviewData() {
            fetch('dashboard_api.php?action=overview')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        displayStats(data.stats);
                        displayRecentActivity(data.recent_activity);
                    }
                })
                .catch(error => {
                    console.error('Error loading overview:', error);
                });
        }
        
        // Display statistics
        function displayStats(stats) {
            const statsGrid = document.getElementById('statsGrid');
            statsGrid.innerHTML = `
                <div class="stat-card">
                    <h3>${stats.total_users || 0}</h3>
                    <p>Total Users</p>
                </div>
                <div class="stat-card">
                    <h3>${stats.total_messages || 0}</h3>
                    <p>Total Messages</p>
                </div>
                <div class="stat-card">
                    <h3>${stats.total_conversations || 0}</h3>
                    <p>Conversations</p>
                </div>
                <div class="stat-card">
                    <h3>${stats.total_tokens || 0}</h3>
                    <p>Tokens Used</p>
                </div>
            `;
        }
        
        // Display recent activity
        function displayRecentActivity(activities) {
            const container = document.getElementById('recentActivity');
            if (!activities || activities.length === 0) {
                container.innerHTML = '<p>No recent activity found.</p>';
                return;
            }
            
            let html = '<table class="data-table"><thead><tr><th>Time</th><th>User</th><th>Message</th><th>Source</th></tr></thead><tbody>';
            
            activities.forEach(activity => {
                html += `
                    <tr>
                        <td>${new Date(activity.created_at).toLocaleString()}</td>
                        <td>${activity.username || activity.user_id}</td>
                        <td>${activity.message_preview}</td>
                        <td><span class="badge">${activity.response_source}</span></td>
                    </tr>
                `;
            });
            
            html += '</tbody></table>';
            container.innerHTML = html;
        }
        
        // Chat functionality
        function sendMessage() {
            const input = document.getElementById('messageInput');
            const message = input.value.trim();
            
            if (!message) return;
            
            // Add user message to chat
            addMessageToChat('user', message);
            input.value = '';
            
            // Show loading
            addMessageToChat('ai', 'Typing...', true);
            
            // Send to API
            fetch('api_handler.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    action: 'chat',
                    user_id: currentUserId,
                    message: message,
                    conversation_id: currentConversationId
                })
            })
            .then(response => response.json())
            .then(data => {
                // Remove loading message
                removeLastMessage();
                
                if (data.success) {
                    currentConversationId = data.conversation_id;
                    addMessageToChat('ai', data.response);
                    
                    // Update chat stats
                    updateChatStats(data);
                } else {
                    addMessageToChat('ai', 'Error: ' + (data.error || 'Unknown error'));
                }
            })
            .catch(error => {
                removeLastMessage();
                addMessageToChat('ai', 'Connection error. Please try again.');
                console.error('Chat error:', error);
            });
        }
        
        function addMessageToChat(type, message, isLoading = false) {
            const chatMessages = document.getElementById('chatMessages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}`;
            messageDiv.innerHTML = `<strong>${type === 'user' ? 'You' : 'Tincada AI'}:</strong> ${message}`;
            
            if (isLoading) {
                messageDiv.classList.add('loading-message');
            }
            
            chatMessages.appendChild(messageDiv);
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }
        
        function removeLastMessage() {
            const chatMessages = document.getElementById('chatMessages');
            const loadingMessage = chatMessages.querySelector('.loading-message');
            if (loadingMessage) {
                loadingMessage.remove();
            }
        }
        
        function handleKeyPress(event) {
            if (event.key === 'Enter') {
                sendMessage();
            }
        }
        
        function updateChatStats(data) {
            const statsDiv = document.getElementById('chatStats');
            statsDiv.innerHTML = `
                <h3>💾 Database Storage</h3>
                <p>✅ Message stored successfully</p>
                <p>🔢 Tokens used: ${data.tokens_used}</p>
                <p>📡 Source: ${data.source}</p>
                <p>🆔 Conversation ID: ${data.conversation_id}</p>
            `;
        }
        
        // Load other tab data functions (placeholder)
        function loadUsersData() {
            document.getElementById('usersData').innerHTML = '<div class="loading">Loading users data...</div>';
        }
        
        function loadMessagesData() {
            document.getElementById('messagesData').innerHTML = '<div class="loading">Loading messages data...</div>';
        }
        
        function loadAnalyticsData() {
            document.getElementById('analyticsData').innerHTML = '<div class="loading">Loading analytics data...</div>';
        }
        
        // Initialize dashboard
        document.addEventListener('DOMContentLoaded', function() {
            loadOverviewData();
        });
    </script>
</body>
</html>
