<!DOCTYPE html>
<html lang="so">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Message Limits Demo - Tincada AI</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
            padding: 2rem;
            margin: 0;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 3rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        h1 {
            text-align: center;
            font-size: 2.5rem;
            margin-bottom: 1rem;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .subtitle {
            text-align: center;
            font-size: 1.2rem;
            margin-bottom: 3rem;
            opacity: 0.9;
        }
        
        .feature-section {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .feature-section h2 {
            color: #4ecdc4;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .feature-list {
            list-style: none;
            padding: 0;
        }
        
        .feature-list li {
            padding: 0.8rem 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .feature-list li:last-child {
            border-bottom: none;
        }
        
        .feature-icon {
            width: 40px;
            height: 40px;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
        }
        
        .demo-box {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 10px;
            padding: 1.5rem;
            margin: 1rem 0;
            border-left: 4px solid #4ecdc4;
        }
        
        .success {
            background: rgba(76, 175, 80, 0.2);
            border: 1px solid rgba(76, 175, 80, 0.3);
            border-radius: 10px;
            padding: 1rem;
            margin: 1rem 0;
            border-left: 4px solid #4caf50;
        }
        
        .warning {
            background: rgba(255, 167, 38, 0.2);
            border: 1px solid rgba(255, 167, 38, 0.3);
            border-radius: 10px;
            padding: 1rem;
            margin: 1rem 0;
            border-left: 4px solid #ffa726;
        }
        
        .demo-buttons {
            display: flex;
            gap: 20px;
            justify-content: center;
            margin-top: 3rem;
            flex-wrap: wrap;
        }
        
        .demo-btn {
            background: linear-gradient(45deg, #4ecdc4, #44a08d);
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 25px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 10px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(78, 205, 196, 0.3);
        }
        
        .demo-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(78, 205, 196, 0.4);
        }
        
        .demo-btn.secondary {
            background: linear-gradient(45deg, #ff6b6b, #ee5a52);
            box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
        }
        
        .demo-btn.secondary:hover {
            box-shadow: 0 6px 20px rgba(255, 107, 107, 0.4);
        }
        
        .pricing-preview {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-top: 2rem;
        }
        
        .price-card {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 1.5rem;
            text-align: center;
        }
        
        .price-card h4 {
            color: #4ecdc4;
            margin-bottom: 0.5rem;
        }
        
        .price {
            font-size: 1.5rem;
            font-weight: bold;
            color: white;
            margin-bottom: 1rem;
        }
        
        .price-features {
            list-style: none;
            padding: 0;
            font-size: 0.9rem;
            color: #cccccc;
        }
        
        .price-features li {
            padding: 0.3rem 0;
        }
        
        @media (max-width: 768px) {
            body {
                padding: 1rem;
            }
            
            .container {
                padding: 2rem 1rem;
            }
            
            h1 {
                font-size: 2rem;
            }
            
            .demo-buttons {
                flex-direction: column;
                align-items: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📊 Message Limits - Tincada AI</h1>
        <p class="subtitle">Smart message management oo leh daily limits, auto new chat, iyo payment system</p>
        
        <div class="feature-section">
            <h2>✅ Features la dhisay</h2>
            <ul class="feature-list">
                <li>
                    <div class="feature-icon">📊</div>
                    <div>
                        <strong>Daily Message Limit (1000)</strong><br>
                        <small>Maalinle 1000 fariin, kadib payment modal ayaa soo baxda</small>
                    </div>
                </li>
                <li>
                    <div class="feature-icon">🔄</div>
                    <div>
                        <strong>Auto New Chat (100 messages)</strong><br>
                        <small>Markii 100 fariin la gaaro, automatic new chat ayaa la bilaabaa</small>
                    </div>
                </li>
                <li>
                    <div class="feature-icon">📌</div>
                    <div>
                        <strong>Fixed Input Position</strong><br>
                        <small>Input container waa fixed bottom, messages kaliya ayaa scroll gareeya</small>
                    </div>
                </li>
                <li>
                    <div class="feature-icon">💰</div>
                    <div>
                        <strong>Payment Modal</strong><br>
                        <small>3 plans: Premium ($9.99), Basic ($4.99), Daily Pass ($1.99)</small>
                    </div>
                </li>
                <li>
                    <div class="feature-icon">📈</div>
                    <div>
                        <strong>Message Counter</strong><br>
                        <small>Real-time counter header-ka oo muujinaya usage</small>
                    </div>
                </li>
            </ul>
        </div>
        
        <div class="feature-section">
            <h2>🎯 Sidee u shaqeeya</h2>
            
            <h3>1. Daily Message Tracking</h3>
            <div class="demo-box">
• Maalinle 1000 fariin ayaa la siiyaa
• Markii la gaaro, payment modal ayaa soo baxda
• Counter header-ka ayaa muujinaya progress
• Maalin cusub = reset automatic
            </div>
            
            <h3>2. Auto New Chat System</h3>
            <div class="demo-box">
• Chat walba waxay qaadan kartaa 100 fariin
• Markii 100 la gaaro, new chat automatic
• User message waa la kaydinayaa oo new chat-ka ayaa la gelayaa
• Smooth transition oo user-friendly ah
            </div>
            
            <h3>3. Fixed Input Design</h3>
            <div class="demo-box">
• Input container waa fixed bottom (like WhatsApp)
• Messages area kaliya ayaa scroll gareeya
• Mobile responsive design
• Smooth scrolling behavior
            </div>
        </div>
        
        <div class="feature-section">
            <h2>💳 Pricing Plans</h2>
            <div class="pricing-preview">
                <div class="price-card">
                    <h4>Premium Plan</h4>
                    <div class="price">$9.99/month</div>
                    <ul class="price-features">
                        <li>✅ Unlimited messages</li>
                        <li>✅ Priority support</li>
                        <li>✅ Advanced AI features</li>
                        <li>✅ 50MB file upload</li>
                    </ul>
                </div>
                <div class="price-card">
                    <h4>Basic Plan</h4>
                    <div class="price">$4.99/month</div>
                    <ul class="price-features">
                        <li>✅ 5,000 messages/day</li>
                        <li>✅ Standard support</li>
                        <li>✅ Basic AI features</li>
                        <li>✅ 10MB file upload</li>
                    </ul>
                </div>
                <div class="price-card">
                    <h4>Daily Pass</h4>
                    <div class="price">$1.99/day</div>
                    <ul class="price-features">
                        <li>✅ 2,000 messages today</li>
                        <li>✅ All AI features</li>
                        <li>✅ 25MB file upload</li>
                        <li>✅ Voice messages</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <div class="feature-section">
            <h2>🧪 Test Instructions</h2>
            
            <div class="success">
                <strong>✅ Ready to test!</strong> Dashboard-ku waa diyaar oo dhammaan features-yada waa shaqaynayaan.
            </div>
            
            <h3>Test Steps:</h3>
            <div class="demo-box">
1. Fur dashboard oo login garee
2. Eeg message counter header-ka (0/1000)
3. Dir dhawr fariin oo eeg counter update
4. Test auto new chat (simulate 100 messages)
5. Test payment modal (simulate 1000 messages)
6. Eeg fixed input position (scroll messages)
            </div>
            
            <div class="warning">
                <strong>⚠️ Demo Mode:</strong> Payment waa simulation kaliya. Real payment integration waa loo baahan yahay production.
            </div>
        </div>
        
        <div class="demo-buttons">
            <a href="dashboard.html" class="demo-btn">
                🚀 Test Dashboard
            </a>
            <a href="api-fix-demo.html" class="demo-btn secondary">
                🔧 API Fix Demo
            </a>
            <a href="demo.html" class="demo-btn secondary">
                📖 Main Demo
            </a>
        </div>
    </div>
</body>
</html>
