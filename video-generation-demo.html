<!DOCTYPE html>
<html lang="so">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Video Generation Demo - Tincada AI</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
            padding: 2rem;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 3rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        h1 {
            text-align: center;
            font-size: 2.5rem;
            margin-bottom: 1rem;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .subtitle {
            text-align: center;
            font-size: 1.2rem;
            margin-bottom: 3rem;
            opacity: 0.9;
        }

        .video-generator {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            border-left: 4px solid #4ecdc4;
        }

        .video-generator h2 {
            color: #4ecdc4;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-label {
            display: block;
            color: white;
            font-weight: 500;
            margin-bottom: 0.5rem;
            font-size: 0.9rem;
        }

        .form-input, .form-select, .form-range {
            width: 100%;
            padding: 12px 15px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            color: white;
            font-family: inherit;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .form-input:focus, .form-select:focus {
            outline: none;
            border-color: #4ecdc4;
            background: rgba(255, 255, 255, 0.15);
            box-shadow: 0 0 0 3px rgba(78, 205, 196, 0.2);
        }

        .form-input::placeholder {
            color: rgba(255, 255, 255, 0.6);
        }

        .form-select option {
            background: #333;
            color: white;
        }

        .range-container {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .form-range {
            flex: 1;
            padding: 0;
            height: 8px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 4px;
            outline: none;
            -webkit-appearance: none;
        }

        .form-range::-webkit-slider-thumb {
            -webkit-appearance: none;
            width: 20px;
            height: 20px;
            background: #4ecdc4;
            border-radius: 50%;
            cursor: pointer;
        }

        .range-value {
            min-width: 40px;
            text-align: center;
            font-weight: 600;
            color: #4ecdc4;
        }

        .generate-btn {
            width: 100%;
            background: linear-gradient(45deg, #4ecdc4, #44a08d);
            border: none;
            color: white;
            padding: 15px;
            border-radius: 10px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(78, 205, 196, 0.3);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }

        .generate-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(78, 205, 196, 0.4);
        }

        .generate-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .video-result {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            padding: 2rem;
            margin-top: 2rem;
            border: 1px solid rgba(255, 255, 255, 0.1);
            display: none;
        }

        .video-result.show {
            display: block;
            animation: fadeIn 0.5s ease;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .generated-video {
            text-align: center;
            margin: 2rem 0;
        }

        .generated-video video {
            max-width: 100%;
            height: auto;
            border-radius: 12px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
        }

        .video-placeholder {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border-radius: 12px;
            padding: 3rem;
            text-align: center;
            border: 2px dashed rgba(255, 255, 255, 0.3);
        }

        .video-placeholder-icon {
            font-size: 4rem;
            margin-bottom: 1rem;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .video-actions {
            display: flex;
            gap: 10px;
            justify-content: center;
            flex-wrap: wrap;
            margin-top: 1rem;
        }

        .action-btn {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.9rem;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .action-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-1px);
        }

        .status-message {
            padding: 1rem;
            border-radius: 10px;
            margin: 1rem 0;
            text-align: center;
            font-weight: 500;
        }

        .status-success {
            background: rgba(76, 175, 80, 0.2);
            border: 1px solid rgba(76, 175, 80, 0.3);
            color: #4caf50;
        }

        .status-error {
            background: rgba(244, 67, 54, 0.2);
            border: 1px solid rgba(244, 67, 54, 0.3);
            color: #f44336;
        }

        .status-info {
            background: rgba(33, 150, 243, 0.2);
            border: 1px solid rgba(33, 150, 243, 0.3);
            color: #2196f3;
        }

        .examples-section {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            padding: 2rem;
            margin-top: 2rem;
        }

        .examples-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin-top: 1rem;
        }

        .example-card {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 10px;
            padding: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .example-card:hover {
            transform: translateY(-2px);
            border-color: rgba(255, 255, 255, 0.2);
        }

        .example-card h4 {
            color: #4ecdc4;
            margin-bottom: 0.5rem;
            font-size: 0.9rem;
        }

        .example-card p {
            color: #cccccc;
            font-size: 0.8rem;
            line-height: 1.4;
            margin-bottom: 0.5rem;
        }

        .example-image {
            width: 100%;
            height: 120px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }

        .image-input-container {
            position: relative;
        }

        .image-options {
            display: flex;
            gap: 10px;
            margin-top: 10px;
        }

        .option-btn {
            background: rgba(255, 255, 255, 0.1);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.2);
            padding: 8px 16px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 0.9rem;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .option-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            border-color: #4ecdc4;
        }

        .image-chooser-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 1000;
        }

        .modal-content {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 2rem;
            max-width: 800px;
            max-height: 80vh;
            overflow-y: auto;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
        }

        .modal-header h3 {
            color: #4ecdc4;
            margin: 0;
        }

        .close-btn {
            background: none;
            border: none;
            color: white;
            font-size: 1.5rem;
            cursor: pointer;
            padding: 5px;
        }

        .image-gallery {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 1rem;
        }

        .gallery-image {
            position: relative;
            border-radius: 8px;
            overflow: hidden;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .gallery-image:hover {
            transform: scale(1.05);
            border-color: #4ecdc4;
        }

        .gallery-image img {
            width: 100%;
            height: 120px;
            object-fit: cover;
        }

        .gallery-image .image-label {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 5px;
            font-size: 0.8rem;
            text-align: center;
        }

        .file-upload-area {
            border: 2px dashed rgba(255, 255, 255, 0.3);
            border-radius: 10px;
            padding: 2rem;
            text-align: center;
            margin-top: 1rem;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .file-upload-area:hover {
            border-color: #4ecdc4;
            background: rgba(78, 205, 196, 0.1);
        }

        .file-upload-area.dragover {
            border-color: #4ecdc4;
            background: rgba(78, 205, 196, 0.2);
        }

        #fileInput {
            display: none;
        }

        @media (max-width: 768px) {
            body { padding: 1rem; }
            .container { padding: 2rem 1rem; }
            h1 { font-size: 2rem; }
            .examples-grid { grid-template-columns: 1fr; }
            .video-actions { flex-direction: column; align-items: center; }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎬 AI Video Generation</h1>
        <p class="subtitle">Convert images to videos using Replicate Stable Video Diffusion API</p>
        
        <div class="video-generator">
            <h2>🎥 Generate Video from Image</h2>
            <p>Upload an image URL and convert it to an animated video:</p>
            
            <form id="videoForm">
                <div class="form-group">
                    <label class="form-label" for="imageUrl">Image URL:</label>
                    <div class="image-input-container">
                        <input type="url" id="imageUrl" class="form-input"
                               placeholder="https://example.com/your-image.jpg" required>
                        <div class="image-options">
                            <button type="button" class="option-btn" onclick="showImageChooser()">
                                <i class="fas fa-images"></i> Choose from Gallery
                            </button>
                            <button type="button" class="option-btn" onclick="showFileUpload()">
                                <i class="fas fa-upload"></i> Upload Image
                            </button>
                        </div>
                    </div>
                    <div class="image-preview" id="imagePreview" style="display: none;">
                        <img id="previewImg" src="" alt="Preview" style="max-width: 200px; max-height: 200px; border-radius: 8px; margin-top: 1rem;">
                    </div>
                </div>

                <div class="form-group">
                    <label class="form-label" for="motionStrength">Motion Strength: <span id="motionValue">5</span>/10</label>
                    <div class="range-container">
                        <input type="range" id="motionStrength" class="form-range" 
                               min="1" max="10" value="5" oninput="updateMotionValue(this.value)">
                        <span class="range-value" id="motionDisplay">5</span>
                    </div>
                </div>

                <div class="form-group">
                    <label class="form-label" for="duration">Duration: <span id="durationValue">3</span> seconds</label>
                    <div class="range-container">
                        <input type="range" id="duration" class="form-range" 
                               min="2" max="8" value="3" oninput="updateDurationValue(this.value)">
                        <span class="range-value" id="durationDisplay">3s</span>
                    </div>
                </div>

                <div class="form-group">
                    <label class="form-label" for="fps">Frame Rate:</label>
                    <select id="fps" class="form-select">
                        <option value="12">12 FPS (Smooth)</option>
                        <option value="24" selected>24 FPS (Standard)</option>
                        <option value="30">30 FPS (High Quality)</option>
                    </select>
                </div>

                <button type="submit" class="generate-btn" id="generateBtn">
                    <i class="fas fa-video"></i>
                    <span>Generate Video</span>
                </button>
            </form>
        </div>
        
        <div class="video-result" id="videoResult">
            <h3>🎬 Generated Video</h3>
            <div class="generated-video" id="generatedVideo">
                <div class="video-placeholder">
                    <div class="video-placeholder-icon">🎬</div>
                    <h4>Generating your video...</h4>
                    <p>This may take 2-5 minutes</p>
                </div>
            </div>
            
            <div class="status-message" id="statusMessage"></div>
            
            <div class="video-actions" id="videoActions" style="display: none;">
                <button class="action-btn" onclick="downloadVideo()">
                    <i class="fas fa-download"></i> Download
                </button>
                <button class="action-btn" onclick="shareVideo()">
                    <i class="fas fa-share"></i> Share
                </button>
                <button class="action-btn" onclick="regenerateVideo()">
                    <i class="fas fa-redo"></i> Regenerate
                </button>
            </div>
        </div>
        
        <div class="examples-section">
            <h3>💡 Quick Start Options</h3>
            <p>Choose how you want to provide your image:</p>

            <div class="examples-grid">
                <div class="example-card" onclick="showImageChooser()">
                    <div class="example-image">🖼️</div>
                    <h4>🖼️ Choose from Gallery</h4>
                    <p>Select from curated high-quality images</p>
                </div>

                <div class="example-card" onclick="showFileUpload()">
                    <div class="example-image">📤</div>
                    <h4>📤 Upload Your Image</h4>
                    <p>Upload your own image file</p>
                </div>

                <div class="example-card" onclick="useExample('https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=800&h=600&fit=crop')">
                    <div class="example-image">🏔️</div>
                    <h4>🏔️ Try Sample Image</h4>
                    <p>Use a sample mountain landscape</p>
                </div>

                <div class="example-card" onclick="document.getElementById('imageUrl').focus()">
                    <div class="example-image">🔗</div>
                    <h4>🔗 Paste Image URL</h4>
                    <p>Enter a direct image URL</p>
                </div>
            </div>
        </div>

        <!-- Image Chooser Modal -->
        <div class="image-chooser-modal" id="imageChooserModal">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>🖼️ Choose an Image</h3>
                    <button class="close-btn" onclick="closeImageChooser()">&times;</button>
                </div>

                <div class="image-gallery">
                    <div class="gallery-image" onclick="selectImage('https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=800&h=600&fit=crop', 'Mountain Landscape')">
                        <img src="https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=200&h=120&fit=crop" alt="Mountain Landscape">
                        <div class="image-label">🏔️ Mountain Landscape</div>
                    </div>

                    <div class="gallery-image" onclick="selectImage('https://images.unsplash.com/photo-1439066615861-d1af74d74000?w=800&h=600&fit=crop', 'Ocean Waves')">
                        <img src="https://images.unsplash.com/photo-1439066615861-d1af74d74000?w=200&h=120&fit=crop" alt="Ocean Waves">
                        <div class="image-label">🌊 Ocean Waves</div>
                    </div>

                    <div class="gallery-image" onclick="selectImage('https://images.unsplash.com/photo-1441974231531-c6227db76b6e?w=800&h=600&fit=crop', 'Forest Path')">
                        <img src="https://images.unsplash.com/photo-1441974231531-c6227db76b6e?w=200&h=120&fit=crop" alt="Forest Path">
                        <div class="image-label">🌲 Forest Path</div>
                    </div>

                    <div class="gallery-image" onclick="selectImage('https://images.unsplash.com/photo-1518837695005-2083093ee35b?w=800&h=600&fit=crop', 'Campfire')">
                        <img src="https://images.unsplash.com/photo-1518837695005-2083093ee35b?w=200&h=120&fit=crop" alt="Campfire">
                        <div class="image-label">🔥 Campfire</div>
                    </div>

                    <div class="gallery-image" onclick="selectImage('https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=800&h=600&fit=crop', 'Flower Field')">
                        <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=200&h=120&fit=crop" alt="Flower Field">
                        <div class="image-label">🌸 Flower Field</div>
                    </div>

                    <div class="gallery-image" onclick="selectImage('https://images.unsplash.com/photo-1506197603052-3cc9c3a201bd?w=800&h=600&fit=crop', 'Starry Night')">
                        <img src="https://images.unsplash.com/photo-1506197603052-3cc9c3a201bd?w=200&h=120&fit=crop" alt="Starry Night">
                        <div class="image-label">⭐ Starry Night</div>
                    </div>

                    <div class="gallery-image" onclick="selectImage('https://images.unsplash.com/photo-1469474968028-56623f02e42e?w=800&h=600&fit=crop', 'Desert Sunset')">
                        <img src="https://images.unsplash.com/photo-1469474968028-56623f02e42e?w=200&h=120&fit=crop" alt="Desert Sunset">
                        <div class="image-label">🌅 Desert Sunset</div>
                    </div>

                    <div class="gallery-image" onclick="selectImage('https://images.unsplash.com/photo-1470071459604-3b5ec3a7fe05?w=800&h=600&fit=crop', 'City Lights')">
                        <img src="https://images.unsplash.com/photo-1470071459604-3b5ec3a7fe05?w=200&h=120&fit=crop" alt="City Lights">
                        <div class="image-label">🏙️ City Lights</div>
                    </div>

                    <div class="gallery-image" onclick="selectImage('https://images.unsplash.com/photo-1501594907352-04cda38ebc29?w=800&h=600&fit=crop', 'Waterfall')">
                        <img src="https://images.unsplash.com/photo-1501594907352-04cda38ebc29?w=200&h=120&fit=crop" alt="Waterfall">
                        <div class="image-label">💧 Waterfall</div>
                    </div>

                    <div class="gallery-image" onclick="selectImage('https://images.unsplash.com/photo-1500382017468-9049fed747ef?w=800&h=600&fit=crop', 'Snowy Mountains')">
                        <img src="https://images.unsplash.com/photo-1500382017468-9049fed747ef?w=200&h=120&fit=crop" alt="Snowy Mountains">
                        <div class="image-label">❄️ Snowy Mountains</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- File Upload Modal -->
        <div class="image-chooser-modal" id="fileUploadModal">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>📤 Upload Image</h3>
                    <button class="close-btn" onclick="closeFileUpload()">&times;</button>
                </div>

                <div class="file-upload-area" id="fileUploadArea" onclick="document.getElementById('fileInput').click()">
                    <i class="fas fa-cloud-upload-alt" style="font-size: 3rem; color: #4ecdc4; margin-bottom: 1rem;"></i>
                    <h4>Click to upload or drag & drop</h4>
                    <p>Supports: JPG, PNG, GIF, WebP</p>
                    <p style="font-size: 0.8rem; opacity: 0.7; margin-top: 1rem;">
                        Note: File will be uploaded to a temporary hosting service
                    </p>
                </div>

                <input type="file" id="fileInput" accept="image/*" onchange="handleFileUpload(event)">

                <div id="uploadProgress" style="display: none; margin-top: 1rem;">
                    <div style="background: rgba(255,255,255,0.1); border-radius: 10px; padding: 1rem; text-align: center;">
                        <i class="fas fa-spinner fa-spin" style="font-size: 2rem; color: #4ecdc4; margin-bottom: 1rem;"></i>
                        <p>Uploading image...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentVideoUrl = null;

        document.getElementById('videoForm').addEventListener('submit', function(e) {
            e.preventDefault();
            generateVideo();
        });

        function updateMotionValue(value) {
            document.getElementById('motionValue').textContent = value;
            document.getElementById('motionDisplay').textContent = value;
        }

        function updateDurationValue(value) {
            document.getElementById('durationValue').textContent = value;
            document.getElementById('durationDisplay').textContent = value + 's';
        }

        async function generateVideo() {
            const imageUrl = document.getElementById('imageUrl').value.trim();
            const motionStrength = parseInt(document.getElementById('motionStrength').value);
            const duration = parseInt(document.getElementById('duration').value);
            const fps = parseInt(document.getElementById('fps').value);
            
            if (!imageUrl) {
                showStatus('Please enter an image URL', 'error');
                return;
            }
            
            const generateBtn = document.getElementById('generateBtn');
            const videoResult = document.getElementById('videoResult');
            const generatedVideo = document.getElementById('generatedVideo');
            const videoActions = document.getElementById('videoActions');
            
            // Show loading state
            generateBtn.disabled = true;
            generateBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> <span>Generating...</span>';
            
            videoResult.classList.add('show');
            generatedVideo.innerHTML = `
                <div class="video-placeholder">
                    <div class="video-placeholder-icon">🎬</div>
                    <h4>Converting image to video...</h4>
                    <p>Using Stable Video Diffusion - This may take 2-5 minutes</p>
                    <div style="margin-top: 1rem; font-size: 0.9rem; opacity: 0.8;">
                        Motion: ${motionStrength}/10 | Duration: ${duration}s | FPS: ${fps}
                    </div>
                </div>
            `;
            videoActions.style.display = 'none';
            
            try {
                const response = await fetch('http://localhost:5001/api/tools/image-to-video', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        image_url: imageUrl,
                        motion_strength: motionStrength,
                        duration: duration,
                        fps: fps,
                        user_id: 'video_demo_user'
                    })
                });
                
                const data = await response.json();
                
                if (data.error) {
                    showStatus(data.error, 'error');
                    
                    if (data.error.includes('credits')) {
                        generatedVideo.innerHTML = `
                            <div class="video-placeholder">
                                <div class="video-placeholder-icon">💳</div>
                                <h4>Credits Required</h4>
                                <p>Replicate account needs credits for video generation</p>
                                <p style="margin-top: 1rem; font-size: 0.9rem; opacity: 0.8;">
                                    Add credits to your Replicate account to generate videos
                                </p>
                            </div>
                        `;
                    } else {
                        generatedVideo.innerHTML = `
                            <div class="video-placeholder">
                                <div class="video-placeholder-icon">❌</div>
                                <h4>Generation Failed</h4>
                                <p>${data.error}</p>
                            </div>
                        `;
                    }
                } else if (data.video_url) {
                    // Real video generated
                    currentVideoUrl = data.video_url;
                    generatedVideo.innerHTML = `
                        <video controls style="max-width: 100%; height: auto; border-radius: 12px; box-shadow: 0 8px 25px rgba(0,0,0,0.3);">
                            <source src="${data.video_url}" type="video/mp4">
                            Your browser does not support the video tag.
                        </video>
                    `;
                    videoActions.style.display = 'flex';
                    showStatus('Video generated successfully!', 'success');
                } else {
                    // Fallback response
                    generatedVideo.innerHTML = `
                        <div class="video-placeholder">
                            <div class="video-placeholder-icon">🎬</div>
                            <h4>Demo Mode</h4>
                            <p>Video generation configured but requires Replicate credits</p>
                            <p style="margin-top: 1rem; font-size: 0.9rem; opacity: 0.8;">
                                Image: ${imageUrl}<br>
                                Motion: ${motionStrength}/10 | Duration: ${duration}s | FPS: ${fps}
                            </p>
                        </div>
                    `;
                    showStatus('Demo mode - Add Replicate credits to generate real videos', 'info');
                }
                
            } catch (error) {
                console.error('Error:', error);
                showStatus('Connection error. Please check if the server is running.', 'error');
                
                generatedVideo.innerHTML = `
                    <div class="video-placeholder">
                        <div class="video-placeholder-icon">🔌</div>
                        <h4>Connection Error</h4>
                        <p>Please start the Flask server</p>
                        <p style="margin-top: 1rem; font-size: 0.9rem; opacity: 0.8;">
                            Run: python flask_server.py
                        </p>
                    </div>
                `;
            } finally {
                generateBtn.disabled = false;
                generateBtn.innerHTML = '<i class="fas fa-video"></i> <span>Generate Video</span>';
            }
        }

        function showStatus(message, type) {
            const statusMessage = document.getElementById('statusMessage');
            statusMessage.textContent = message;
            statusMessage.className = `status-message status-${type}`;
            statusMessage.style.display = 'block';
            
            setTimeout(() => {
                statusMessage.style.display = 'none';
            }, 5000);
        }

        function useExample(imageUrl) {
            document.getElementById('imageUrl').value = imageUrl;
            document.getElementById('imageUrl').focus();
        }

        function downloadVideo() {
            if (currentVideoUrl) {
                const link = document.createElement('a');
                link.href = currentVideoUrl;
                link.download = 'tincada-ai-generated-video.mp4';
                link.click();
            }
        }

        function shareVideo() {
            if (currentVideoUrl) {
                if (navigator.share) {
                    navigator.share({
                        title: 'AI Generated Video',
                        text: 'Check out this video I created with Tincada AI!',
                        url: currentVideoUrl
                    });
                } else {
                    navigator.clipboard.writeText(currentVideoUrl);
                    showStatus('Video URL copied to clipboard!', 'success');
                }
            }
        }

        function regenerateVideo() {
            generateVideo();
        }

        // Image chooser functions
        function showImageChooser() {
            document.getElementById('imageChooserModal').style.display = 'flex';
        }

        function closeImageChooser() {
            document.getElementById('imageChooserModal').style.display = 'none';
        }

        function selectImage(imageUrl, imageName) {
            document.getElementById('imageUrl').value = imageUrl;

            // Show preview
            const preview = document.getElementById('imagePreview');
            const previewImg = document.getElementById('previewImg');
            previewImg.src = imageUrl;
            previewImg.alt = imageName;
            preview.style.display = 'block';

            closeImageChooser();
            showStatus(`Selected: ${imageName}`, 'success');
        }

        // File upload functions
        function showFileUpload() {
            document.getElementById('fileUploadModal').style.display = 'flex';
        }

        function closeFileUpload() {
            document.getElementById('fileUploadModal').style.display = 'none';
        }

        function handleFileUpload(event) {
            const file = event.target.files[0];
            if (!file) return;

            // Validate file type
            const validTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
            if (!validTypes.includes(file.type)) {
                showStatus('Please select a valid image file (JPG, PNG, GIF, WebP)', 'error');
                return;
            }

            // Validate file size (max 10MB)
            if (file.size > 10 * 1024 * 1024) {
                showStatus('File size must be less than 10MB', 'error');
                return;
            }

            // Show upload progress
            document.getElementById('uploadProgress').style.display = 'block';

            // Create FormData for upload
            const formData = new FormData();
            formData.append('image', file);

            // Upload to temporary hosting service (using imgbb as example)
            uploadToImageHost(file);
        }

        async function uploadToImageHost(file) {
            try {
                // Create FormData for upload
                const formData = new FormData();
                formData.append('image', file);

                // Upload to server
                const response = await fetch('http://localhost:5001/api/upload-image', {
                    method: 'POST',
                    body: formData
                });

                const data = await response.json();

                if (data.success) {
                    // Set the image URL
                    document.getElementById('imageUrl').value = data.image_url;

                    // Show preview
                    const preview = document.getElementById('imagePreview');
                    const previewImg = document.getElementById('previewImg');
                    previewImg.src = data.image_url;
                    previewImg.alt = file.name;
                    preview.style.display = 'block';

                    // Hide upload progress
                    document.getElementById('uploadProgress').style.display = 'none';

                    closeFileUpload();
                    showStatus(`✅ Uploaded: ${file.name}`, 'success');
                } else {
                    throw new Error(data.error || 'Upload failed');
                }

            } catch (error) {
                console.error('Upload error:', error);
                document.getElementById('uploadProgress').style.display = 'none';
                showStatus(`❌ Upload failed: ${error.message}`, 'error');
            }
        }

        // Drag and drop functionality
        function setupDragAndDrop() {
            const uploadArea = document.getElementById('fileUploadArea');

            uploadArea.addEventListener('dragover', function(e) {
                e.preventDefault();
                uploadArea.classList.add('dragover');
            });

            uploadArea.addEventListener('dragleave', function(e) {
                e.preventDefault();
                uploadArea.classList.remove('dragover');
            });

            uploadArea.addEventListener('drop', function(e) {
                e.preventDefault();
                uploadArea.classList.remove('dragover');

                const files = e.dataTransfer.files;
                if (files.length > 0) {
                    const file = files[0];
                    document.getElementById('fileInput').files = files;
                    handleFileUpload({ target: { files: [file] } });
                }
            });
        }

        // Close modals when clicking outside
        window.addEventListener('click', function(e) {
            const imageModal = document.getElementById('imageChooserModal');
            const fileModal = document.getElementById('fileUploadModal');

            if (e.target === imageModal) {
                closeImageChooser();
            }
            if (e.target === fileModal) {
                closeFileUpload();
            }
        });

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🎬 Video generation demo loaded');

            // Setup drag and drop
            setupDragAndDrop();

            // Test server connection
            fetch('http://localhost:5001/api/health')
                .then(response => response.json())
                .then(data => {
                    console.log('✅ Server connected:', data);
                    if (data.replicate_available) {
                        showStatus('✅ Replicate API connected - Ready to generate videos!', 'success');
                    } else {
                        showStatus('⚠️ Replicate API not configured - Demo mode only', 'info');
                    }
                })
                .catch(error => {
                    console.log('⚠️ Server not connected:', error);
                    showStatus('❌ Server not connected. Please start: python flask_server.py', 'error');
                });
        });
    </script>
</body>
</html>
