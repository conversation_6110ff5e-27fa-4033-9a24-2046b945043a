#!/usr/bin/env python3
"""
🚀 TINCADA AI SERVER STARTER
Automatically starts the appropriate server for Tincada AI system
"""

import os
import sys
import subprocess
import time
import webbrowser
import socket
from pathlib import Path

def check_port(port):
    """Check if a port is available"""
    sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    result = sock.connect_ex(('localhost', port))
    sock.close()
    return result != 0

def check_php_installed():
    """Check if PHP is installed and available"""
    try:
        result = subprocess.run(['php', '--version'], 
                              capture_output=True, text=True, timeout=5)
        return result.returncode == 0
    except (subprocess.TimeoutExpired, FileNotFoundError):
        return False

def check_python_installed():
    """Check if Python is installed and available"""
    try:
        result = subprocess.run([sys.executable, '--version'], 
                              capture_output=True, text=True, timeout=5)
        return result.returncode == 0
    except (subprocess.TimeoutExpired, FileNotFoundError):
        return False

def find_available_port(start_port=8000, max_attempts=10):
    """Find an available port starting from start_port"""
    for port in range(start_port, start_port + max_attempts):
        if check_port(port):
            return port
    return None

def start_php_server(port=8000):
    """Start PHP built-in server"""
    print(f"🔧 Starting PHP server on port {port}...")
    try:
        # Start PHP server
        process = subprocess.Popen([
            'php', '-S', f'localhost:{port}'
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        # Wait a moment for server to start
        time.sleep(2)
        
        # Check if process is still running
        if process.poll() is None:
            print(f"✅ PHP server started successfully!")
            print(f"🌐 Main Chat: http://localhost:{port}/index.html")
            print(f"📊 Dashboard: http://localhost:{port}/dashboard.php")
            print(f"🔌 API Health: http://localhost:{port}/simple_api.php/health")
            print(f"🧪 System Test: http://localhost:{port}/test_complete_system.php")
            return process, port
        else:
            print("❌ PHP server failed to start")
            return None, None
            
    except FileNotFoundError:
        print("❌ PHP not found. Please install PHP or use Python server.")
        return None, None
    except Exception as e:
        print(f"❌ Error starting PHP server: {e}")
        return None, None

def start_python_server(port=8000):
    """Start Python HTTP server"""
    print(f"🐍 Starting Python HTTP server on port {port}...")
    try:
        # Start Python HTTP server
        process = subprocess.Popen([
            sys.executable, '-m', 'http.server', str(port)
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        # Wait a moment for server to start
        time.sleep(2)
        
        # Check if process is still running
        if process.poll() is None:
            print(f"✅ Python HTTP server started successfully!")
            print(f"🌐 Main Chat: http://localhost:{port}/index.html")
            print(f"📊 Dashboard: http://localhost:{port}/dashboard.html")
            print(f"⚠️  Note: PHP files won't work with Python server")
            print(f"💡 For full functionality, use PHP server or XAMPP")
            return process, port
        else:
            print("❌ Python HTTP server failed to start")
            return None, None
            
    except Exception as e:
        print(f"❌ Error starting Python server: {e}")
        return None, None

def check_required_files():
    """Check if required files exist"""
    required_files = [
        'index.html',
        'styles.css', 
        'script.js'
    ]
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    return missing_files

def print_banner():
    """Print welcome banner"""
    print("=" * 60)
    print("🤖 TINCADA AI SERVER STARTER")
    print("=" * 60)
    print("🚀 Automatically starting the best available server...")
    print()

def print_system_info():
    """Print system information"""
    print("📋 System Information:")
    print(f"   • Python: {sys.version.split()[0]}")
    print(f"   • PHP: {'✅ Available' if check_php_installed() else '❌ Not found'}")
    print(f"   • Working Directory: {os.getcwd()}")
    print()

def main():
    """Main function"""
    print_banner()
    print_system_info()
    
    # Check required files
    missing_files = check_required_files()
    if missing_files:
        print("❌ Missing required files:")
        for file in missing_files:
            print(f"   • {file}")
        print("\n💡 Please ensure you're in the correct directory with Tincada AI files.")
        return
    
    # Find available port
    port = find_available_port(8000)
    if not port:
        print("❌ No available ports found (8000-8009)")
        return
    
    print(f"🔍 Using port: {port}")
    print()
    
    # Try to start servers in order of preference
    server_process = None
    server_port = None
    
    # 1. Try PHP server first (best option)
    if check_php_installed():
        print("🎯 Attempting to start PHP server (recommended)...")
        server_process, server_port = start_php_server(port)
    
    # 2. Fall back to Python HTTP server
    if not server_process and check_python_installed():
        print("🎯 Falling back to Python HTTP server...")
        server_process, server_port = start_python_server(port)
    
    if not server_process:
        print("❌ Failed to start any server!")
        print("\n💡 Manual options:")
        print("   • Install XAMPP for full PHP/MySQL support")
        print("   • Use 'php -S localhost:8000' if PHP is installed")
        print("   • Use 'python -m http.server 8000' for basic HTML")
        return
    
    print()
    print("=" * 60)
    print("🎉 SERVER STARTED SUCCESSFULLY!")
    print("=" * 60)
    
    # Open browser automatically
    try:
        print("🌐 Opening browser...")
        webbrowser.open(f'http://localhost:{server_port}/index.html')
    except Exception as e:
        print(f"⚠️  Could not open browser automatically: {e}")
    
    print()
    print("📖 Available URLs:")
    print(f"   • Main Chat: http://localhost:{server_port}/index.html")
    if check_php_installed():
        print(f"   • Dashboard: http://localhost:{server_port}/dashboard.php")
        print(f"   • API Health: http://localhost:{server_port}/simple_api.php/health")
        print(f"   • System Test: http://localhost:{server_port}/test_complete_system.php")
    else:
        print(f"   • Dashboard: http://localhost:{server_port}/dashboard.html")
        print("   • Note: PHP features disabled (install PHP for full functionality)")
    
    print()
    print("🛑 Press Ctrl+C to stop the server")
    print("=" * 60)
    
    try:
        # Keep the server running
        while True:
            time.sleep(1)
            # Check if process is still alive
            if server_process.poll() is not None:
                print("\n❌ Server process died unexpectedly")
                break
                
    except KeyboardInterrupt:
        print("\n\n🛑 Stopping server...")
        server_process.terminate()
        try:
            server_process.wait(timeout=5)
        except subprocess.TimeoutExpired:
            server_process.kill()
        print("✅ Server stopped successfully!")

if __name__ == "__main__":
    main()
