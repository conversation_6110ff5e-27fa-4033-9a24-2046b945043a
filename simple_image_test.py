#!/usr/bin/env python3
"""
Simple Image Generation Test
Ku saabsan koodhka aad bixisay
"""

import requests
import base64
import json

# API Key (ka soo qaad .env file-ka ama ku qor toos)
OPENAI_API_KEY = "********************************************************************************************************************************************************************"

def generate_image_modern(prompt, filename="generated_image.png", size="1024x1024"):
    """
    Modern version of your image generation code
    Using requests instead of openai library
    """
    
    print(f"🎨 Sameeynaya sawir: {prompt}")
    print(f"📏 Cabbir: {size}")
    print(f"📁 Filename: {filename}")
    
    try:
        # Prepare API request
        headers = {
            'Authorization': f'Bearer {OPENAI_API_KEY}',
            'Content-Type': 'application/json'
        }
        
        data = {
            'model': 'dall-e-3',  # Latest model
            'prompt': prompt,
            'n': 1,
            'size': size,
            'response_format': 'b64_json'
        }
        
        print("📡 La xiriiraya OpenAI API...")
        
        # Make API request
        response = requests.post(
            'https://api.openai.com/v1/images/generations',
            headers=headers,
            json=data,
            timeout=60
        )
        
        print(f"📊 Response status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            image_b64 = result['data'][0]['b64_json']
            
            # Save image (exactly like your code)
            with open(filename, "wb") as f:
                f.write(base64.b64decode(image_b64))
            
            print(f"✅ Sawirka waa la keydiyay: {filename}")
            return True
            
        else:
            print(f"❌ API Error: {response.status_code}")
            error_data = response.json()
            print(f"Error details: {json.dumps(error_data, indent=2)}")
            return False
            
    except Exception as e:
        print(f"❌ Khalad: {e}")
        return False

def test_your_original_code():
    """
    Test using your exact original prompt
    """
    print("🧪 Testing your original code...")
    print("=" * 50)
    
    # Your exact prompt
    prompt = "A gray tabby cat hugging an otter with an orange scarf"
    filename = "otter.png"
    
    success = generate_image_modern(prompt, filename)
    
    if success:
        print("\n🎉 Success! Your code works!")
        print(f"📁 Check the file: {filename}")
    else:
        print("\n❌ Failed. Check API key and billing.")
    
    return success

def test_additional_images():
    """
    Test additional images
    """
    print("\n🧪 Testing additional images...")
    print("=" * 50)
    
    test_cases = [
        {
            "prompt": "A beautiful sunset over mountains with a lake",
            "filename": "sunset.png"
        },
        {
            "prompt": "A futuristic city with flying cars",
            "filename": "futuristic_city.png"
        },
        {
            "prompt": "A peaceful garden with colorful flowers",
            "filename": "garden.png"
        }
    ]
    
    successful = 0
    
    for i, test in enumerate(test_cases, 1):
        print(f"\n{i}️⃣ Test {i}/{len(test_cases)}")
        success = generate_image_modern(test["prompt"], test["filename"])
        
        if success:
            successful += 1
        
        # Small delay between requests
        import time
        time.sleep(2)
    
    print(f"\n📊 Results: {successful}/{len(test_cases)} successful")
    return successful

if __name__ == "__main__":
    print("🎨 Tincada AI Image Generation Test")
    print("Based on your original code")
    print("=" * 60)
    
    # Test 1: Your original code
    original_success = test_your_original_code()
    
    # Test 2: Additional images (only if first test succeeds)
    if original_success:
        additional_success = test_additional_images()
        
        print("\n" + "=" * 60)
        print("📊 Final Summary:")
        print(f"✅ Original test: {'PASSED' if original_success else 'FAILED'}")
        print(f"✅ Additional tests: {additional_success}/3 passed")
        
        if original_success and additional_success > 0:
            print("\n🎉 Image generation is working!")
            print("📁 Check the generated PNG files in this folder.")
        else:
            print("\n⚠️ Some issues detected. Check API key and billing.")
    else:
        print("\n❌ Original test failed. Check API key and billing.")
        print("\nPossible issues:")
        print("- API key is invalid")
        print("- Billing limit reached")
        print("- Network connection problem")
        print("- OpenAI service temporarily unavailable")
