#!/usr/bin/env python3
"""
Test real OpenAI API with the provided key
"""

from openai import OpenAI
import requests
import json

def test_openai_direct():
    """Test OpenAI API directly"""
    
    api_key = "********************************************************************************************************************************************************************"
    
    print("🔑 Testing OpenAI API directly...")
    print("=" * 50)
    
    try:
        client = OpenAI(api_key=api_key)
        
        # Test basic chat
        print("📝 Testing basic chat completion...")
        response = client.chat.completions.create(
            model="gpt-4o-mini",
            messages=[
                {
                    "role": "system",
                    "content": "You are a helpful AI assistant. Respond professionally."
                },
                {
                    "role": "user", 
                    "content": "What is 2+2? Give a brief answer."
                }
            ],
            max_tokens=100,
            temperature=0.7
        )
        
        ai_response = response.choices[0].message.content
        tokens_used = response.usage.total_tokens if response.usage else 0
        
        print(f"✅ API Response: {ai_response}")
        print(f"📊 Tokens used: {tokens_used}")
        print("✅ OpenAI API is working!")
        
        return True
        
    except Exception as e:
        print(f"❌ OpenAI API Error: {e}")
        return False

def test_tincada_api():
    """Test Tincada AI API endpoint"""
    
    print("\n🧪 Testing Tincada AI API endpoint...")
    print("=" * 50)
    
    try:
        response = requests.post("http://localhost:5001/api/chat", json={
            "message": "What is artificial intelligence? Give me a brief explanation.",
            "user_id": "test_user",
            "chat_history": []
        })
        
        if response.status_code == 200:
            data = response.json()
            ai_response = data.get('response', 'No response')
            print(f"✅ Tincada AI Response: {ai_response[:200]}...")
            print("✅ Tincada AI API is working!")
            return True
        else:
            print(f"❌ Tincada API Error: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Tincada API Request failed: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Testing Real API Integration")
    print("=" * 60)
    
    # Test OpenAI directly
    openai_works = test_openai_direct()
    
    # Test Tincada AI
    tincada_works = test_tincada_api()
    
    print("\n" + "=" * 60)
    print("📊 SUMMARY:")
    print(f"OpenAI Direct: {'✅ Working' if openai_works else '❌ Failed'}")
    print(f"Tincada AI: {'✅ Working' if tincada_works else '❌ Failed'}")
    
    if openai_works and tincada_works:
        print("🎉 Both APIs are working! AI will use real OpenAI data.")
    elif openai_works:
        print("⚠️ OpenAI works but Tincada AI has issues. Check server.")
    else:
        print("❌ OpenAI API has permission issues. Will use mock responses.")
