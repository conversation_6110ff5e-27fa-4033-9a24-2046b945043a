# 🔧 Tincada AI - Troubleshooting Guide

## API Key Issues

### Problem: "Waan ka xumahay, khalad ayaa dhacay"

Waxaa jiri kara dhawr sabab oo keena khaladkan:

### 1. API Key Format Issues

**Primary API Key (OpenAI Standard):**
```
********************************************************************************************************************************************************************
```

**Fallback API Key (Alternative Format):**
```
sk-1d9fea924a444e58b28769de9662ef20
```

### 2. Common Error Codes

| Error Code | Macnaha | Xalka |
|------------|---------|-------|
| 401 | API key khalad ah | Hubi API key-ga |
| 429 | Aad bay u badan tahay codsashada | Sug dhawr daqiiqo |
| 500 | OpenAI server khalad | Mar kale isku day |
| Network Error | Internet connection khalad | Hubi internet-ka |

### 3. Testing API Keys

1. **Fur API Test Page:**
   ```
   http://localhost:5000/api-test.html
   ```

2. **Test Primary API:**
   - Riix "Test Primary API"
   - Eeg natiijooyinka

3. **Test Fallback API:**
   - Riix "Test Fallback API"
   - Eeg natiijooyinka

4. **Test Both APIs:**
   - Riix "Test Both APIs"
   - Eeg kuwa shaqeeya

### 4. Manual API Key Update

Haddii API keys-yada aan shaqaynin, waxaad beddeli kartaa:

**Dashboard (dashboard.js):**
```javascript
// Line 5-8
this.apiKey = 'YOUR_NEW_PRIMARY_KEY';
this.fallbackApiKey = 'YOUR_NEW_FALLBACK_KEY';
```

**Old Interface (script.js):**
```javascript
// Line 4
this.apiKey = 'YOUR_NEW_KEY';
```

### 5. Browser Console Debugging

1. **Fur Browser Developer Tools:**
   - Press F12 ama right-click → "Inspect"

2. **Eeg Console Tab:**
   - Eeg error messages
   - Raadi "API Error" ama "Network error"

3. **Common Console Messages:**
   ```
   🔄 Making API request with key: sk-proj-...
   📝 Messages: [...]
   📤 Request body: {...}
   📥 Response status: 401
   ❌ API Error 401: Invalid API key
   ```

### 6. Network Issues

**Check Internet Connection:**
- Hubi in internet-ku shaqaynayo
- Isku day websites kale

**CORS Issues:**
- Haddii aad isticmaalayso file:// protocol, isticmaal server
- Run: `python server.py`

**Firewall/Proxy:**
- Hubi in firewall-ku aan blocking gareynin OpenAI API
- Haddii aad ku jirto corporate network, la xiriir IT

### 7. API Key Validation

**Valid OpenAI API Key Format:**
- Waa in uu ku bilaabmaa `sk-`
- Waa in uu leeyahay 51+ characters
- Ma laha spaces ama special characters

**Check API Key Status:**
1. Gal OpenAI Dashboard
2. Eeg API Keys section
3. Hubi in key-gu active yahay
4. Eeg usage limits

### 8. Rate Limiting

**Haddii aad hesho "429 Too Many Requests":**
- Sug 1-5 daqiiqo
- Yaree message frequency
- Upgrade OpenAI plan haddii loo baahdo

### 9. Model Availability

**GPT-4o-mini Model:**
- Hubi in model-ku available yahay account-kaaga
- Haddii aan la helin, beddel model:
  ```javascript
  model: 'gpt-3.5-turbo'  // instead of 'gpt-4o-mini'
  ```

### 10. Step-by-Step Debugging

1. **Test API Keys:**
   ```
   http://localhost:5000/api-test.html
   ```

2. **Check Browser Console:**
   - F12 → Console tab
   - Eeg error messages

3. **Verify Network:**
   - Ping google.com
   - Check internet speed

4. **Update API Keys:**
   - Get fresh keys from OpenAI
   - Update dashboard.js iyo script.js

5. **Restart Server:**
   ```bash
   # Stop server (Ctrl+C)
   # Start again
   python server.py
   ```

### 11. Contact Support

Haddii dhammaan xalalkani ay fashilmaan:

1. **Gather Information:**
   - Browser type iyo version
   - Error messages from console
   - API test results
   - Network status

2. **Report Issue:**
   - Screenshot error messages
   - Copy console logs
   - Describe steps taken

### 12. Alternative Solutions

**Use Different Browser:**
- Chrome, Firefox, Safari, Edge

**Clear Browser Cache:**
- Ctrl+Shift+Delete
- Clear all data

**Disable Extensions:**
- Ad blockers
- Privacy extensions
- VPN extensions

**Try Incognito/Private Mode:**
- Test without extensions
- Fresh session

---

## Quick Fix Checklist

- [ ] API keys waa sax yihiin
- [ ] Internet connection waa shaqaynayaa
- [ ] Browser console ma muujinayo errors
- [ ] Server waa running (python server.py)
- [ ] API test page waa shaqaynayaa
- [ ] Model name waa sax yahay (gpt-4o-mini)
- [ ] No rate limiting (429 errors)
- [ ] OpenAI account waa active

---

**Haddii wali khalad jiro, isticmaal API test page si aad u hubiso API keys-yada!**

🔗 **API Test:** http://localhost:5000/api-test.html
