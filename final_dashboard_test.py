#!/usr/bin/env python3
"""
🧪 Final Dashboard Test
Test dashboard code cards with real AI responses
"""

import requests
import json
import time

def test_dashboard_final():
    """Final test for dashboard code cards"""
    
    print("🧪 Final Dashboard Code Cards Test")
    print("=" * 60)
    
    api_url = "http://localhost:5001/api/chat"
    
    # Test HTML code request
    payload = {
        "message": "write html button with click event",
        "user_id": "final_dashboard_test"
    }
    
    print("❓ Testing: write html button with click event")
    print("🚀 Sending request to AI...")
    
    try:
        response = requests.post(
            api_url,
            json=payload,
            headers={"Content-Type": "application/json"},
            timeout=20
        )
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Response received!")
            
            # Check response format
            if 'response' in data:
                ai_response = data['response']
                print(f"📊 Response type: Custom format")
                print(f"📊 Source: {data.get('source', 'unknown')}")
                print(f"📊 Tokens: {data.get('tokens_used', 0)}")
            elif 'choices' in data:
                ai_response = data['choices'][0]['message']['content']
                print(f"📊 Response type: OpenAI format")
            else:
                print("❌ Unknown response format")
                return
            
            print(f"📏 Response length: {len(ai_response)} characters")
            
            # Check for code blocks
            html_blocks = ai_response.count('```html')
            css_blocks = ai_response.count('```css')
            js_blocks = ai_response.count('```javascript')
            total_blocks = ai_response.count('```') // 2
            
            print(f"💻 Code blocks found:")
            print(f"   - HTML blocks: {html_blocks}")
            print(f"   - CSS blocks: {css_blocks}")
            print(f"   - JavaScript blocks: {js_blocks}")
            print(f"   - Total blocks: {total_blocks}")
            
            if total_blocks > 0:
                print("🎯 ✅ SUCCESS: Code blocks found!")
                print("💻 Dashboard should display these in code cards")
                
                # Show full response
                print("\n📄 Full AI Response:")
                print("-" * 50)
                print(ai_response)
                print("-" * 50)
                
                print("\n🌐 Dashboard Test Instructions:")
                print("1. Open: http://127.0.0.1:5001/dashboard.html")
                print("2. Ask AI: 'write html button with click event'")
                print("3. You should see:")
                print("   ✅ Dark code cards with language labels")
                print("   ✅ Copy buttons (top right of each card)")
                print("   ✅ Edit buttons (top right of each card)")
                print("   ✅ Syntax highlighting with colors")
                print("   ✅ Professional dark theme")
                print("   ✅ Fira Code font for code")
                
                print("\n🎯 Expected Code Cards:")
                if html_blocks > 0:
                    print(f"   📝 {html_blocks} HTML card(s) with red/pink syntax")
                if css_blocks > 0:
                    print(f"   🎨 {css_blocks} CSS card(s) with blue/green syntax")
                if js_blocks > 0:
                    print(f"   ⚡ {js_blocks} JavaScript card(s) with yellow/purple syntax")
                    
            else:
                print("❌ FAILED: No code blocks found!")
                print("⚠️ Dashboard will NOT show code cards")
                print(f"Response: {ai_response[:200]}...")
                
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            print(f"Response: {response.text}")
            
    except Exception as e:
        print(f"❌ Error: {e}")

def test_dashboard_features():
    """Test dashboard features checklist"""
    
    print("\n🔍 Dashboard Features Checklist")
    print("=" * 60)
    
    features = [
        "✅ formatAIMessage function updated with code card support",
        "✅ copyCode function for copy functionality", 
        "✅ editCode function for edit modal",
        "✅ saveEditedCode function for saving edits",
        "✅ Code card CSS styles in dashboard.css",
        "✅ Syntax highlighting (highlight.js) included",
        "✅ Fira Code font for professional code display",
        "✅ Dark theme with Monokai colors",
        "✅ Interactive copy/edit buttons",
        "✅ Language labels (HTML, CSS, JS, Python, etc.)",
        "✅ Professional styling matching reference image",
        "✅ Dashboard initialization script added"
    ]
    
    print("📋 Implemented Features:")
    for feature in features:
        print(f"   {feature}")
    
    print("\n💡 Manual Testing Steps:")
    print("1. Open http://127.0.0.1:5001/dashboard.html")
    print("2. Login or continue as guest")
    print("3. Ask AI any of these:")
    print("   - 'write html button code'")
    print("   - 'create css button style'")
    print("   - 'javascript alert function'")
    print("   - 'python hello world'")
    print("4. Verify code appears in interactive cards")
    print("5. Test copy button functionality")
    print("6. Test edit button functionality")

def test_specific_examples():
    """Test specific code examples"""
    
    print("\n🎨 Testing Specific Examples")
    print("=" * 60)
    
    examples = [
        "write html form with input fields",
        "create css grid layout",
        "javascript function to validate form",
        "python function to read file"
    ]
    
    api_url = "http://localhost:5001/api/chat"
    
    for i, example in enumerate(examples, 1):
        print(f"\n📝 Example {i}: {example}")
        
        try:
            payload = {
                "message": example,
                "user_id": f"dashboard_example_{i}"
            }
            
            response = requests.post(api_url, json=payload, timeout=15)
            
            if response.status_code == 200:
                data = response.json()
                ai_response = data.get('response', '') or (data.get('choices', [{}])[0].get('message', {}).get('content', ''))
                
                # Count code blocks
                code_blocks = ai_response.count('```') // 2
                if code_blocks > 0:
                    print(f"✅ SUCCESS: {code_blocks} code block(s) found")
                    
                    # Check languages
                    languages = []
                    if '```html' in ai_response:
                        languages.append('HTML')
                    if '```css' in ai_response:
                        languages.append('CSS')
                    if '```javascript' in ai_response:
                        languages.append('JavaScript')
                    if '```python' in ai_response:
                        languages.append('Python')
                    
                    if languages:
                        print(f"💻 Languages: {', '.join(languages)}")
                        
                else:
                    print("❌ FAILED: No code blocks found")
                    
            time.sleep(1)
            
        except Exception as e:
            print(f"❌ Error: {e}")

if __name__ == "__main__":
    print("🤖 Final Dashboard Code Cards Test")
    print("🎯 Testing complete code card functionality")
    print("📅 " + time.strftime("%Y-%m-%d %H:%M:%S"))
    print()
    
    # Test dashboard final
    test_dashboard_final()
    
    # Test dashboard features
    test_dashboard_features()
    
    # Test specific examples
    test_specific_examples()
    
    print("\n🎉 Final dashboard tests completed!")
    print("🌐 Open http://127.0.0.1:5001/dashboard.html to test manually!")
    print("🎯 All HTML/CSS/JS/Python code should appear in interactive cards!")
