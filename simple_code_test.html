<!DOCTYPE html>
<html lang="so">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Code Card Test</title>
    <link rel="stylesheet" href="dashboard.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Fira+Code:wght@300;400;500;600&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/styles/monokai.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/highlight.min.js"></script>
</head>
<body style="background: #212121; color: white; padding: 20px; font-family: 'Poppins', sans-serif;">
    <h1>🧪 Simple Code Card Test</h1>
    
    <div id="testResult" style="margin: 20px 0;"></div>
    
    <button onclick="testCodeCard()" style="background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;">
        Test Code Card
    </button>

    <script>
        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }
        
        function testCodeCard() {
            console.log('🧪 Testing code card...');
            
            const testMessage = `Here's a simple HTML button:

\`\`\`html
<button type="button">Click Me!</button>
\`\`\``;

            console.log('📝 Test message:', testMessage);
            
            // Process code blocks
            let formattedText = testMessage;
            
            formattedText = formattedText.replace(/```(\w+)?\n?([\s\S]*?)```/g, (match, language, code) => {
                const lang = language || 'text';
                const cleanCode = code.trim();
                const codeId = 'code_' + Math.random().toString(36).substr(2, 9);
                
                console.log('🎨 Found code block:', { lang, cleanCode });
                
                const cardHtml = `<div class="code-card" data-language="${lang}">
                    <div class="code-header">
                        <div class="code-language">
                            <i class="fas fa-code"></i>
                            <span>${lang.toUpperCase()}</span>
                        </div>
                        <div class="code-actions">
                            <button class="code-btn copy-btn" onclick="copyCode('${codeId}')" title="Copy code">
                                <i class="fas fa-copy"></i>
                                Copy
                            </button>
                            <button class="code-btn edit-btn" onclick="editCode('${codeId}')" title="Edit code">
                                <i class="fas fa-edit"></i>
                                Edit
                            </button>
                        </div>
                    </div>
                    <div class="code-content">
                        <pre id="${codeId}"><code class="language-${lang}">${escapeHtml(cleanCode)}</code></pre>
                    </div>
                </div>`;
                
                console.log('🎯 Generated card HTML:', cardHtml);
                
                // Apply syntax highlighting after DOM insertion
                setTimeout(() => {
                    const codeElement = document.getElementById(codeId);
                    if (codeElement && window.hljs) {
                        hljs.highlightElement(codeElement.querySelector('code'));
                        console.log('✨ Applied syntax highlighting to:', codeId);
                    }
                }, 100);
                
                return cardHtml;
            });
            
            // Process other formatting
            formattedText = formattedText
                .replace(/\n/g, '<br>')
                .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
                .replace(/\*(.*?)\*/g, '<em>$1</em>');
            
            console.log('🎨 Final formatted text:', formattedText);
            
            // Display result
            document.getElementById('testResult').innerHTML = formattedText;
        }
        
        function copyCode(codeId) {
            const codeElement = document.getElementById(codeId);
            const code = codeElement.textContent;
            
            navigator.clipboard.writeText(code).then(() => {
                console.log('✅ Code copied:', code);
                alert('Code copied to clipboard!');
            }).catch(() => {
                console.error('❌ Failed to copy code');
                alert('Failed to copy code');
            });
        }
        
        function editCode(codeId) {
            const codeElement = document.getElementById(codeId);
            const currentCode = codeElement.textContent;
            
            const newCode = prompt('Edit code:', currentCode);
            if (newCode !== null) {
                codeElement.textContent = newCode;
                console.log('✅ Code updated:', newCode);
            }
        }
        
        // Auto-test on page load
        setTimeout(() => {
            console.log('🔄 Auto-testing...');
            testCodeCard();
        }, 1000);
    </script>
</body>
</html>
