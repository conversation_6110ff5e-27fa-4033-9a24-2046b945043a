<!DOCTYPE html>
<html lang="so">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tincada AI Test - تجربة تينكادا الذكي</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            direction: rtl;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .status {
            padding: 20px;
            text-align: center;
            font-weight: bold;
        }

        .status.connected {
            background: #d4edda;
            color: #155724;
        }

        .status.disconnected {
            background: #f8d7da;
            color: #721c24;
        }

        .chat-container {
            padding: 30px;
        }

        .chat-messages {
            height: 400px;
            overflow-y: auto;
            border: 2px solid #e0e0e0;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            background: #f9f9f9;
        }

        .message {
            margin-bottom: 15px;
            padding: 15px;
            border-radius: 15px;
            max-width: 80%;
        }

        .user-message {
            background: #007bff;
            color: white;
            margin-left: auto;
            text-align: right;
        }

        .ai-message {
            background: #e9ecef;
            color: #333;
            margin-right: auto;
            text-align: left;
        }

        .input-container {
            display: flex;
            gap: 10px;
        }

        .message-input {
            flex: 1;
            padding: 15px;
            border: 2px solid #ddd;
            border-radius: 25px;
            font-size: 16px;
            outline: none;
            transition: border-color 0.3s;
        }

        .message-input:focus {
            border-color: #007bff;
        }

        .send-button {
            padding: 15px 30px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            transition: background 0.3s;
        }

        .send-button:hover {
            background: #0056b3;
        }

        .send-button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }

        .quick-tests {
            margin-top: 20px;
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .quick-test-btn {
            padding: 10px 15px;
            background: #28a745;
            color: white;
            border: none;
            border-radius: 20px;
            cursor: pointer;
            font-size: 14px;
            transition: background 0.3s;
        }

        .quick-test-btn:hover {
            background: #218838;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 20px;
            color: #666;
        }

        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #3498db;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 Tincada AI</h1>
            <p>Kaaliyahaaga AI ah - مساعدك الذكي</p>
        </div>

        <div id="status" class="status disconnected">
            🔴 La xiriirayo server-ka...
        </div>

        <div class="chat-container">
            <div id="chatMessages" class="chat-messages">
                <div class="message ai-message">
                    <strong>Tincada AI:</strong> Salam alaykum! Waan ku soo dhaweynayaa. Sidee kuu caawin karaa maanta?
                </div>
            </div>

            <div class="loading" id="loading">
                <div class="spinner"></div>
                <p>AI-gu wuu ka jawaabayaa...</p>
            </div>

            <div class="input-container">
                <input type="text" id="messageInput" class="message-input" 
                       placeholder="Qor fariintaada halkan..." 
                       onkeypress="handleKeyPress(event)">
                <button onclick="sendMessage()" id="sendButton" class="send-button">Dir</button>
            </div>

            <div class="quick-tests">
                <button class="quick-test-btn" onclick="quickTest('Salam alaykum')">Salam</button>
                <button class="quick-test-btn" onclick="quickTest('Maxaad sameen kartaa?')">Maxaad sameen kartaa?</button>
                <button class="quick-test-btn" onclick="quickTest('Samee JavaScript function')">Code samee</button>
                <button class="quick-test-btn" onclick="quickTest('Caawimaad')">Caawimaad</button>
            </div>
        </div>
    </div>

    <script>
        let chatHistory = [];

        // Check server status
        async function checkServerStatus() {
            try {
                const response = await fetch('/api/health');
                const data = await response.json();
                
                const statusDiv = document.getElementById('status');
                if (response.ok) {
                    statusDiv.className = 'status connected';
                    statusDiv.innerHTML = `🟢 Server wuu shaqeynayaa (${data.mock_mode ? 'Mock Mode' : 'OpenAI Mode'})`;
                } else {
                    throw new Error('Server error');
                }
            } catch (error) {
                const statusDiv = document.getElementById('status');
                statusDiv.className = 'status disconnected';
                statusDiv.innerHTML = '🔴 Server lama heli karo';
            }
        }

        // Send message to AI
        async function sendMessage() {
            const input = document.getElementById('messageInput');
            const message = input.value.trim();
            
            if (!message) return;

            // Add user message to chat
            addMessage(message, 'user');
            input.value = '';
            
            // Show loading
            document.getElementById('loading').style.display = 'block';
            document.getElementById('sendButton').disabled = true;

            try {
                const response = await fetch('/api/chat', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        message: message,
                        user_id: 'test_user',
                        chat_history: chatHistory
                    })
                });

                const data = await response.json();
                
                if (response.ok) {
                    addMessage(data.response, 'ai');
                    
                    // Update chat history
                    chatHistory.push({role: 'user', content: message});
                    chatHistory.push({role: 'assistant', content: data.response});
                    
                    // Keep only last 10 messages
                    if (chatHistory.length > 10) {
                        chatHistory = chatHistory.slice(-10);
                    }
                } else {
                    addMessage('Khalad ayaa dhacay: ' + data.error, 'ai');
                }
            } catch (error) {
                addMessage('Khalad ayaa dhacay: ' + error.message, 'ai');
            } finally {
                document.getElementById('loading').style.display = 'none';
                document.getElementById('sendButton').disabled = false;
            }
        }

        // Add message to chat
        function addMessage(message, sender) {
            const chatMessages = document.getElementById('chatMessages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${sender}-message`;
            
            if (sender === 'user') {
                messageDiv.innerHTML = `<strong>Adiga:</strong> ${message}`;
            } else {
                messageDiv.innerHTML = `<strong>Tincada AI:</strong> ${message.replace(/\n/g, '<br>')}`;
            }
            
            chatMessages.appendChild(messageDiv);
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        // Quick test function
        function quickTest(message) {
            document.getElementById('messageInput').value = message;
            sendMessage();
        }

        // Handle Enter key
        function handleKeyPress(event) {
            if (event.key === 'Enter') {
                sendMessage();
            }
        }

        // Initialize
        checkServerStatus();
        setInterval(checkServerStatus, 30000); // Check every 30 seconds
    </script>
</body>
</html>
