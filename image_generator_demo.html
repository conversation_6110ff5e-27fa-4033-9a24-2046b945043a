<!DOCTYPE html>
<html lang="so">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tincada AI - Image Generator</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(45deg, #FF6B6B, #4ECDC4);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .content {
            padding: 30px;
        }

        .input-section {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 15px;
            margin-bottom: 30px;
        }

        .prompt-input {
            width: 100%;
            padding: 15px;
            border: 2px solid #ddd;
            border-radius: 10px;
            font-size: 16px;
            margin-bottom: 15px;
            outline: none;
            transition: border-color 0.3s;
        }

        .prompt-input:focus {
            border-color: #4ECDC4;
        }

        .controls {
            display: flex;
            gap: 15px;
            align-items: center;
            flex-wrap: wrap;
        }

        .size-selector {
            padding: 10px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 14px;
            outline: none;
        }

        .generate-btn {
            padding: 15px 30px;
            background: linear-gradient(45deg, #FF6B6B, #4ECDC4);
            color: white;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            transition: transform 0.3s;
        }

        .generate-btn:hover {
            transform: translateY(-2px);
        }

        .generate-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
        }

        .quick-prompts {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            margin-top: 15px;
        }

        .quick-prompt-btn {
            padding: 8px 15px;
            background: #e9ecef;
            border: 1px solid #ddd;
            border-radius: 20px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.3s;
        }

        .quick-prompt-btn:hover {
            background: #4ECDC4;
            color: white;
        }

        .status {
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            text-align: center;
            font-weight: bold;
        }

        .status.success {
            background: #d4edda;
            color: #155724;
        }

        .status.error {
            background: #f8d7da;
            color: #721c24;
        }

        .status.info {
            background: #d1ecf1;
            color: #0c5460;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 30px;
        }

        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #4ECDC4;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            animation: spin 1s linear infinite;
            margin: 0 auto 15px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .result-section {
            display: none;
            background: #f8f9fa;
            padding: 25px;
            border-radius: 15px;
            text-align: center;
        }

        .generated-image {
            max-width: 100%;
            height: auto;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            margin: 20px 0;
        }

        .image-info {
            background: white;
            padding: 15px;
            border-radius: 10px;
            margin-top: 15px;
            text-align: left;
        }

        .download-btn {
            padding: 10px 20px;
            background: #28a745;
            color: white;
            border: none;
            border-radius: 20px;
            cursor: pointer;
            margin: 10px;
            text-decoration: none;
            display: inline-block;
        }

        .examples {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 15px;
            margin-top: 30px;
        }

        .examples h3 {
            margin-bottom: 15px;
            color: #333;
        }

        .example-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
        }

        .example-card {
            background: white;
            padding: 15px;
            border-radius: 10px;
            border: 1px solid #ddd;
            cursor: pointer;
            transition: all 0.3s;
        }

        .example-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 25px;
            border-radius: 10px;
            color: white;
            font-weight: bold;
            z-index: 1000;
            transform: translateX(400px);
            transition: transform 0.3s ease;
        }

        .notification.show {
            transform: translateX(0);
        }

        .notification.success {
            background: linear-gradient(45deg, #28a745, #20c997);
        }

        .notification.error {
            background: linear-gradient(45deg, #dc3545, #fd7e14);
        }

        .notification.info {
            background: linear-gradient(45deg, #17a2b8, #6f42c1);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎨 Tincada AI Image Generator</h1>
            <p>Samee sawiro qurux badan adoo isticmaalaya AI - إنشاء صور جميلة باستخدام الذكاء الاصطناعي</p>
        </div>

        <div class="content">
            <div class="input-section">
                <h3>📝 Qor waxa aad rabto inaad sawir ka sameyso</h3>
                <textarea 
                    id="promptInput" 
                    class="prompt-input" 
                    placeholder="Tusaale: A beautiful sunset over mountains with a lake, realistic style..."
                    rows="3"></textarea>
                
                <div class="controls">
                    <select id="sizeSelector" class="size-selector">
                        <option value="1024x1024">Square (1024x1024)</option>
                        <option value="1024x1792">Portrait (1024x1792)</option>
                        <option value="1792x1024">Landscape (1792x1024)</option>
                    </select>
                    
                    <button id="generateBtn" class="generate-btn" onclick="generateImage()">
                        🎨 Samee Sawir
                    </button>
                </div>

                <div class="quick-prompts">
                    <button class="quick-prompt-btn" onclick="setPrompt('A gray tabby cat hugging an otter with an orange scarf')">Cat & Otter</button>
                    <button class="quick-prompt-btn" onclick="setPrompt('A beautiful sunset over mountains')">Sunset Mountains</button>
                    <button class="quick-prompt-btn" onclick="setPrompt('A futuristic city with flying cars')">Futuristic City</button>
                    <button class="quick-prompt-btn" onclick="setPrompt('A peaceful garden with colorful flowers')">Garden</button>
                    <button class="quick-prompt-btn" onclick="setPrompt('A majestic lion in African savanna')">Lion Safari</button>
                </div>
            </div>

            <div id="statusDiv" class="status info" style="display: none;">
                🔄 Checking server status...
            </div>

            <div id="loadingDiv" class="loading">
                <div class="spinner"></div>
                <h3>🎨 Sawirka waa la sameeynayaa...</h3>
                <p>Fadlan sug, waxaan la xiriirnaynaa AI-ga...</p>
            </div>

            <div id="resultSection" class="result-section">
                <h3>✅ Sawirka waa la sameeyay!</h3>
                <img id="generatedImage" class="generated-image" alt="Generated Image">
                
                <div class="image-info">
                    <p><strong>Prompt:</strong> <span id="usedPrompt"></span></p>
                    <p><strong>Size:</strong> <span id="usedSize"></span></p>
                    <p><strong>Generated:</strong> <span id="generatedTime"></span></p>
                </div>
                
                <a id="downloadLink" class="download-btn" download>📥 Download Sawirka</a>
                <button class="download-btn" onclick="generateAnother()">🔄 Samee Mid Kale</button>
            </div>

            <div class="examples">
                <h3>💡 Tusaalooyin (Examples)</h3>
                <div class="example-grid">
                    <div class="example-card" onclick="setPrompt('A majestic eagle soaring over snow-capped mountains at sunrise')">
                        <h4>🦅 Eagle Mountains</h4>
                        <p>A majestic eagle soaring over snow-capped mountains at sunrise</p>
                    </div>
                    <div class="example-card" onclick="setPrompt('A cozy coffee shop in Paris with people reading books')">
                        <h4>☕ Paris Cafe</h4>
                        <p>A cozy coffee shop in Paris with people reading books</p>
                    </div>
                    <div class="example-card" onclick="setPrompt('A underwater scene with colorful coral reef and tropical fish')">
                        <h4>🐠 Underwater World</h4>
                        <p>A underwater scene with colorful coral reef and tropical fish</p>
                    </div>
                    <div class="example-card" onclick="setPrompt('A space station orbiting Earth with astronauts working outside')">
                        <h4>🚀 Space Station</h4>
                        <p>A space station orbiting Earth with astronauts working outside</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Check server status on load
        window.onload = function() {
            checkServerStatus();
        };

        async function checkServerStatus() {
            try {
                const response = await fetch('/api/health');
                const data = await response.json();
                
                const statusDiv = document.getElementById('statusDiv');
                statusDiv.style.display = 'block';
                
                if (response.ok) {
                    statusDiv.className = 'status success';
                    statusDiv.innerHTML = `🟢 Server wuu shaqeynayaa (${data.mock_mode ? 'Mock Mode - API key needed' : 'OpenAI Mode - Ready!'})`;
                } else {
                    statusDiv.className = 'status error';
                    statusDiv.innerHTML = '🔴 Server khalad ayaa ka dhacay';
                }
            } catch (error) {
                const statusDiv = document.getElementById('statusDiv');
                statusDiv.style.display = 'block';
                statusDiv.className = 'status error';
                statusDiv.innerHTML = '🔴 Server lama heli karo';
            }
        }

        function setPrompt(prompt) {
            document.getElementById('promptInput').value = prompt;
        }

        async function generateImage() {
            const prompt = document.getElementById('promptInput').value.trim();
            const size = document.getElementById('sizeSelector').value;
            
            if (!prompt) {
                alert('Fadlan qor waxa aad rabto inaad sawir ka sameyso!');
                return;
            }

            // Show loading with better animation
            const loadingDiv = document.getElementById('loadingDiv');
            const resultSection = document.getElementById('resultSection');
            const generateBtn = document.getElementById('generateBtn');

            loadingDiv.style.display = 'block';
            resultSection.style.display = 'none';
            generateBtn.disabled = true;
            generateBtn.textContent = '🎨 Sameeynaya...';

            // Scroll to loading area
            loadingDiv.scrollIntoView({ behavior: 'smooth', block: 'center' });

            showNotification('🎨 Sawir sameeynta waa bilaabmay...', 'info');

            try {
                const response = await fetch('/api/generate-image', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        prompt: prompt,
                        size: size,
                        user_id: 'image_generator_user'
                    })
                });

                const data = await response.json();

                if (data.success) {
                    // Show result immediately
                    const img = document.getElementById('generatedImage');
                    img.src = data.image_url;

                    // Add loading effect for image
                    img.onload = function() {
                        // Smooth reveal animation
                        img.style.opacity = '0';
                        img.style.transform = 'scale(0.8)';
                        img.style.transition = 'all 0.5s ease';

                        setTimeout(() => {
                            img.style.opacity = '1';
                            img.style.transform = 'scale(1)';
                        }, 100);
                    };

                    document.getElementById('usedPrompt').textContent = data.prompt;
                    document.getElementById('usedSize').textContent = data.size;
                    document.getElementById('generatedTime').textContent = new Date(data.timestamp).toLocaleString();
                    document.getElementById('downloadLink').href = data.image_url;
                    document.getElementById('downloadLink').download = data.filename;

                    // Show result section with animation
                    const resultSection = document.getElementById('resultSection');
                    resultSection.style.display = 'block';
                    resultSection.style.opacity = '0';
                    resultSection.style.transform = 'translateY(20px)';
                    resultSection.style.transition = 'all 0.5s ease';

                    setTimeout(() => {
                        resultSection.style.opacity = '1';
                        resultSection.style.transform = 'translateY(0)';
                    }, 200);

                    // Show success message
                    if (data.mock_mode) {
                        showNotification('🎨 Demo sawir la sameeyay! Real API key u baahan tahay sawiro dhabta ah.', 'info');
                    } else {
                        showNotification('🎉 Sawirka waa la sameeyay!', 'success');
                    }

                } else {
                    showNotification('❌ Khalad: ' + data.error, 'error');
                }

            } catch (error) {
                showNotification('❌ Khalad ayaa dhacay: ' + error.message, 'error');
            } finally {
                document.getElementById('loadingDiv').style.display = 'none';
                document.getElementById('generateBtn').disabled = false;
                document.getElementById('generateBtn').textContent = '🎨 Samee Sawir';
            }
        }

        function generateAnother() {
            document.getElementById('resultSection').style.display = 'none';
            document.getElementById('promptInput').focus();
        }

        function showNotification(message, type) {
            // Remove existing notifications
            const existing = document.querySelector('.notification');
            if (existing) {
                existing.remove();
            }

            // Create new notification
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.textContent = message;

            document.body.appendChild(notification);

            // Show notification
            setTimeout(() => {
                notification.classList.add('show');
            }, 100);

            // Hide notification after 5 seconds
            setTimeout(() => {
                notification.classList.remove('show');
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.remove();
                    }
                }, 300);
            }, 5000);
        }
    </script>
</body>
</html>
