#!/usr/bin/env python3
"""
🧪 Test HTML Code Cards
Test script to verify that ALL HTML code appears in code cards
"""

import requests
import json
import time

def test_html_code_formatting():
    """Test that HTML code always appears in code cards"""
    
    print("🧪 Testing HTML Code Card Formatting")
    print("=" * 60)
    
    # Test cases for HTML code requests
    test_cases = [
        {
            "name": "Simple HTML Request",
            "message": "write html code for hello world",
            "expected": "```html"
        },
        {
            "name": "HTML Form Request", 
            "message": "create html form",
            "expected": "```html"
        },
        {
            "name": "HTML Button Request",
            "message": "html button code",
            "expected": "```html"
        },
        {
            "name": "HTML Div Request",
            "message": "create div with text",
            "expected": "```html"
        },
        {
            "name": "HTML Page Request",
            "message": "simple html page structure",
            "expected": "```html"
        },
        {
            "name": "HTML Table Request",
            "message": "html table example",
            "expected": "```html"
        },
        {
            "name": "HTML List Request",
            "message": "html ul li list",
            "expected": "```html"
        },
        {
            "name": "HTML Link Request",
            "message": "html anchor tag",
            "expected": "```html"
        }
    ]
    
    api_url = "http://localhost:5001/api/chat"
    
    success_count = 0
    total_tests = len(test_cases)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📝 Test {i}/{total_tests}: {test_case['name']}")
        print("-" * 50)
        
        try:
            # Send request to AI
            payload = {
                "message": test_case["message"],
                "user_id": f"html_test_user_{i}_{int(time.time())}"
            }
            
            print(f"❓ Question: {test_case['message']}")
            print("🚀 Sending request to AI...")
            
            response = requests.post(
                api_url,
                json=payload,
                headers={"Content-Type": "application/json"},
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                
                if 'response' in data:
                    ai_response = data['response']
                    source = data.get('source', 'unknown')
                    tokens = data.get('tokens_used', 0)
                    
                    print(f"✅ Response received!")
                    print(f"📊 Source: {source}")
                    print(f"🔢 Tokens: {tokens}")
                    print(f"📏 Length: {len(ai_response)} characters")
                    
                    # Check for HTML code blocks
                    if "```html" in ai_response:
                        print("🎯 ✅ SUCCESS: HTML code found in proper ```html format!")
                        success_count += 1
                        
                        # Count HTML blocks
                        html_blocks = ai_response.count('```html')
                        print(f"💻 HTML code blocks: {html_blocks}")
                        
                        # Show preview of HTML code
                        import re
                        html_matches = re.findall(r'```html\n?(.*?)```', ai_response, re.DOTALL)
                        if html_matches:
                            for j, html_code in enumerate(html_matches, 1):
                                preview = html_code.strip()[:100] + "..." if len(html_code.strip()) > 100 else html_code.strip()
                                print(f"👀 HTML Block {j}: {preview}")
                        
                    else:
                        print("❌ FAILED: No ```html code blocks found!")
                        print("🔍 Checking for any HTML content...")
                        
                        # Check if there's HTML content without proper formatting
                        html_tags = ['<html>', '<div>', '<p>', '<h1>', '<form>', '<button>', '<table>', '<ul>', '<li>', '<a>']
                        found_html = any(tag in ai_response.lower() for tag in html_tags)
                        
                        if found_html:
                            print("⚠️ WARNING: HTML content found but NOT in ```html format!")
                            print("🔧 This needs to be fixed - HTML should be in code cards")
                        else:
                            print("ℹ️ No HTML content detected in response")
                        
                        # Show response preview
                        preview = ai_response[:200] + "..." if len(ai_response) > 200 else ai_response
                        print(f"📄 Response preview: {preview}")
                        
                elif 'choices' in data and data['choices']:
                    # Handle OpenAI format response
                    ai_response = data['choices'][0]['message']['content']
                    print(f"✅ OpenAI Response received!")
                    
                    if "```html" in ai_response:
                        print("🎯 ✅ SUCCESS: HTML code found in proper ```html format!")
                        success_count += 1
                    else:
                        print("❌ FAILED: No ```html code blocks found!")
                        
                else:
                    print("❌ ERROR: Invalid response format")
                    print(f"📄 Response: {data}")
                    
            else:
                print(f"❌ ERROR: HTTP {response.status_code}")
                print(f"📄 Response: {response.text}")
                
        except requests.exceptions.RequestException as e:
            print(f"❌ ERROR: Request failed - {e}")
        except Exception as e:
            print(f"❌ ERROR: Unexpected error - {e}")
        
        # Wait between requests
        if i < len(test_cases):
            print("⏳ Waiting 2 seconds...")
            time.sleep(2)
    
    # Final results
    print("\n" + "=" * 60)
    print("🎉 HTML Code Card Tests Completed!")
    print(f"📊 Results: {success_count}/{total_tests} tests passed")
    print(f"📈 Success Rate: {(success_count/total_tests)*100:.1f}%")
    
    if success_count == total_tests:
        print("✅ PERFECT! All HTML code appears in code cards!")
    elif success_count > total_tests * 0.8:
        print("🟡 GOOD! Most HTML code appears in code cards")
    else:
        print("🔴 NEEDS IMPROVEMENT! HTML code formatting needs work")
    
    print("\n💡 To see the HTML code cards:")
    print("1. Open http://localhost:5001 in your browser")
    print("2. Ask AI for HTML code examples")
    print("3. HTML code should appear in interactive cards with:")
    print("   - Dark theme background")
    print("   - 'HTML' language label")
    print("   - Copy button")
    print("   - Edit button")
    print("   - Syntax highlighting")

def test_specific_html_examples():
    """Test specific HTML examples that should always be in code cards"""
    
    print("\n🎯 Testing Specific HTML Examples")
    print("=" * 60)
    
    specific_tests = [
        "write <div> tag",
        "create <p> paragraph",
        "make <button> element", 
        "html <form> example",
        "simple <h1> heading",
        "create <table> structure",
        "make <ul> list",
        "write <a> link tag"
    ]
    
    api_url = "http://localhost:5001/api/chat"
    
    for i, test_message in enumerate(specific_tests, 1):
        print(f"\n🔍 Specific Test {i}: {test_message}")
        
        try:
            payload = {
                "message": test_message,
                "user_id": f"specific_html_test_{i}_{int(time.time())}"
            }
            
            response = requests.post(api_url, json=payload, timeout=20)
            
            if response.status_code == 200:
                data = response.json()
                ai_response = data.get('response', '') or (data.get('choices', [{}])[0].get('message', {}).get('content', ''))
                
                if "```html" in ai_response:
                    print("✅ SUCCESS: HTML in code card format")
                else:
                    print("❌ FAILED: HTML not in code card format")
                    
            time.sleep(1)
            
        except Exception as e:
            print(f"❌ ERROR: {e}")

if __name__ == "__main__":
    print("🤖 Tincada AI HTML Code Card Test")
    print("🎯 Testing that ALL HTML code appears in code cards")
    print("📅 " + time.strftime("%Y-%m-%d %H:%M:%S"))
    print()
    
    # Test HTML code formatting
    test_html_code_formatting()
    
    # Test specific HTML examples
    test_specific_html_examples()
    
    print("\n🎉 All HTML code card tests completed!")
    print("🌐 Open http://localhost:5001 to test manually!")
