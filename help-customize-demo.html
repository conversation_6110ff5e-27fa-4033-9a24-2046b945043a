<!DOCTYPE html>
<html lang="so">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Help & Customize Demo - Tincada AI</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
            padding: 2rem;
            margin: 0;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 3rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        h1 {
            text-align: center;
            font-size: 2.5rem;
            margin-bottom: 1rem;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .subtitle {
            text-align: center;
            font-size: 1.2rem;
            margin-bottom: 3rem;
            opacity: 0.9;
        }
        
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 2rem;
            margin-bottom: 3rem;
        }
        
        .feature-card {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 2rem;
            transition: all 0.3s ease;
        }
        
        .feature-card:hover {
            transform: translateY(-5px);
            border-color: rgba(255, 255, 255, 0.2);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);
        }
        
        .feature-icon {
            width: 70px;
            height: 70px;
            background: linear-gradient(45deg, #10a37f, #0d8f6f);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.8rem;
            margin-bottom: 1.5rem;
            color: white;
        }
        
        .feature-card h3 {
            color: #ffffff;
            font-size: 1.3rem;
            margin-bottom: 1rem;
        }
        
        .feature-card p {
            color: #cccccc;
            line-height: 1.6;
            margin-bottom: 1.5rem;
        }
        
        .feature-list {
            list-style: none;
            padding: 0;
            margin-bottom: 1.5rem;
        }
        
        .feature-list li {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 8px;
            color: #cccccc;
            font-size: 0.9rem;
        }
        
        .feature-list li i {
            color: #10a37f;
            font-size: 0.8rem;
        }
        
        .demo-section {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            border-left: 4px solid #4ecdc4;
        }
        
        .demo-section h2 {
            color: #4ecdc4;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .help-tabs-demo {
            display: flex;
            gap: 10px;
            margin: 1rem 0;
            flex-wrap: wrap;
        }
        
        .help-tab-demo {
            background: rgba(255, 255, 255, 0.05);
            border: none;
            color: rgba(255, 255, 255, 0.6);
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 0.8rem;
            transition: all 0.3s ease;
            border-bottom: 2px solid transparent;
        }
        
        .help-tab-demo.active {
            color: #10a37f;
            border-bottom-color: #10a37f;
            background: rgba(16, 163, 127, 0.1);
        }
        
        .customize-preview {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin: 1rem 0;
        }
        
        .customize-item {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            padding: 1rem;
            text-align: center;
        }
        
        .avatar-demo {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: linear-gradient(45deg, #10a37f, #0d8f6f);
            margin: 0 auto 0.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.2rem;
        }
        
        .theme-demo {
            width: 40px;
            height: 25px;
            border-radius: 4px;
            margin: 0 auto 0.5rem;
        }
        
        .dark-theme-demo {
            background: linear-gradient(45deg, #1a1a1a, #2d2d2d);
        }
        
        .toggle-demo {
            width: 40px;
            height: 20px;
            background: #10a37f;
            border-radius: 20px;
            margin: 0 auto 0.5rem;
            position: relative;
        }
        
        .toggle-demo::after {
            content: '';
            position: absolute;
            width: 16px;
            height: 16px;
            background: white;
            border-radius: 50%;
            top: 2px;
            right: 2px;
        }
        
        .success {
            background: rgba(76, 175, 80, 0.2);
            border: 1px solid rgba(76, 175, 80, 0.3);
            border-radius: 10px;
            padding: 1.5rem;
            margin: 2rem 0;
            border-left: 4px solid #4caf50;
            text-align: center;
        }
        
        .demo-buttons {
            display: flex;
            gap: 20px;
            justify-content: center;
            margin-top: 3rem;
            flex-wrap: wrap;
        }
        
        .demo-btn {
            background: linear-gradient(45deg, #4ecdc4, #44a08d);
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 25px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 10px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(78, 205, 196, 0.3);
        }
        
        .demo-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(78, 205, 196, 0.4);
        }
        
        .demo-btn.secondary {
            background: linear-gradient(45deg, #ff6b6b, #ee5a52);
            box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
        }
        
        .demo-btn.secondary:hover {
            box-shadow: 0 6px 20px rgba(255, 107, 107, 0.4);
        }
        
        .step-flow {
            display: flex;
            flex-direction: column;
            gap: 1rem;
            margin: 1rem 0;
        }
        
        .step-item {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 1rem;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
        }
        
        .step-number {
            width: 30px;
            height: 30px;
            background: linear-gradient(45deg, #10a37f, #0d8f6f);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            font-size: 0.9rem;
        }
        
        @media (max-width: 768px) {
            body { padding: 1rem; }
            .container { padding: 2rem 1rem; }
            h1 { font-size: 2rem; }
            .features-grid { grid-template-columns: 1fr; }
            .demo-buttons { flex-direction: column; align-items: center; }
            .help-tabs-demo { justify-content: center; }
            .customize-preview { grid-template-columns: repeat(2, 1fr); }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🛠️ Help & Customize</h1>
        <p class="subtitle">Comprehensive help system iyo powerful customization options</p>
        
        <div class="features-grid">
            <div class="feature-card">
                <div class="feature-icon">❓</div>
                <h3>Help & Support System</h3>
                <p>Comprehensive help system oo leh tutorials, shortcuts, FAQ, iyo contact options.</p>
                <ul class="feature-list">
                    <li><i class="fas fa-check"></i> Getting Started guide</li>
                    <li><i class="fas fa-check"></i> Features overview</li>
                    <li><i class="fas fa-check"></i> Keyboard shortcuts</li>
                    <li><i class="fas fa-check"></i> FAQ section</li>
                    <li><i class="fas fa-check"></i> Contact support</li>
                    <li><i class="fas fa-check"></i> Tabbed interface</li>
                </ul>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">🎨</div>
                <h3>Customize Tincada</h3>
                <p>Powerful customization options oo aad ku personalize gareyn karto experience-kaaga.</p>
                <ul class="feature-list">
                    <li><i class="fas fa-check"></i> Profile picture upload</li>
                    <li><i class="fas fa-check"></i> Theme selection</li>
                    <li><i class="fas fa-check"></i> Sound preferences</li>
                    <li><i class="fas fa-check"></i> Auto-save settings</li>
                    <li><i class="fas fa-check"></i> Typing indicators</li>
                    <li><i class="fas fa-check"></i> Settings persistence</li>
                </ul>
            </div>
        </div>
        
        <div class="demo-section">
            <h2>❓ Help System Demo</h2>
            <p>Tabbed help interface oo organized ah:</p>
            
            <div class="help-tabs-demo">
                <button class="help-tab-demo active">
                    <i class="fas fa-play"></i> Getting Started
                </button>
                <button class="help-tab-demo">
                    <i class="fas fa-star"></i> Features
                </button>
                <button class="help-tab-demo">
                    <i class="fas fa-keyboard"></i> Shortcuts
                </button>
                <button class="help-tab-demo">
                    <i class="fas fa-question"></i> FAQ
                </button>
                <button class="help-tab-demo">
                    <i class="fas fa-envelope"></i> Contact
                </button>
            </div>
            
            <div class="step-flow">
                <div class="step-item">
                    <div class="step-number">1</div>
                    <div>
                        <strong>Click profile dropdown</strong><br>
                        <small>Select "Help & Support" option</small>
                    </div>
                </div>
                <div class="step-item">
                    <div class="step-number">2</div>
                    <div>
                        <strong>Browse help tabs</strong><br>
                        <small>Getting Started, Features, Shortcuts, FAQ, Contact</small>
                    </div>
                </div>
                <div class="step-item">
                    <div class="step-number">3</div>
                    <div>
                        <strong>Get instant help</strong><br>
                        <small>Step-by-step guides iyo comprehensive documentation</small>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="demo-section">
            <h2>🎨 Customize Demo</h2>
            <p>Personalization options oo comprehensive ah:</p>
            
            <div class="customize-preview">
                <div class="customize-item">
                    <div class="avatar-demo">
                        <i class="fas fa-user"></i>
                    </div>
                    <h4>Profile Picture</h4>
                    <p>Upload custom avatar</p>
                </div>
                <div class="customize-item">
                    <div class="theme-demo dark-theme-demo"></div>
                    <h4>Theme Selection</h4>
                    <p>Dark, Light, Auto</p>
                </div>
                <div class="customize-item">
                    <div class="toggle-demo"></div>
                    <h4>Preferences</h4>
                    <p>Sound, Auto-save, etc.</p>
                </div>
            </div>
            
            <div class="step-flow">
                <div class="step-item">
                    <div class="step-number">1</div>
                    <div>
                        <strong>Open customize modal</strong><br>
                        <small>Profile dropdown → "Customize Tincada"</small>
                    </div>
                </div>
                <div class="step-item">
                    <div class="step-number">2</div>
                    <div>
                        <strong>Upload profile picture</strong><br>
                        <small>Click avatar → Select image (max 5MB)</small>
                    </div>
                </div>
                <div class="step-item">
                    <div class="step-number">3</div>
                    <div>
                        <strong>Choose theme & preferences</strong><br>
                        <small>Select theme, toggle settings, save changes</small>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="success">
            <h3>🎉 Help & Customize Complete!</h3>
            <p><strong>Help System:</strong> 5 comprehensive tabs oo leh tutorials iyo support</p>
            <p><strong>Customization:</strong> Profile pictures, themes, preferences oo persistent ah</p>
            <p><strong>User Experience:</strong> Professional interface oo easy-to-use ah</p>
        </div>
        
        <div class="demo-buttons">
            <a href="dashboard.html" class="demo-btn">
                🛠️ Test Help & Customize
            </a>
            <a href="features-demo.html" class="demo-btn secondary">
                🚀 All Features
            </a>
            <a href="ai-response-demo.html" class="demo-btn secondary">
                🤖 AI Responses
            </a>
        </div>
    </div>
</body>
</html>
