<?php
/**
 * 🔌 TINCADA AI API ENDPOINTS
 * RESTful API for chat, users, messages, and payments
 */

require_once 'database_config.php';

// Enable CORS
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');
header('Content-Type: application/json');

// Handle preflight OPTIONS requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Get request method and path
$method = $_SERVER['REQUEST_METHOD'];
$path = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
$path = str_replace('/api_endpoints.php', '', $path);

// Get input data
$input = null;
if ($method === 'POST' || $method === 'PUT') {
    $input = json_decode(file_get_contents('php://input'), true);
}

/**
 * Detect language from text
 */
function detectLanguage($text) {
    $text = strtolower($text);
    
    // Somali keywords
    if (preg_match('/\b(waa|maxaa|sidee|halkan|waxaan|tahay|ahay|salaan)\b/', $text)) {
        return 'so';
    }
    
    // Arabic keywords
    if (preg_match('/[\x{0600}-\x{06FF}]/u', $text)) {
        return 'ar';
    }
    
    // French keywords
    if (preg_match('/\b(bonjour|comment|merci|oui|non|je|tu|il)\b/', $text)) {
        return 'fr';
    }
    
    // Default to English
    return 'en';
}

/**
 * Generate AI response with OpenAI API
 */
function generateAIResponse($message, $language = 'auto') {
    // Detect language if auto
    if ($language === 'auto') {
        $language = detectLanguage($message);
    }
    
    // System messages for different languages
    $systemMessages = [
        'so' => 'Waxaad tahay Tincada AI, kaaliye AI ah oo ku hadla af Soomaali. Waxaad si fiican ugu jawaabaysaa su\'aalaha, waxaadna bixisaa macluumaad saxan oo faa\'iido leh. Haddii lagaa weydiiyo code, waxaad bixisaa code nadiif ah oo la sharaxo.',
        'en' => 'You are Tincada AI, a helpful AI assistant. You provide accurate, helpful information and can generate clean, well-commented code when requested.',
        'ar' => 'أنت تينكادا AI، مساعد ذكي مفيد. تقدم معلومات دقيقة ومفيدة ويمكنك إنشاء كود نظيف ومعلق عليه عند الطلب.',
        'fr' => 'Vous êtes Tincada AI, un assistant IA utile. Vous fournissez des informations précises et utiles et pouvez générer du code propre et bien commenté sur demande.'
    ];
    
    $systemMessage = $systemMessages[$language] ?? $systemMessages['en'];
    
    // Try OpenAI API first
    $openai_api_key = '********************************************************************************************************************************************************************';
    
    if (!empty($openai_api_key) && $openai_api_key !== 'your-api-key-here') {
        try {
            $data = [
                'model' => 'gpt-4o-mini',
                'messages' => [
                    ['role' => 'system', 'content' => $systemMessage],
                    ['role' => 'user', 'content' => $message]
                ],
                'max_tokens' => 1000,
                'temperature' => 0.7
            ];
            
            $ch = curl_init('https://api.openai.com/v1/chat/completions');
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Content-Type: application/json',
                'Authorization: Bearer ' . $openai_api_key
            ]);
            curl_setopt($ch, CURLOPT_TIMEOUT, 30);
            
            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);
            
            if ($httpCode === 200 && $response) {
                $result = json_decode($response, true);
                if (isset($result['choices'][0]['message']['content'])) {
                    return [
                        'response' => $result['choices'][0]['message']['content'],
                        'tokens_used' => $result['usage']['total_tokens'] ?? 0,
                        'source' => 'openai',
                        'language' => $language
                    ];
                }
            }
        } catch (Exception $e) {
            error_log("OpenAI API Error: " . $e->getMessage());
        }
    }
    
    // Fallback responses
    $fallbackResponses = [
        'so' => [
            'greeting' => 'Salaan! Waxaan ahay Tincada AI. Sidee kaa caawin karaa?',
            'code' => 'Waxaan kuu sameyn karaa code. Maxaad rabaa in aan ku qoro?',
            'help' => 'Waan ku caawin karaa. Su\'aal kasta weydii!',
            'default' => 'Waan fahmay su\'aashaada. Maxaad kale rabaa in aan ku caawiyo?'
        ],
        'en' => [
            'greeting' => 'Hello! I am Tincada AI. How can I help you?',
            'code' => 'I can generate code for you. What would you like me to write?',
            'help' => 'I\'m here to help! Ask me anything!',
            'default' => 'I understand your question. What else can I help you with?'
        ],
        'ar' => [
            'greeting' => 'مرحبا! أنا تينكادا AI. كيف يمكنني مساعدتك؟',
            'code' => 'يمكنني إنشاء كود لك. ماذا تريد أن أكتب؟',
            'help' => 'أنا هنا للمساعدة! اسأل أي شيء!',
            'default' => 'أفهم سؤالك. بماذا يمكنني المساعدة أيضا؟'
        ],
        'fr' => [
            'greeting' => 'Bonjour! Je suis Tincada AI. Comment puis-je vous aider?',
            'code' => 'Je peux générer du code pour vous. Que voulez-vous que j\'écrive?',
            'help' => 'Je suis là pour aider! Demandez-moi n\'importe quoi!',
            'default' => 'Je comprends votre question. Que puis-je faire d\'autre pour vous?'
        ]
    ];
    
    $langResponses = $fallbackResponses[$language] ?? $fallbackResponses['en'];
    
    // Determine response type
    $message_lower = strtolower($message);
    
    if (strpos($message_lower, 'hello') !== false || strpos($message_lower, 'salaan') !== false || strpos($message_lower, 'مرحبا') !== false || strpos($message_lower, 'bonjour') !== false) {
        $response = $langResponses['greeting'];
    } elseif (strpos($message_lower, 'code') !== false || strpos($message_lower, 'html') !== false || strpos($message_lower, 'css') !== false || strpos($message_lower, 'javascript') !== false) {
        $response = $langResponses['code'];
        
        // Add code example
        if (strpos($message_lower, 'html') !== false) {
            $response .= "\n\n```html\n<div class=\"card\">\n    <h3>Sample Card</h3>\n    <p>This is a sample HTML card</p>\n    <button class=\"btn\">Click Me</button>\n</div>\n```";
        } elseif (strpos($message_lower, 'css') !== false) {
            $response .= "\n\n```css\n.card {\n    background: white;\n    border-radius: 10px;\n    padding: 20px;\n    box-shadow: 0 2px 10px rgba(0,0,0,0.1);\n}\n\n.btn {\n    background: #007bff;\n    color: white;\n    border: none;\n    padding: 10px 20px;\n    border-radius: 5px;\n    cursor: pointer;\n}\n```";
        } elseif (strpos($message_lower, 'javascript') !== false || strpos($message_lower, 'js') !== false) {
            $response .= "\n\n```javascript\nfunction showMessage(text) {\n    alert('Message: ' + text);\n}\n\ndocument.querySelector('.btn').addEventListener('click', function() {\n    showMessage('Button clicked!');\n});\n```";
        }
    } elseif (strpos($message_lower, 'help') !== false || strpos($message_lower, 'caawin') !== false || strpos($message_lower, 'مساعدة') !== false || strpos($message_lower, 'aide') !== false) {
        $response = $langResponses['help'];
    } else {
        $response = $langResponses['default'];
    }
    
    return [
        'response' => $response,
        'tokens_used' => ceil(strlen($response) / 4), // Rough estimate
        'source' => 'fallback',
        'language' => $language
    ];
}

// Route handling
switch ($path) {
    case '/chat':
        if ($method === 'POST') {
            if (!$input || !isset($input['message'])) {
                sendError('Message is required');
            }
            
            $message = sanitizeInput($input['message']);
            $user_id = $input['user_id'] ?? 1;
            $session_id = $input['session_id'] ?? 'session_' . time();
            $language = $input['language'] ?? 'auto';
            
            // Rate limiting check
            if (!rateLimitCheck($user_id)) {
                sendError('Rate limit exceeded. Please try again later.', 429);
            }
            
            // Generate AI response
            $aiResult = generateAIResponse($message, $language);
            
            // Save to database if available
            if ($db && $messageManager) {
                try {
                    // Save user message
                    $db->prepare("
                        INSERT INTO chat_messages (user_id, conversation_id, message_type, message_content, message_language, created_at) 
                        VALUES (?, ?, 'user', ?, ?, NOW())
                    ")->execute([$user_id, $session_id, $message, $aiResult['language']]);
                    
                    // Save AI response
                    $db->prepare("
                        INSERT INTO chat_messages (user_id, conversation_id, message_type, message_content, tokens_used, response_source, message_language, created_at) 
                        VALUES (?, ?, 'ai', ?, ?, ?, ?, NOW())
                    ")->execute([$user_id, $session_id, $aiResult['response'], $aiResult['tokens_used'], $aiResult['source'], $aiResult['language']]);
                    
                    // Log activity
                    logActivity($user_id, 'chat_message', "Message: " . substr($message, 0, 100));
                    
                } catch (Exception $e) {
                    error_log("Database save error: " . $e->getMessage());
                }
            }
            
            sendJsonResponse([
                'success' => true,
                'response' => $aiResult['response'],
                'session_id' => $session_id,
                'language' => $aiResult['language'],
                'tokens_used' => $aiResult['tokens_used'],
                'source' => $aiResult['source'],
                'timestamp' => date('Y-m-d H:i:s')
            ]);
        }
        break;
    
    case '/users':
        if ($method === 'GET') {
            $page = $_GET['page'] ?? 1;
            $limit = $_GET['limit'] ?? 10;
            
            if ($userManager) {
                $users = $userManager->getAllUsers($page, $limit);
                sendJsonResponse(['success' => true, 'data' => $users]);
            } else {
                // Mock data
                sendJsonResponse([
                    'success' => true,
                    'data' => [
                        ['username' => 'Ahmed123', 'email' => '<EMAIL>', 'account_type' => 'premium'],
                        ['username' => 'Fatima_AI', 'email' => '<EMAIL>', 'account_type' => 'free'],
                        ['username' => 'Mohamed_Dev', 'email' => '<EMAIL>', 'account_type' => 'enterprise']
                    ]
                ]);
            }
        }
        break;
    
    case '/messages':
        if ($method === 'GET') {
            $limit = $_GET['limit'] ?? 10;
            
            if ($messageManager) {
                $messages = $messageManager->getRecentMessages($limit);
                sendJsonResponse(['success' => true, 'data' => $messages]);
            } else {
                // Mock data
                sendJsonResponse([
                    'success' => true,
                    'data' => [
                        ['message_content' => 'Salaan, sidee tahay?', 'message_type' => 'user', 'created_at' => date('Y-m-d H:i:s')],
                        ['message_content' => 'Waan fiican ahay, mahadsanid!', 'message_type' => 'ai', 'created_at' => date('Y-m-d H:i:s')]
                    ]
                ]);
            }
        }
        break;
    
    case '/payments':
        if ($method === 'GET') {
            $limit = $_GET['limit'] ?? 10;
            
            if ($paymentManager) {
                $payments = $paymentManager->getRecentPayments($limit);
                sendJsonResponse(['success' => true, 'data' => $payments]);
            } else {
                // Mock data
                sendJsonResponse([
                    'success' => true,
                    'data' => [
                        ['amount' => 29.99, 'payment_status' => 'completed', 'created_at' => date('Y-m-d H:i:s')],
                        ['amount' => 9.99, 'payment_status' => 'pending', 'created_at' => date('Y-m-d H:i:s')]
                    ]
                ]);
            }
        }
        break;
    
    case '/stats':
        if ($method === 'GET') {
            $stats = [];
            
            if ($userManager && $messageManager && $paymentManager) {
                $stats = [
                    'users' => $userManager->getUserStats(),
                    'messages' => $messageManager->getMessageStats(),
                    'payments' => $paymentManager->getPaymentStats(),
                    'daily_activity' => $messageManager->getDailyMessageCount(7),
                    'languages' => $messageManager->getLanguageStats()
                ];
            } else {
                // Mock stats
                $stats = [
                    'users' => ['total_users' => 156, 'active_today' => 23],
                    'messages' => ['total_messages' => 2847, 'today_messages' => 89],
                    'payments' => ['total_revenue' => 1250.75, 'total_transactions' => 67],
                    'daily_activity' => [
                        ['date' => date('Y-m-d', strtotime('-6 days')), 'count' => 45],
                        ['date' => date('Y-m-d', strtotime('-5 days')), 'count' => 67],
                        ['date' => date('Y-m-d', strtotime('-4 days')), 'count' => 89],
                        ['date' => date('Y-m-d', strtotime('-3 days')), 'count' => 123],
                        ['date' => date('Y-m-d', strtotime('-2 days')), 'count' => 98],
                        ['date' => date('Y-m-d', strtotime('-1 day')), 'count' => 156],
                        ['date' => date('Y-m-d'), 'count' => 89]
                    ],
                    'languages' => [
                        ['message_language' => 'so', 'count' => 1245],
                        ['message_language' => 'en', 'count' => 987],
                        ['message_language' => 'ar', 'count' => 456],
                        ['message_language' => 'fr', 'count' => 159]
                    ]
                ];
            }
            
            sendJsonResponse(['success' => true, 'data' => $stats]);
        }
        break;
    
    case '/health':
        sendJsonResponse([
            'success' => true,
            'status' => 'healthy',
            'message' => 'Tincada AI API is working!',
            'database' => $db ? 'connected' : 'disconnected',
            'timestamp' => date('Y-m-d H:i:s'),
            'version' => '2.0.0'
        ]);
        break;
    
    default:
        sendError('Endpoint not found', 404);
}
?>
