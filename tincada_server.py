#!/usr/bin/env python3
"""
Tincada AI Real Server - Complete OpenAI Integration
Supports all languages and comprehensive AI responses
"""

from flask import Flask, request, jsonify, send_from_directory
from flask_cors import CORS
import requests
import json
import os
import time
import logging
from datetime import datetime

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)
CORS(app)

# OpenAI Configuration
OPENAI_API_KEY = '********************************************************************************************************************************************************************'
OPENAI_API_URL = 'https://api.openai.com/v1/chat/completions'

def get_openai_response(message, chat_history=None):
    """
    Get response from OpenAI API with comprehensive error handling
    """
    try:
        # Prepare system prompt for multilingual support
        system_prompt = """You are Tincada AI, an advanced AI assistant with the following capabilities:

LANGUAGE SUPPORT:
- Respond in the same language the user writes in
- Support Somali, English, Arabic, French, Spanish, and other major languages
- When unsure of language, default to Somali or English

PERSONALITY:
- Professional, helpful, and friendly
- Knowledgeable about technology, programming, science, culture
- Respectful of cultural differences
- Patient and encouraging

CAPABILITIES:
- Programming help (JavaScript, Python, HTML, CSS, Java, C++, etc.)
- Code examples with proper syntax highlighting
- Technical explanations
- General knowledge questions
- Creative writing and content creation
- Problem-solving assistance
- Educational support

RESPONSE FORMAT:
- Use proper markdown formatting for code blocks
- Include language specification for code: ```javascript, ```python, etc.
- Provide clear, detailed explanations
- Use emojis appropriately to enhance communication
- Structure responses with headers and bullet points when helpful

Always be helpful, accurate, and engaging in your responses."""

        # Prepare messages
        messages = [{"role": "system", "content": system_prompt}]
        
        # Add chat history (last 10 messages for context)
        if chat_history:
            for msg in chat_history[-10:]:
                messages.append({
                    "role": msg.get("role", "user"),
                    "content": msg.get("content", "")
                })
        
        # Add current message
        messages.append({"role": "user", "content": message})
        
        # Prepare API request
        headers = {
            'Authorization': f'Bearer {OPENAI_API_KEY}',
            'Content-Type': 'application/json'
        }
        
        data = {
            'model': 'gpt-4o-mini',
            'messages': messages,
            'max_tokens': 2000,
            'temperature': 0.7,
            'top_p': 0.9,
            'frequency_penalty': 0.1,
            'presence_penalty': 0.1
        }
        
        logger.info(f"🚀 Making OpenAI API request for message: {message[:50]}...")
        
        # Make API request
        response = requests.post(
            OPENAI_API_URL,
            headers=headers,
            json=data,
            timeout=45
        )
        
        logger.info(f"📊 OpenAI API response status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            ai_response = result['choices'][0]['message']['content']
            usage = result.get('usage', {})
            
            logger.info(f"✅ OpenAI response received: {len(ai_response)} characters")
            
            return {
                'success': True,
                'response': ai_response,
                'source': 'openai',
                'model': 'gpt-4o-mini',
                'tokens_used': usage.get('total_tokens', 0),
                'prompt_tokens': usage.get('prompt_tokens', 0),
                'completion_tokens': usage.get('completion_tokens', 0)
            }
        
        elif response.status_code == 429:
            logger.warning("⚠️ OpenAI API rate limit exceeded")
            return {
                'success': False,
                'error': 'rate_limit',
                'message': 'API rate limit exceeded. Please try again in a moment.'
            }
        
        elif response.status_code == 401:
            logger.error("❌ OpenAI API authentication failed")
            return {
                'success': False,
                'error': 'auth_failed',
                'message': 'API authentication failed. Please check API key.'
            }
        
        else:
            logger.error(f"❌ OpenAI API error: {response.status_code} - {response.text}")
            return {
                'success': False,
                'error': 'api_error',
                'message': f'API error: {response.status_code}'
            }
            
    except requests.exceptions.Timeout:
        logger.error("⏰ OpenAI API request timeout")
        return {
            'success': False,
            'error': 'timeout',
            'message': 'Request timeout. Please try again.'
        }
    
    except requests.exceptions.ConnectionError:
        logger.error("🌐 OpenAI API connection error")
        return {
            'success': False,
            'error': 'connection_error',
            'message': 'Connection error. Please check your internet connection.'
        }
    
    except Exception as e:
        logger.error(f"💥 Unexpected error: {str(e)}")
        return {
            'success': False,
            'error': 'unexpected_error',
            'message': f'Unexpected error: {str(e)}'
        }

def get_fallback_response(message):
    """
    Intelligent fallback response when OpenAI API is unavailable
    """
    message_lower = message.lower()
    
    # Detect language and respond appropriately
    if any(word in message_lower for word in ['salam', 'maxaad', 'sidee', 'waa', 'tahay']):
        # Somali language detected
        return """Salam alaykum! 🌟 Waxaan ahay Tincada AI.

Hadda waxaan isticmaalayaa fallback mode maxaa yeelay OpenAI API-gu ma shaqaynayo. Laakiin weli waan kaa caawin karaa:

📝 **Su'aalo guud**
💻 **Programming tusaalayaal**  
🔄 **Macluumaad asaasi ah**
📚 **Talo iyo hagitaan**

Fadlan dib ii soo qor su'aashaada si cad!"""
    
    elif any(word in message_lower for word in ['hello', 'hi', 'how', 'what', 'help']):
        # English language detected
        return """Hello! 👋 I'm Tincada AI.

I'm currently running in fallback mode because the OpenAI API is unavailable. However, I can still help you with:

📝 **General questions**
💻 **Programming examples**
🔄 **Basic information**
📚 **Advice and guidance**

Please rephrase your question and I'll do my best to help!"""
    
    else:
        # Default multilingual response
        return """Hello! مرحبا! Salam alaykum! 🌍

I'm Tincada AI, currently in fallback mode. I can help with:
- Programming questions
- General information  
- Multiple languages support

Please ask your question clearly and I'll assist you!"""

@app.route('/')
def index():
    """Serve the main dashboard"""
    return send_from_directory('.', 'dashboard.html')

@app.route('/<path:filename>')
def serve_static(filename):
    """Serve static files"""
    try:
        return send_from_directory('.', filename)
    except Exception as e:
        logger.error(f"Error serving file {filename}: {e}")
        return jsonify({'error': 'File not found'}), 404

@app.route('/api/health', methods=['GET'])
def health():
    """Health check endpoint"""
    return jsonify({
        'status': 'healthy',
        'service': 'Tincada AI Server',
        'version': '2.0.0',
        'timestamp': datetime.now().isoformat(),
        'openai_configured': bool(OPENAI_API_KEY),
        'endpoints': {
            'chat': '/api/chat',
            'health': '/api/health'
        }
    })

@app.route('/api/chat', methods=['POST'])
def chat():
    """
    Main chat endpoint with comprehensive OpenAI integration
    """
    try:
        # Get request data
        data = request.get_json()
        
        if not data:
            return jsonify({'error': 'No JSON data provided'}), 400
        
        message = data.get('message', '').strip()
        user_id = data.get('user_id', 'anonymous')
        chat_history = data.get('chat_history', [])
        
        if not message:
            return jsonify({'error': 'Message is required'}), 400
        
        logger.info(f"💬 Chat request from {user_id}: {message[:100]}...")
        
        # Get OpenAI response
        result = get_openai_response(message, chat_history)
        
        if result['success']:
            # Successful OpenAI response
            return jsonify({
                'response': result['response'],
                'source': result['source'],
                'model': result['model'],
                'tokens_used': result['tokens_used'],
                'prompt_tokens': result['prompt_tokens'],
                'completion_tokens': result['completion_tokens'],
                'timestamp': datetime.now().isoformat(),
                'status': 'success'
            })
        
        else:
            # OpenAI failed, use fallback
            logger.warning(f"OpenAI failed: {result.get('error', 'unknown')}")
            fallback_response = get_fallback_response(message)
            
            return jsonify({
                'response': fallback_response,
                'source': 'fallback',
                'model': 'tincada-fallback',
                'tokens_used': 0,
                'error_reason': result.get('error', 'unknown'),
                'timestamp': datetime.now().isoformat(),
                'status': 'fallback'
            })
            
    except Exception as e:
        logger.error(f"❌ Chat endpoint error: {str(e)}")
        return jsonify({
            'error': 'Internal server error',
            'message': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500

if __name__ == '__main__':
    print("🚀 Starting Tincada AI Real Server v2.0")
    print("=" * 60)
    print(f"🔑 OpenAI API: {'✅ Configured' if OPENAI_API_KEY else '❌ Not configured'}")
    print(f"🌐 Server URL: http://localhost:5001")
    print(f"📱 Dashboard: http://localhost:5001/dashboard.html")
    print(f"🔧 Health Check: http://localhost:5001/api/health")
    print(f"💬 Chat API: http://localhost:5001/api/chat")
    print("=" * 60)
    print("🎯 Features:")
    print("   ✅ Real OpenAI GPT-4o-mini integration")
    print("   ✅ Multilingual support (Somali, English, Arabic, etc.)")
    print("   ✅ Comprehensive error handling")
    print("   ✅ Smart fallback responses")
    print("   ✅ Professional code examples")
    print("   ✅ Chat history context")
    print("=" * 60)
    print("⚡ Press Ctrl+C to stop the server")
    print()
    
    app.run(host='0.0.0.0', port=5001, debug=True)
