#!/usr/bin/env python3
"""
🔑 Test New OpenAI API Key
Testing the latest API key provided by user
"""

import requests
from openai import OpenAI

def test_direct_api():
    """Test the new API key directly"""
    print("🔑 TESTING NEW OPENAI API KEY")
    print("=" * 60)
    
    api_key = "********************************************************************************************************************************************************************"
    print(f"API Key: {api_key}")
    print("=" * 60)
    
    try:
        client = OpenAI(api_key=api_key)
        
        # Test basic access
        print("📡 Testing basic API access...")
        models = client.models.list()
        print("✅ Basic API access: SUCCESS")
        
        # Test chat completion
        print("💬 Testing chat completion...")
        response = client.chat.completions.create(
            model="gpt-4o-mini",
            messages=[
                {"role": "system", "content": "You are a helpful assistant that responds in the user's language."},
                {"role": "user", "content": "Maxaad tahay? Ku jawaab Somali."}
            ],
            max_tokens=150
        )
        
        ai_response = response.choices[0].message.content
        print("✅ Chat completion: SUCCESS")
        print(f"🤖 Response: {ai_response}")
        return True
        
    except Exception as e:
        print(f"❌ API Error: {e}")
        return False

def test_multilingual_api():
    """Test multilingual capabilities with new API"""
    print("\n🌍 TESTING MULTILINGUAL CAPABILITIES")
    print("=" * 50)
    
    test_cases = [
        ("Somali", "Maxaad tahay?"),
        ("English", "What is the capital of Somalia?"),
        ("Arabic", "ما هي عاصمة الصومال؟"),
        ("French", "Quelle est la capitale de la Somalie?")
    ]
    
    for lang, question in test_cases:
        print(f"\n🗣️ {lang}: {question}")
        
        try:
            response = requests.post(
                "http://localhost:5001/api/chat",
                json={"message": question, "user_id": "test"},
                timeout=25
            )
            
            if response.status_code == 200:
                data = response.json()
                answer = data['response']
                print(f"✅ Response: {answer[:80]}...")
            else:
                print(f"❌ HTTP Error: {response.status_code}")
                
        except Exception as e:
            print(f"❌ Error: {e}")

def test_code_generation():
    """Test code generation with new API"""
    print("\n🔧 TESTING CODE GENERATION")
    print("=" * 40)
    
    code_requests = [
        "Ku qor HTML button ah oo cagaar ah",
        "Write a simple JavaScript function",
        "Create CSS for a red div"
    ]
    
    for request in code_requests:
        print(f"\n💻 Request: {request}")
        
        try:
            response = requests.post(
                "http://localhost:5001/api/chat",
                json={"message": request, "user_id": "code_test"},
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                answer = data['response']
                
                # Check if code is properly formatted
                if "```" in answer:
                    print("✅ Code formatted in cards: SUCCESS")
                    print(f"📝 Preview: {answer[:100]}...")
                else:
                    print("⚠️ Code not in card format")
                    print(f"📝 Response: {answer[:100]}...")
            else:
                print(f"❌ HTTP Error: {response.status_code}")
                
        except Exception as e:
            print(f"❌ Error: {e}")

def main():
    """Run comprehensive test of new API key"""
    print("🚀 COMPREHENSIVE API KEY TEST")
    print("=" * 70)
    print("Testing new OpenAI API key: 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")
    print("=" * 70)
    
    # Test 1: Direct API access
    api_works = test_direct_api()
    
    if api_works:
        # Test 2: Multilingual capabilities
        test_multilingual_api()
        
        # Test 3: Code generation
        test_code_generation()
        
        print("\n" + "=" * 70)
        print("🎉 SUCCESS! New API key is working perfectly!")
        print("✅ AI uses the new API for ALL responses")
        print("🌍 AI speaks ALL world languages")
        print("💬 AI responds to every question using the API")
        print("🔧 Code formatting works with cards")
        print("🌐 Server: http://localhost:5001")
        
    else:
        print("\n❌ API key has issues - check configuration")

if __name__ == "__main__":
    main()
