#!/usr/bin/env python3
"""
🧪 Live Dashboard Test
Test dashboard code cards in real-time
"""

import requests
import json
import time

def test_dashboard_live():
    """Test dashboard with live API calls"""
    
    print("🧪 Live Dashboard Code Cards Test")
    print("=" * 50)
    
    api_url = "http://localhost:5001/api/chat"
    
    # Simple HTML test
    payload = {
        "message": "write simple html button",
        "user_id": "dashboard_live_test"
    }
    
    print("❓ Testing: write simple html button")
    print("🚀 Sending request...")
    
    try:
        response = requests.post(
            api_url,
            json=payload,
            headers={"Content-Type": "application/json"},
            timeout=15
        )
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Response received!")
            
            # Check response format
            if 'response' in data:
                ai_response = data['response']
                print(f"📊 Response type: Custom format")
            elif 'choices' in data:
                ai_response = data['choices'][0]['message']['content']
                print(f"📊 Response type: OpenAI format")
            else:
                print("❌ Unknown response format")
                return
            
            print(f"📏 Response length: {len(ai_response)} characters")
            
            # Check for HTML code blocks
            if "```html" in ai_response:
                print("🎯 ✅ SUCCESS: Found ```html code block!")
                print("💻 Dashboard should show this in a code card")
                
                # Show full response
                print("\n📄 Full AI Response:")
                print("-" * 40)
                print(ai_response)
                print("-" * 40)
                
                print("\n🌐 Dashboard Instructions:")
                print("1. Open: http://127.0.0.1:5001/dashboard.html")
                print("2. Ask AI: 'write simple html button'")
                print("3. You should see:")
                print("   ✅ Dark code card with 'HTML' label")
                print("   ✅ Copy button (top right)")
                print("   ✅ Edit button (top right)")
                print("   ✅ Syntax highlighting")
                print("   ✅ Professional dark theme")
                
            else:
                print("❌ FAILED: No ```html code block found!")
                print("⚠️ Dashboard will NOT show code card")
                print(f"Response: {ai_response}")
                
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            print(f"Response: {response.text}")
            
    except Exception as e:
        print(f"❌ Error: {e}")

def test_multiple_languages():
    """Test multiple programming languages"""
    
    print("\n🎨 Testing Multiple Languages")
    print("=" * 50)
    
    tests = [
        ("HTML", "write html div tag"),
        ("CSS", "create css button style"),
        ("JavaScript", "javascript alert function"),
        ("Python", "python hello world")
    ]
    
    api_url = "http://localhost:5001/api/chat"
    
    for lang, message in tests:
        print(f"\n📝 Testing {lang}: {message}")
        
        try:
            payload = {
                "message": message,
                "user_id": f"dashboard_lang_test_{lang.lower()}"
            }
            
            response = requests.post(api_url, json=payload, timeout=15)
            
            if response.status_code == 200:
                data = response.json()
                ai_response = data.get('response', '') or (data.get('choices', [{}])[0].get('message', {}).get('content', ''))
                
                # Check for code blocks
                expected_format = f"```{lang.lower()}"
                if expected_format in ai_response:
                    print(f"✅ SUCCESS: Found {expected_format} code block")
                elif "```" in ai_response:
                    print(f"🟡 PARTIAL: Found code block but not {expected_format}")
                else:
                    print(f"❌ FAILED: No code block found")
                    
            time.sleep(1)
            
        except Exception as e:
            print(f"❌ Error: {e}")

if __name__ == "__main__":
    print("🤖 Dashboard Live Test")
    print("🎯 Testing real-time code card display")
    print("📅 " + time.strftime("%Y-%m-%d %H:%M:%S"))
    print()
    
    # Test dashboard live
    test_dashboard_live()
    
    # Test multiple languages
    test_multiple_languages()
    
    print("\n🎉 Live dashboard tests completed!")
    print("🌐 Open http://127.0.0.1:5001/dashboard.html to see results!")
