<!DOCTYPE html>
<html lang="so">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Complete AI System Demo - Tincada AI</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
            padding: 2rem;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 3rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        h1 {
            text-align: center;
            font-size: 3rem;
            margin-bottom: 1rem;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .subtitle {
            text-align: center;
            font-size: 1.3rem;
            margin-bottom: 3rem;
            opacity: 0.9;
        }

        .features-overview {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-bottom: 4rem;
        }

        .feature-card {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 2rem;
            transition: all 0.3s ease;
            text-align: center;
            cursor: pointer;
        }

        .feature-card:hover {
            transform: translateY(-5px);
            border-color: rgba(255, 255, 255, 0.2);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);
        }

        .feature-card.working {
            border-color: rgba(76, 175, 80, 0.5);
            background: rgba(76, 175, 80, 0.1);
        }

        .feature-card.partial {
            border-color: rgba(255, 193, 7, 0.5);
            background: rgba(255, 193, 7, 0.1);
        }

        .feature-card.demo {
            border-color: rgba(33, 150, 243, 0.5);
            background: rgba(33, 150, 243, 0.1);
        }

        .feature-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
        }

        .feature-card h3 {
            color: #ffffff;
            margin-bottom: 0.5rem;
        }

        .feature-card p {
            color: #cccccc;
            line-height: 1.6;
            margin-bottom: 1rem;
        }

        .feature-status {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
            margin-top: 0.5rem;
        }

        .status-working {
            background: rgba(76, 175, 80, 0.2);
            color: #4caf50;
        }

        .status-partial {
            background: rgba(255, 193, 7, 0.2);
            color: #ffc107;
        }

        .status-demo {
            background: rgba(33, 150, 243, 0.2);
            color: #2196f3;
        }

        .system-stats {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 3rem;
            border-left: 4px solid #4ecdc4;
        }

        .system-stats h2 {
            color: #4ecdc4;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
            margin-top: 1.5rem;
        }

        .stat-item {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            padding: 1.5rem;
            text-align: center;
        }

        .stat-value {
            font-size: 2rem;
            font-weight: 700;
            color: #4ecdc4;
            margin-bottom: 0.5rem;
        }

        .stat-label {
            color: #cccccc;
            font-size: 0.9rem;
        }

        .demo-links {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 3rem;
        }

        .demo-links h3 {
            color: #4ecdc4;
            margin-bottom: 1.5rem;
            text-align: center;
        }

        .links-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
        }

        .demo-link {
            background: rgba(0, 0, 0, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 1.5rem;
            text-decoration: none;
            color: white;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .demo-link:hover {
            transform: translateY(-2px);
            border-color: rgba(255, 255, 255, 0.2);
            background: rgba(0, 0, 0, 0.4);
        }

        .demo-link-icon {
            font-size: 2rem;
            color: #4ecdc4;
        }

        .demo-link-content h4 {
            margin-bottom: 0.5rem;
            color: #ffffff;
        }

        .demo-link-content p {
            color: #cccccc;
            font-size: 0.9rem;
        }

        .tech-stack {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 3rem;
        }

        .tech-stack h3 {
            color: #4ecdc4;
            margin-bottom: 1.5rem;
            text-align: center;
        }

        .tech-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 1rem;
        }

        .tech-item {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 10px;
            padding: 1rem;
            text-align: center;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .tech-item h5 {
            color: #ffffff;
            margin-bottom: 0.5rem;
        }

        .tech-item p {
            color: #cccccc;
            font-size: 0.8rem;
        }

        .action-buttons {
            display: flex;
            gap: 20px;
            justify-content: center;
            margin-top: 3rem;
            flex-wrap: wrap;
        }

        .action-btn {
            background: linear-gradient(45deg, #4ecdc4, #44a08d);
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 25px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 10px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(78, 205, 196, 0.3);
        }

        .action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(78, 205, 196, 0.4);
        }

        .action-btn.primary {
            background: linear-gradient(45deg, #10a37f, #0d8f6f);
            box-shadow: 0 4px 15px rgba(16, 163, 127, 0.3);
        }

        .action-btn.primary:hover {
            box-shadow: 0 6px 20px rgba(16, 163, 127, 0.4);
        }

        .action-btn.secondary {
            background: linear-gradient(45deg, #ff6b6b, #ee5a52);
            box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
        }

        .action-btn.secondary:hover {
            box-shadow: 0 6px 20px rgba(255, 107, 107, 0.4);
        }

        @media (max-width: 768px) {
            body { padding: 1rem; }
            .container { padding: 2rem 1rem; }
            h1 { font-size: 2.5rem; }
            .features-overview { grid-template-columns: 1fr; }
            .action-buttons { flex-direction: column; align-items: center; }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Complete AI System</h1>
        <p class="subtitle">Tincada AI - Advanced AI platform with multilingual support, image generation, and video creation</p>
        
        <div class="features-overview">
            <div class="feature-card working" onclick="window.open('multilingual-demo.html', '_blank')">
                <div class="feature-icon">🌍</div>
                <h3>Multilingual Chat</h3>
                <p>AI chat in 7 languages with automatic detection and natural responses</p>
                <span class="feature-status status-working">✅ Working</span>
            </div>
            
            <div class="feature-card partial" onclick="window.open('image-generation-demo.html', '_blank')">
                <div class="feature-icon">🎨</div>
                <h3>Image Generation</h3>
                <p>DALL-E 3 integration for creating stunning AI-generated images</p>
                <span class="feature-status status-partial">⚠️ Needs Credits</span>
            </div>
            
            <div class="feature-card demo" onclick="window.open('video-generation-demo.html', '_blank')">
                <div class="feature-icon">🎬</div>
                <h3>Video Generation</h3>
                <p>Convert images to videos using Replicate Stable Video Diffusion</p>
                <span class="feature-status status-demo">🔧 Demo Ready</span>
            </div>
            
            <div class="feature-card working" onclick="window.open('login.html', '_blank')">
                <div class="feature-icon">🔐</div>
                <h3>Social Authentication</h3>
                <p>Google and Apple Sign In with OAuth integration</p>
                <span class="feature-status status-working">✅ Working</span>
            </div>
            
            <div class="feature-card working" onclick="window.open('dashboard.html', '_blank')">
                <div class="feature-icon">🛠️</div>
                <h3>AI Tools Suite</h3>
                <p>Think Longer, Deep Research, Web Search, Canvas tools</p>
                <span class="feature-status status-working">✅ Working</span>
            </div>
            
            <div class="feature-card working">
                <div class="feature-icon">📱</div>
                <h3>Responsive Design</h3>
                <p>Mobile-optimized interface with modern glassmorphism design</p>
                <span class="feature-status status-working">✅ Working</span>
            </div>
        </div>
        
        <div class="system-stats">
            <h2>📊 System Status</h2>
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-value" id="chatStatus">✅</div>
                    <div class="stat-label">Chat API</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">7</div>
                    <div class="stat-label">Languages</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="imageStatus">⚠️</div>
                    <div class="stat-label">Image Gen</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="videoStatus">🔧</div>
                    <div class="stat-label">Video Gen</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">100%</div>
                    <div class="stat-label">Uptime</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">5</div>
                    <div class="stat-label">AI Tools</div>
                </div>
            </div>
        </div>
        
        <div class="demo-links">
            <h3>🧪 Live Demos</h3>
            <div class="links-grid">
                <a href="multilingual-demo.html" class="demo-link">
                    <div class="demo-link-icon">🌍</div>
                    <div class="demo-link-content">
                        <h4>Multilingual Chat</h4>
                        <p>Test AI in 7 languages</p>
                    </div>
                </a>
                
                <a href="image-generation-demo.html" class="demo-link">
                    <div class="demo-link-icon">🎨</div>
                    <div class="demo-link-content">
                        <h4>Image Generation</h4>
                        <p>Create AI images</p>
                    </div>
                </a>
                
                <a href="video-generation-demo.html" class="demo-link">
                    <div class="demo-link-icon">🎬</div>
                    <div class="demo-link-content">
                        <h4>Video Generation</h4>
                        <p>Image to video conversion</p>
                    </div>
                </a>
                
                <a href="login.html" class="demo-link">
                    <div class="demo-link-icon">🔐</div>
                    <div class="demo-link-content">
                        <h4>Social Login</h4>
                        <p>Google & Apple Sign In</p>
                    </div>
                </a>
                
                <a href="dashboard.html" class="demo-link">
                    <div class="demo-link-icon">🏠</div>
                    <div class="demo-link-content">
                        <h4>Main Dashboard</h4>
                        <p>Complete AI interface</p>
                    </div>
                </a>
                
                <a href="api-key-setup-guide.html" class="demo-link">
                    <div class="demo-link-icon">🔑</div>
                    <div class="demo-link-content">
                        <h4>API Setup Guide</h4>
                        <p>Configuration help</p>
                    </div>
                </a>
            </div>
        </div>
        
        <div class="tech-stack">
            <h3>🛠️ Technology Stack</h3>
            <div class="tech-grid">
                <div class="tech-item">
                    <h5>Frontend</h5>
                    <p>HTML5, CSS3, JavaScript</p>
                </div>
                <div class="tech-item">
                    <h5>Backend</h5>
                    <p>Python Flask</p>
                </div>
                <div class="tech-item">
                    <h5>AI Models</h5>
                    <p>OpenAI GPT-4, DALL-E 3</p>
                </div>
                <div class="tech-item">
                    <h5>Video AI</h5>
                    <p>Replicate API</p>
                </div>
                <div class="tech-item">
                    <h5>Authentication</h5>
                    <p>Google OAuth, Apple Sign In</p>
                </div>
                <div class="tech-item">
                    <h5>Design</h5>
                    <p>Glassmorphism, Responsive</p>
                </div>
            </div>
        </div>
        
        <div class="action-buttons">
            <a href="dashboard.html" class="action-btn primary">
                🏠 Main Dashboard
            </a>
            <a href="multilingual-demo.html" class="action-btn">
                🌍 Test Multilingual
            </a>
            <a href="image-generation-demo.html" class="action-btn">
                🎨 Generate Images
            </a>
            <a href="video-generation-demo.html" class="action-btn secondary">
                🎬 Create Videos
            </a>
        </div>
    </div>

    <script>
        // Check system status on page load
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 Complete AI System Demo loaded');
            
            // Test server connection and update status
            fetch('http://localhost:5001/api/health')
                .then(response => response.json())
                .then(data => {
                    console.log('System status:', data);
                    updateSystemStatus(data);
                })
                .catch(error => {
                    console.log('Server not connected:', error);
                    updateSystemStatus(null);
                });
        });

        function updateSystemStatus(data) {
            const chatStatus = document.getElementById('chatStatus');
            const imageStatus = document.getElementById('imageStatus');
            const videoStatus = document.getElementById('videoStatus');
            
            if (data) {
                // Update chat status
                chatStatus.textContent = data.openai_available ? '✅' : '❌';
                chatStatus.style.color = data.openai_available ? '#4caf50' : '#f44336';
                
                // Update image status
                imageStatus.textContent = data.openai_available ? '⚠️' : '❌';
                imageStatus.style.color = data.openai_available ? '#ffc107' : '#f44336';
                
                // Update video status
                videoStatus.textContent = data.replicate_available ? '✅' : '🔧';
                videoStatus.style.color = data.replicate_available ? '#4caf50' : '#2196f3';
                
            } else {
                // Server not connected
                chatStatus.textContent = '❌';
                chatStatus.style.color = '#f44336';
                imageStatus.textContent = '❌';
                imageStatus.style.color = '#f44336';
                videoStatus.textContent = '❌';
                videoStatus.style.color = '#f44336';
            }
        }

        // Add click animations to feature cards
        document.querySelectorAll('.feature-card').forEach(card => {
            card.addEventListener('click', function() {
                this.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    this.style.transform = 'translateY(-5px)';
                }, 150);
            });
        });
    </script>
</body>
</html>
