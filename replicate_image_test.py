#!/usr/bin/env python3
"""
Replicate Image Generation Test
Alternative to OpenAI DALL-E
"""

import requests
import base64
import time
import json

# Replicate API Token
REPLICATE_API_TOKEN = "****************************************"

def generate_image_replicate(prompt, filename="replicate_image.png"):
    """
    Generate image using Replicate API (FLUX model)
    Free alternative to OpenAI DALL-E
    """
    
    print(f"🎨 Sameeynaya sawir adoo isticmaalaya Replicate: {prompt}")
    print(f"📁 Filename: {filename}")
    
    try:
        headers = {
            'Authorization': f'Token {REPLICATE_API_TOKEN}',
            'Content-Type': 'application/json'
        }
        
        # Using FLUX.1 Schnell model (fast and free)
        data = {
            "version": "black-forest-labs/flux-schnell",
            "input": {
                "prompt": prompt,
                "num_outputs": 1,
                "aspect_ratio": "1:1",
                "output_format": "png",
                "output_quality": 80,
                "disable_safety_checker": False
            }
        }
        
        print("📡 Creating prediction...")
        
        # Create prediction
        response = requests.post(
            'https://api.replicate.com/v1/predictions',
            headers=headers,
            json=data,
            timeout=10
        )
        
        if response.status_code == 201:
            prediction = response.json()
            prediction_id = prediction['id']
            
            print(f"🔄 Prediction ID: {prediction_id}")
            print("⏳ Waiting for completion...")
            
            # Poll for completion
            for i in range(60):  # Wait up to 2 minutes
                time.sleep(2)
                
                status_response = requests.get(
                    f'https://api.replicate.com/v1/predictions/{prediction_id}',
                    headers=headers
                )
                
                if status_response.status_code == 200:
                    status_data = status_response.json()
                    status = status_data['status']
                    
                    print(f"📊 Status: {status} ({i*2}s)")
                    
                    if status == 'succeeded':
                        if status_data.get('output') and len(status_data['output']) > 0:
                            image_url = status_data['output'][0]
                            
                            print(f"🌐 Image URL: {image_url}")
                            print("📥 Downloading image...")
                            
                            # Download image
                            img_response = requests.get(image_url, timeout=30)
                            if img_response.status_code == 200:
                                # Save image
                                with open(filename, 'wb') as f:
                                    f.write(img_response.content)
                                
                                print(f"✅ Sawirka waa la keydiyay: {filename}")
                                print(f"📁 File size: {len(img_response.content)} bytes")
                                return True
                            else:
                                print(f"❌ Failed to download image: {img_response.status_code}")
                                return False
                        else:
                            print("❌ No output received from Replicate")
                            return False
                        
                    elif status == 'failed':
                        error_msg = status_data.get('error', 'Unknown error')
                        print(f"❌ Replicate generation failed: {error_msg}")
                        return False
                    
                    elif status in ['starting', 'processing']:
                        continue  # Keep waiting
                    
                else:
                    print(f"❌ Status check failed: {status_response.status_code}")
                    return False
            
            print("❌ Generation timed out after 2 minutes")
            return False
            
        else:
            print(f"❌ Replicate API Error: {response.status_code}")
            try:
                error_data = response.json()
                print(f"Error details: {json.dumps(error_data, indent=2)}")
            except:
                print(f"Error text: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Khalad: {e}")
        return False

def test_replicate_images():
    """
    Test multiple images with Replicate
    """
    
    print("🎨 Tincada AI - Replicate Image Generator")
    print("=" * 60)
    
    test_cases = [
        {
            "prompt": "A gray tabby cat hugging an otter with an orange scarf",
            "filename": "replicate_otter.png"
        },
        {
            "prompt": "A beautiful sunset over mountains with a lake, photorealistic",
            "filename": "replicate_sunset.png"
        },
        {
            "prompt": "A futuristic city with flying cars and neon lights",
            "filename": "replicate_city.png"
        }
    ]
    
    successful = 0
    
    for i, test in enumerate(test_cases, 1):
        print(f"\n🧪 Test {i}/{len(test_cases)}")
        print("-" * 40)
        
        success = generate_image_replicate(test["prompt"], test["filename"])
        
        if success:
            successful += 1
            print(f"✅ Test {i} PASSED")
        else:
            print(f"❌ Test {i} FAILED")
        
        # Delay between requests
        if i < len(test_cases):
            print("⏳ Waiting 5 seconds before next test...")
            time.sleep(5)
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 Test Results:")
    print(f"✅ Successful: {successful}/{len(test_cases)}")
    print(f"❌ Failed: {len(test_cases) - successful}/{len(test_cases)}")
    
    if successful > 0:
        print("\n🎉 Replicate image generation is working!")
        print("📁 Check the generated PNG files in this folder.")
        
        print("\n📋 Generated files:")
        for test in test_cases[:successful]:
            print(f"   - {test['filename']}")
    else:
        print("\n❌ All tests failed. Check API token and connection.")
    
    return successful

if __name__ == "__main__":
    test_replicate_images()
