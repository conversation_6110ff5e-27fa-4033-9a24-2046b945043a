<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// API Configuration
$openai_api_key = '********************************************************************************************************************************************************************';

function logMessage($message) {
    error_log("[API_TEST] " . $message);
}

try {
    logMessage("API test request received");
    
    // Get request data
    $input = file_get_contents('php://input');
    $data = json_decode($input, true);
    
    if (!$data) {
        throw new Exception('Invalid JSON data');
    }
    
    $message = $data['message'] ?? 'Hello, test API';
    logMessage("Testing with message: " . $message);
    
    // Test OpenAI API
    $apiData = [
        'model' => 'gpt-4o-mini',
        'messages' => [
            [
                'role' => 'system',
                'content' => 'You are a helpful AI assistant. Respond in the same language as the user\'s question. Keep responses concise and helpful.'
            ],
            [
                'role' => 'user',
                'content' => $message
            ]
        ],
        'max_tokens' => 500,
        'temperature' => 0.7
    ];
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, 'https://api.openai.com/v1/chat/completions');
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($apiData));
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        'Authorization: Bearer ' . $openai_api_key
    ]);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $curlError = curl_error($ch);
    curl_close($ch);
    
    if ($curlError) {
        throw new Exception('CURL Error: ' . $curlError);
    }
    
    if ($httpCode !== 200) {
        logMessage("OpenAI API Error: HTTP " . $httpCode);
        logMessage("Response: " . $response);
        throw new Exception('OpenAI API Error: HTTP ' . $httpCode);
    }
    
    $apiResponse = json_decode($response, true);
    
    if (!$apiResponse || !isset($apiResponse['choices'][0]['message']['content'])) {
        throw new Exception('Invalid API response format');
    }
    
    $aiMessage = $apiResponse['choices'][0]['message']['content'];
    $tokensUsed = $apiResponse['usage']['total_tokens'] ?? 0;
    
    logMessage("API test successful. Tokens used: " . $tokensUsed);
    
    // Return success response
    echo json_encode([
        'success' => true,
        'message' => $aiMessage,
        'model' => $apiResponse['model'] ?? 'gpt-4o-mini',
        'tokens_used' => $tokensUsed,
        'timestamp' => date('Y-m-d H:i:s'),
        'test_status' => 'API connection working perfectly!'
    ]);
    
} catch (Exception $e) {
    logMessage("API test error: " . $e->getMessage());
    
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage(),
        'timestamp' => date('Y-m-d H:i:s'),
        'test_status' => 'API connection failed'
    ]);
}
?>
