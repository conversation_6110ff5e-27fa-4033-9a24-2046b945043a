<!DOCTYPE html>
<html lang="so">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Dashboard Code Cards</title>
    <link rel="stylesheet" href="dashboard.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Fira+Code:wght@300;400;500;600&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/styles/monokai.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/highlight.min.js"></script>
</head>
<body style="background: #212121; color: white; padding: 20px; font-family: 'Poppins', sans-serif;">
    <h1>🔍 Debug Dashboard Code Cards</h1>
    
    <div style="margin: 20px 0;">
        <button onclick="testFormatAIMessage()" style="background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; margin-right: 10px;">
            Test formatAIMessage
        </button>
        
        <button onclick="testAPICall()" style="background: #28a745; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;">
            Test API Call
        </button>
    </div>
    
    <div id="debugOutput" style="background: #2d2d2d; padding: 20px; border-radius: 8px; margin: 20px 0; font-family: monospace; white-space: pre-wrap;"></div>
    
    <div id="testResult" style="margin: 20px 0; border: 2px solid #444; padding: 20px; border-radius: 8px;"></div>

    <script src="dashboard.js"></script>
    <script>
        let dashboard;
        
        function log(message) {
            const output = document.getElementById('debugOutput');
            output.textContent += new Date().toLocaleTimeString() + ': ' + message + '\n';
            console.log(message);
        }
        
        function testFormatAIMessage() {
            log('🧪 Testing formatAIMessage function...');
            
            // Check if TincadaDashboard class exists
            if (typeof TincadaDashboard === 'undefined') {
                log('❌ ERROR: TincadaDashboard class not found!');
                return;
            }
            
            log('✅ TincadaDashboard class found');
            
            // Create dashboard instance
            try {
                dashboard = new TincadaDashboard();
                log('✅ Dashboard instance created');
            } catch (error) {
                log('❌ ERROR creating dashboard: ' + error.message);
                return;
            }
            
            // Check if formatAIMessage method exists
            if (typeof dashboard.formatAIMessage !== 'function') {
                log('❌ ERROR: formatAIMessage method not found!');
                return;
            }
            
            log('✅ formatAIMessage method found');
            
            // Test message with HTML code
            const testMessage = `Here's a simple HTML button:

\`\`\`html
<button type="button">Click Me!</button>
\`\`\`

And some CSS:

\`\`\`css
.button {
    background: #007bff;
    color: white;
}
\`\`\``;

            log('📝 Test message prepared');
            log('🎯 Calling formatAIMessage...');
            
            try {
                const formatted = dashboard.formatAIMessage(testMessage);
                log('✅ formatAIMessage completed');
                log('📏 Formatted length: ' + formatted.length + ' characters');
                
                // Check if code cards were created
                if (formatted.includes('code-card')) {
                    log('🎯 ✅ SUCCESS: Code cards found in formatted output!');
                    
                    const htmlCards = (formatted.match(/data-language="html"/g) || []).length;
                    const cssCards = (formatted.match(/data-language="css"/g) || []).length;
                    
                    log('💻 HTML cards: ' + htmlCards);
                    log('🎨 CSS cards: ' + cssCards);
                } else {
                    log('❌ FAILED: No code cards found in formatted output');
                }
                
                // Display result
                document.getElementById('testResult').innerHTML = formatted;
                log('📺 Result displayed in testResult div');
                
                // Apply syntax highlighting
                setTimeout(() => {
                    document.querySelectorAll('pre code').forEach((block) => {
                        if (window.hljs) {
                            hljs.highlightElement(block);
                            log('✨ Applied syntax highlighting');
                        }
                    });
                }, 100);
                
            } catch (error) {
                log('❌ ERROR in formatAIMessage: ' + error.message);
                log('📄 Error stack: ' + error.stack);
            }
        }
        
        async function testAPICall() {
            log('🚀 Testing API call...');
            
            try {
                const response = await fetch('/api/chat', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        message: 'write html button code',
                        user_id: 'debug_test_user'
                    })
                });
                
                log('📡 API response received: ' + response.status);
                
                const data = await response.json();
                log('📊 Response data parsed');
                
                const aiResponse = data.response || (data.choices && data.choices[0].message.content);
                
                if (aiResponse) {
                    log('🎯 AI response found: ' + aiResponse.length + ' characters');
                    log('📝 AI response preview: ' + aiResponse.substring(0, 100) + '...');
                    
                    // Check for code blocks in response
                    if (aiResponse.includes('```html')) {
                        log('✅ HTML code block found in AI response');
                    } else {
                        log('❌ No HTML code block in AI response');
                    }
                    
                    // Test formatting
                    if (dashboard && dashboard.formatAIMessage) {
                        log('🎨 Formatting AI response...');
                        const formatted = dashboard.formatAIMessage(aiResponse);
                        
                        if (formatted.includes('code-card')) {
                            log('🎯 ✅ SUCCESS: Code cards created from API response!');
                        } else {
                            log('❌ FAILED: No code cards created from API response');
                        }
                        
                        document.getElementById('testResult').innerHTML = formatted;
                        
                        // Apply syntax highlighting
                        setTimeout(() => {
                            document.querySelectorAll('pre code').forEach((block) => {
                                if (window.hljs) {
                                    hljs.highlightElement(block);
                                }
                            });
                        }, 100);
                    }
                } else {
                    log('❌ No AI response found in data');
                }
                
            } catch (error) {
                log('❌ API call failed: ' + error.message);
            }
        }
        
        // Auto-test on page load
        setTimeout(() => {
            log('🔄 Auto-testing formatAIMessage...');
            testFormatAIMessage();
        }, 1000);
    </script>
</body>
</html>
