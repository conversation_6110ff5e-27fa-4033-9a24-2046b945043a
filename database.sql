-- Tincada AI Website Database
-- SQL Database structure for storing website data

CREATE DATABASE IF NOT EXISTS tincada_ai_db;
USE tincada_ai_db;

-- Users table - kaydin mac<PERSON>uma<PERSON>ka users-ka
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id VARCHAR(100) UNIQUE NOT NULL,
    username VARCHAR(50),
    email VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_active TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    total_messages INT DEFAULT 0,
    total_tokens_used INT DEFAULT 0
);

-- Chat conversations table - kaydin sheekooyin
CREATE TABLE conversations (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id VARCHAR(100) NOT NULL,
    conversation_id VARCHAR(100) UNIQUE NOT NULL,
    title VARCHAR(200) DEFAULT 'New Conversation',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    message_count INT DEFAULT 0,
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE
);

-- Chat messages table - kaydin dhammaan messages-ka
CREATE TABLE chat_messages (
    id INT AUTO_INCREMENT PRIMARY KEY,
    conversation_id VARCHAR(100) NOT NULL,
    user_id VARCHAR(100) NOT NULL,
    message_type ENUM('user', 'ai') NOT NULL,
    message_content TEXT NOT NULL,
    tokens_used INT DEFAULT 0,
    response_source ENUM('openai', 'mock_fallback') DEFAULT 'openai',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (conversation_id) REFERENCES conversations(conversation_id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE
);

-- Generated images table - kaydin sawirrada la sameeyay
CREATE TABLE generated_images (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id VARCHAR(100) NOT NULL,
    image_prompt TEXT NOT NULL,
    image_url VARCHAR(500),
    image_size VARCHAR(20) DEFAULT '1024x1024',
    generation_method ENUM('openai', 'replicate', 'mock') DEFAULT 'mock',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE
);

-- API usage statistics - kaydin isticmaalka API
CREATE TABLE api_usage (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id VARCHAR(100) NOT NULL,
    api_type ENUM('chat', 'image') NOT NULL,
    tokens_used INT DEFAULT 0,
    request_count INT DEFAULT 1,
    success BOOLEAN DEFAULT TRUE,
    error_message TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE
);

-- System logs table - kaydin system activities
CREATE TABLE system_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    log_type ENUM('info', 'warning', 'error') NOT NULL,
    message TEXT NOT NULL,
    user_id VARCHAR(100),
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Website analytics table - kaydin website statistics
CREATE TABLE website_analytics (
    id INT AUTO_INCREMENT PRIMARY KEY,
    page_url VARCHAR(200) NOT NULL,
    user_id VARCHAR(100),
    ip_address VARCHAR(45),
    user_agent TEXT,
    session_duration INT DEFAULT 0,
    actions_performed INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Insert sample data
INSERT INTO users (user_id, username, email) VALUES 
('user_sample_001', 'Test User', '<EMAIL>'),
('user_sample_002', 'Ahmed Hassan', '<EMAIL>');

INSERT INTO conversations (user_id, conversation_id, title) VALUES 
('user_sample_001', 'conv_001', 'AI Questions'),
('user_sample_002', 'conv_002', 'Programming Help');

-- Create indexes for better performance
CREATE INDEX idx_user_id ON chat_messages(user_id);
CREATE INDEX idx_conversation_id ON chat_messages(conversation_id);
CREATE INDEX idx_created_at ON chat_messages(created_at);
CREATE INDEX idx_user_created ON users(created_at);
CREATE INDEX idx_api_usage_user ON api_usage(user_id, created_at);

-- Views for easy data retrieval
CREATE VIEW user_stats AS
SELECT 
    u.user_id,
    u.username,
    u.email,
    u.total_messages,
    u.total_tokens_used,
    u.created_at,
    u.last_active,
    COUNT(DISTINCT c.conversation_id) as total_conversations,
    COUNT(DISTINCT gi.id) as total_images_generated
FROM users u
LEFT JOIN conversations c ON u.user_id = c.user_id
LEFT JOIN generated_images gi ON u.user_id = gi.user_id
GROUP BY u.user_id;

CREATE VIEW recent_activity AS
SELECT 
    cm.id,
    cm.user_id,
    u.username,
    cm.conversation_id,
    c.title as conversation_title,
    cm.message_type,
    LEFT(cm.message_content, 100) as message_preview,
    cm.tokens_used,
    cm.response_source,
    cm.created_at
FROM chat_messages cm
JOIN users u ON cm.user_id = u.user_id
JOIN conversations c ON cm.conversation_id = c.conversation_id
ORDER BY cm.created_at DESC
LIMIT 50;
