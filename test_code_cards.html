<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧪 Test Code Cards - Tincada AI</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .header h1 {
            color: #333;
            margin-bottom: 10px;
        }
        
        .test-section {
            margin-bottom: 40px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
            border-left: 4px solid #007bff;
        }
        
        .test-section h2 {
            color: #007bff;
            margin-bottom: 15px;
        }
        
        .test-btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
            transition: all 0.3s ease;
        }
        
        .test-btn:hover {
            background: #0056b3;
            transform: translateY(-2px);
        }
        
        .result-area {
            margin-top: 20px;
            padding: 20px;
            background: white;
            border-radius: 8px;
            border: 1px solid #ddd;
            min-height: 100px;
        }
        
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-weight: bold;
        }
        
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 Code Cards Test</h1>
            <p>Testing Tincada AI code formatting with interactive cards</p>
        </div>
        
        <div class="test-section">
            <h2>📝 Test 1: HTML Code Card</h2>
            <p>Test HTML code formatting with syntax highlighting</p>
            <button class="test-btn" onclick="testHtmlCode()">Test HTML Code</button>
            <div id="htmlResult" class="result-area"></div>
        </div>
        
        <div class="test-section">
            <h2>🎨 Test 2: CSS Code Card</h2>
            <p>Test CSS code formatting with copy/edit functionality</p>
            <button class="test-btn" onclick="testCssCode()">Test CSS Code</button>
            <div id="cssResult" class="result-area"></div>
        </div>
        
        <div class="test-section">
            <h2>⚡ Test 3: JavaScript Code Card</h2>
            <p>Test JavaScript code with interactive features</p>
            <button class="test-btn" onclick="testJsCode()">Test JavaScript Code</button>
            <div id="jsResult" class="result-area"></div>
        </div>
        
        <div class="test-section">
            <h2>🐍 Test 4: Python Code Card</h2>
            <p>Test Python code formatting</p>
            <button class="test-btn" onclick="testPythonCode()">Test Python Code</button>
            <div id="pythonResult" class="result-area"></div>
        </div>
        
        <div class="test-section">
            <h2>🔄 Test 5: Multiple Code Blocks</h2>
            <p>Test multiple code blocks in one response</p>
            <button class="test-btn" onclick="testMultipleCode()">Test Multiple Codes</button>
            <div id="multipleResult" class="result-area"></div>
        </div>
        
        <div class="test-section">
            <h2>🤖 Test 6: Live AI Response</h2>
            <p>Test real AI response with code formatting</p>
            <button class="test-btn" onclick="testAiResponse()">Ask AI for Code</button>
            <div id="aiResult" class="result-area"></div>
        </div>
    </div>

    <script>
        // Mock TincadaAI class for testing
        class MockTincadaAI {
            formatMessage(text) {
                let formattedText = text;
                
                // Process code blocks first (```language ... ```)
                formattedText = formattedText.replace(/```(\w+)?\n?([\s\S]*?)```/g, (match, language, code) => {
                    const lang = language || 'text';
                    const cleanCode = code.trim();
                    const codeId = 'code_' + Math.random().toString(36).substr(2, 9);
                    
                    const cardHtml = `<div class="code-card" data-language="${lang}">
                        <div class="code-header">
                            <div class="code-language">
                                <i class="fas fa-code"></i>
                                <span>${lang.toUpperCase()}</span>
                            </div>
                            <div class="code-actions">
                                <button class="code-btn copy-btn" onclick="mockAI.copyCode('${codeId}')" title="Copy code">
                                    <i class="fas fa-copy"></i>
                                    Copy
                                </button>
                                <button class="code-btn edit-btn" onclick="mockAI.editCode('${codeId}')" title="Edit code">
                                    <i class="fas fa-edit"></i>
                                    Edit
                                </button>
                            </div>
                        </div>
                        <div class="code-content">
                            <pre id="${codeId}"><code class="language-${lang}">${this.escapeHtml(cleanCode)}</code></pre>
                        </div>
                    </div>`;
                    
                    return cardHtml;
                });
                
                // Process inline code (`code`)
                formattedText = formattedText.replace(/`([^`]+)`/g, '<code class="inline-code">$1</code>');
                
                // Process other formatting
                formattedText = formattedText
                    .replace(/\n/g, '<br>')
                    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
                    .replace(/\*(.*?)\*/g, '<em>$1</em>');
                
                return formattedText;
            }
            
            escapeHtml(text) {
                const div = document.createElement('div');
                div.textContent = text;
                return div.innerHTML;
            }
            
            copyCode(codeId) {
                const codeElement = document.getElementById(codeId);
                const code = codeElement.textContent;
                
                navigator.clipboard.writeText(code).then(() => {
                    showStatus('Code copied to clipboard!', 'success');
                    
                    // Visual feedback
                    const copyBtn = codeElement.closest('.code-card').querySelector('.copy-btn');
                    const originalText = copyBtn.innerHTML;
                    copyBtn.innerHTML = '<i class="fas fa-check"></i> Copied!';
                    copyBtn.style.background = '#28a745';
                    
                    setTimeout(() => {
                        copyBtn.innerHTML = originalText;
                        copyBtn.style.background = '';
                    }, 2000);
                }).catch(() => {
                    showStatus('Failed to copy code', 'error');
                });
            }
            
            editCode(codeId) {
                showStatus('Edit functionality would open modal here', 'info');
            }
        }
        
        const mockAI = new MockTincadaAI();
        
        function showStatus(message, type) {
            const status = document.createElement('div');
            status.className = `status ${type}`;
            status.textContent = message;
            document.body.appendChild(status);
            
            setTimeout(() => {
                status.remove();
            }, 3000);
        }
        
        function testHtmlCode() {
            const htmlCode = `Here's a simple HTML example:

\`\`\`html
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Hello World</title>
</head>
<body>
    <h1>Hello, World!</h1>
    <p>This is a paragraph.</p>
    <button onclick="alert('Hello!')">Click me</button>
</body>
</html>
\`\`\`

This creates a basic webpage with a heading, paragraph, and button.`;
            
            document.getElementById('htmlResult').innerHTML = mockAI.formatMessage(htmlCode);
            showStatus('HTML code card generated!', 'success');
        }
        
        function testCssCode() {
            const cssCode = `Here's some CSS styling:

\`\`\`css
.button {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.button:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.2);
}
\`\`\`

This creates a beautiful gradient button with hover effects.`;
            
            document.getElementById('cssResult').innerHTML = mockAI.formatMessage(cssCode);
            showStatus('CSS code card generated!', 'success');
        }
        
        function testJsCode() {
            const jsCode = `Here's a JavaScript function:

\`\`\`javascript
function fibonacci(n) {
    if (n <= 0) return [];
    if (n === 1) return [0];
    if (n === 2) return [0, 1];
    
    const sequence = [0, 1];
    for (let i = 2; i < n; i++) {
        sequence.push(sequence[i-1] + sequence[i-2]);
    }
    
    return sequence;
}

// Example usage
const result = fibonacci(10);
console.log("First 10 Fibonacci numbers:", result);
\`\`\`

This function generates the Fibonacci sequence up to n terms.`;
            
            document.getElementById('jsResult').innerHTML = mockAI.formatMessage(jsCode);
            showStatus('JavaScript code card generated!', 'success');
        }
        
        function testPythonCode() {
            const pythonCode = `Here's a Python class example:

\`\`\`python
class Calculator:
    def __init__(self):
        self.history = []
    
    def add(self, a, b):
        result = a + b
        self.history.append(f"{a} + {b} = {result}")
        return result
    
    def multiply(self, a, b):
        result = a * b
        self.history.append(f"{a} * {b} = {result}")
        return result
    
    def get_history(self):
        return self.history

# Example usage
calc = Calculator()
print(calc.add(5, 3))
print(calc.multiply(4, 7))
print(calc.get_history())
\`\`\`

This creates a calculator class with history tracking.`;
            
            document.getElementById('pythonResult').innerHTML = mockAI.formatMessage(pythonCode);
            showStatus('Python code card generated!', 'success');
        }
        
        function testMultipleCode() {
            const multipleCode = `Here's a complete web component with HTML, CSS, and JavaScript:

**HTML Structure:**
\`\`\`html
<div class="card">
    <h3>Interactive Card</h3>
    <p>Click the button below!</p>
    <button id="actionBtn">Click Me</button>
</div>
\`\`\`

**CSS Styling:**
\`\`\`css
.card {
    background: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    max-width: 300px;
    margin: 20px auto;
}

.card h3 {
    color: #333;
    margin-bottom: 10px;
}

#actionBtn {
    background: #007bff;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 5px;
    cursor: pointer;
}
\`\`\`

**JavaScript Functionality:**
\`\`\`javascript
document.getElementById('actionBtn').addEventListener('click', function() {
    this.textContent = 'Clicked!';
    this.style.background = '#28a745';
    
    setTimeout(() => {
        this.textContent = 'Click Me';
        this.style.background = '#007bff';
    }, 2000);
});
\`\`\`

This creates a complete interactive card component!`;
            
            document.getElementById('multipleResult').innerHTML = mockAI.formatMessage(multipleCode);
            showStatus('Multiple code cards generated!', 'success');
        }
        
        async function testAiResponse() {
            const resultDiv = document.getElementById('aiResult');
            resultDiv.innerHTML = '<div class="status info">🤖 Asking AI for code example...</div>';
            
            try {
                const response = await fetch('http://localhost:5001/api/chat', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        message: 'Create a simple HTML form with CSS styling and JavaScript validation. Show me the complete code with proper formatting.',
                        user_id: 'test_user_' + Date.now()
                    })
                });
                
                const data = await response.json();
                
                if (data.choices && data.choices[0]) {
                    const aiResponse = data.choices[0].message.content;
                    resultDiv.innerHTML = mockAI.formatMessage(aiResponse);
                    showStatus('AI response with code cards generated!', 'success');
                } else {
                    resultDiv.innerHTML = '<div class="status error">❌ Failed to get AI response</div>';
                }
                
            } catch (error) {
                console.error('Error:', error);
                resultDiv.innerHTML = '<div class="status error">❌ Error connecting to AI API</div>';
            }
        }
    </script>
    
    <!-- Include the code card styles -->
    <style>
        /* Code Card Styles */
        .code-card {
            background: #1e1e1e;
            border-radius: 12px;
            margin: 15px 0;
            overflow: hidden;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
            border: 1px solid #333;
            font-family: 'Fira Code', 'Monaco', 'Consolas', monospace;
        }

        .code-header {
            background: linear-gradient(135deg, #2d2d2d 0%, #1a1a1a 100%);
            padding: 12px 16px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid #333;
        }

        .code-language {
            display: flex;
            align-items: center;
            gap: 8px;
            color: #61dafb;
            font-weight: 600;
            font-size: 14px;
            text-transform: uppercase;
        }

        .code-language i {
            font-size: 16px;
        }

        .code-actions {
            display: flex;
            gap: 8px;
        }

        .code-btn {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: #fff;
            padding: 6px 12px;
            border-radius: 6px;
            font-size: 12px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 4px;
            transition: all 0.3s ease;
        }

        .code-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-1px);
        }

        .copy-btn:hover {
            background: #28a745;
        }

        .edit-btn:hover {
            background: #007bff;
        }

        .code-content {
            background: #1e1e1e;
            overflow-x: auto;
        }

        .code-content pre {
            margin: 0;
            padding: 20px;
            background: transparent;
            color: #f8f8f2;
            font-size: 14px;
            line-height: 1.6;
            overflow-x: auto;
        }

        .code-content code {
            font-family: 'Fira Code', 'Monaco', 'Consolas', monospace;
            background: transparent;
            color: inherit;
        }

        /* Inline code */
        .inline-code {
            background: rgba(0, 0, 0, 0.1);
            color: #e6db74;
            padding: 2px 6px;
            border-radius: 4px;
            font-family: 'Fira Code', 'Monaco', 'Consolas', monospace;
            font-size: 0.9em;
            border: 1px solid rgba(0, 0, 0, 0.2);
        }
    </style>
    
    <!-- Font Awesome for icons -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</body>
</html>
