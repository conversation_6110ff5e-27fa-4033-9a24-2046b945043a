#!/usr/bin/env python3
"""
Realistic Mock Image Generator
<PERSON><PERSON><PERSON> mock ah oo u eg kuwa dhabta ah
"""

from PIL import Image, ImageDraw, ImageFont, ImageFilter
import random
import os
from datetime import datetime

def create_realistic_cat_otter_image():
    """Create a realistic-looking mock image of cat and otter"""
    
    # Create high-quality image
    img = Image.new('RGB', (1024, 1024), color=(255, 255, 255))
    draw = ImageDraw.Draw(img)
    
    # Create natural background (grass and sky)
    # Sky gradient
    for y in range(400):
        blue_intensity = int(135 + (200 - 135) * y / 400)
        sky_color = (135, 206, blue_intensity)
        draw.line([(0, y), (1024, y)], fill=sky_color)
    
    # Grass area
    for y in range(400, 1024):
        green_base = int(34 + random.randint(-10, 10))
        green_intensity = int(139 + random.randint(-20, 20))
        grass_color = (green_base, green_intensity, 34)
        draw.line([(0, y), (1024, y)], fill=grass_color)
    
    # Add some clouds
    cloud_color = (255, 255, 255, 180)
    for i in range(5):
        x = random.randint(100, 900)
        y = random.randint(50, 300)
        size = random.randint(80, 150)
        # Create cloud shape with multiple circles
        for j in range(4):
            offset_x = random.randint(-30, 30)
            offset_y = random.randint(-20, 20)
            draw.ellipse([x + offset_x - size//2, y + offset_y - size//3, 
                         x + offset_x + size//2, y + offset_y + size//3], 
                        fill=(255, 255, 255))
    
    # Draw cat body (gray tabby)
    cat_x, cat_y = 350, 600
    cat_body_color = (128, 128, 128)  # Gray
    cat_stripe_color = (64, 64, 64)   # Darker gray for stripes
    
    # Cat body (oval)
    draw.ellipse([cat_x - 80, cat_y - 60, cat_x + 80, cat_y + 60], fill=cat_body_color)
    
    # Cat head
    draw.ellipse([cat_x - 50, cat_y - 120, cat_x + 50, cat_y - 40], fill=cat_body_color)
    
    # Cat ears
    draw.polygon([(cat_x - 40, cat_y - 110), (cat_x - 20, cat_y - 140), (cat_x - 10, cat_y - 110)], fill=cat_body_color)
    draw.polygon([(cat_x + 10, cat_y - 110), (cat_x + 20, cat_y - 140), (cat_x + 40, cat_y - 110)], fill=cat_body_color)
    
    # Cat stripes (tabby pattern)
    for i in range(5):
        stripe_y = cat_y - 40 + i * 20
        draw.ellipse([cat_x - 70, stripe_y - 5, cat_x + 70, stripe_y + 5], fill=cat_stripe_color)
    
    # Cat legs
    draw.ellipse([cat_x - 60, cat_y + 40, cat_x - 30, cat_y + 100], fill=cat_body_color)
    draw.ellipse([cat_x + 30, cat_y + 40, cat_x + 60, cat_y + 100], fill=cat_body_color)
    
    # Cat tail
    draw.ellipse([cat_x + 70, cat_y - 20, cat_x + 120, cat_y + 20], fill=cat_body_color)
    
    # Cat face features
    # Eyes
    draw.ellipse([cat_x - 25, cat_y - 90, cat_x - 15, cat_y - 80], fill=(0, 255, 0))  # Green eyes
    draw.ellipse([cat_x + 15, cat_y - 90, cat_x + 25, cat_y - 80], fill=(0, 255, 0))
    # Pupils
    draw.ellipse([cat_x - 22, cat_y - 87, cat_x - 18, cat_y - 83], fill=(0, 0, 0))
    draw.ellipse([cat_x + 18, cat_y - 87, cat_x + 22, cat_y - 83], fill=(0, 0, 0))
    
    # Nose
    draw.polygon([(cat_x - 3, cat_y - 75), (cat_x + 3, cat_y - 75), (cat_x, cat_y - 70)], fill=(255, 192, 203))
    
    # Draw otter
    otter_x, otter_y = 650, 650
    otter_color = (101, 67, 33)  # Brown
    
    # Otter body
    draw.ellipse([otter_x - 70, otter_y - 50, otter_x + 70, otter_y + 50], fill=otter_color)
    
    # Otter head
    draw.ellipse([otter_x - 45, otter_y - 100, otter_x + 45, otter_y - 30], fill=otter_color)
    
    # Otter ears (small)
    draw.ellipse([otter_x - 35, otter_y - 95, otter_x - 25, otter_y - 85], fill=otter_color)
    draw.ellipse([otter_x + 25, otter_y - 95, otter_x + 35, otter_y - 85], fill=otter_color)
    
    # Otter legs/flippers
    draw.ellipse([otter_x - 60, otter_y + 30, otter_x - 30, otter_y + 80], fill=otter_color)
    draw.ellipse([otter_x + 30, otter_y + 30, otter_x + 60, otter_y + 80], fill=otter_color)
    
    # Otter tail
    draw.ellipse([otter_x + 60, otter_y - 10, otter_x + 110, otter_y + 30], fill=otter_color)
    
    # Otter face
    # Eyes
    draw.ellipse([otter_x - 20, otter_y - 75, otter_x - 10, otter_y - 65], fill=(0, 0, 0))
    draw.ellipse([otter_x + 10, otter_y - 75, otter_x + 20, otter_y - 65], fill=(0, 0, 0))
    
    # Nose
    draw.ellipse([otter_x - 5, otter_y - 60, otter_x + 5, otter_y - 50], fill=(0, 0, 0))
    
    # Orange scarf on otter
    scarf_color = (255, 165, 0)  # Orange
    # Scarf around neck
    draw.ellipse([otter_x - 50, otter_y - 45, otter_x + 50, otter_y - 25], fill=scarf_color)
    # Scarf ends
    draw.ellipse([otter_x + 40, otter_y - 35, otter_x + 80, otter_y - 15], fill=scarf_color)
    draw.ellipse([otter_x + 70, otter_y - 25, otter_x + 90, otter_y + 15], fill=scarf_color)
    
    # Show hugging - cat's paw on otter
    # Cat's arm/paw reaching toward otter
    draw.ellipse([cat_x + 60, cat_y - 20, cat_x + 120, cat_y + 20], fill=cat_body_color)
    draw.ellipse([cat_x + 110, cat_y - 10, cat_x + 140, cat_y + 30], fill=cat_body_color)
    
    # Add some decorative elements
    # Flowers in the grass
    flower_colors = [(255, 192, 203), (255, 255, 0), (255, 0, 255), (0, 255, 255)]
    for i in range(8):
        fx = random.randint(50, 950)
        fy = random.randint(450, 950)
        flower_color = random.choice(flower_colors)
        # Simple flower shape
        for petal in range(5):
            angle = petal * 72  # 360/5 = 72 degrees
            px = fx + 15 * (1 if angle < 180 else -1)
            py = fy + 15 * (1 if 90 < angle < 270 else -1)
            draw.ellipse([px - 8, py - 8, px + 8, py + 8], fill=flower_color)
        # Flower center
        draw.ellipse([fx - 5, fy - 5, fx + 5, fy + 5], fill=(255, 255, 0))
    
    # Add text overlay
    try:
        font_large = ImageFont.truetype("arial.ttf", 36)
        font_small = ImageFont.truetype("arial.ttf", 24)
    except:
        font_large = ImageFont.load_default()
        font_small = ImageFont.load_default()
    
    # Title
    title = "🐱 Gray Tabby Cat Hugging Otter 🦦"
    title_bbox = draw.textbbox((0, 0), title, font=font_large)
    title_width = title_bbox[2] - title_bbox[0]
    draw.text(((1024 - title_width) // 2, 50), title, fill=(255, 255, 255), font=font_large)
    
    # Add shadow to title
    draw.text(((1024 - title_width) // 2 + 2, 52), title, fill=(0, 0, 0), font=font_large)
    draw.text(((1024 - title_width) // 2, 50), title, fill=(255, 255, 255), font=font_large)
    
    # Subtitle
    subtitle = "🧡 With Orange Scarf - Realistic Mock Image 🧡"
    subtitle_bbox = draw.textbbox((0, 0), subtitle, font=font_small)
    subtitle_width = subtitle_bbox[2] - subtitle_bbox[0]
    draw.text(((1024 - subtitle_width) // 2, 100), subtitle, fill=(255, 255, 255), font=font_small)
    
    # Watermark
    watermark = "High-Quality Mock • Real API needed for actual AI images"
    watermark_bbox = draw.textbbox((0, 0), watermark, font=font_small)
    watermark_width = watermark_bbox[2] - watermark_bbox[0]
    draw.text(((1024 - watermark_width) // 2, 950), watermark, fill=(128, 128, 128), font=font_small)
    
    return img

def save_realistic_image():
    """Save the realistic mock image"""
    
    print("🎨 Creating realistic mock image...")
    print("📏 Size: 1024x1024 pixels")
    print("🎯 Subject: Gray tabby cat hugging otter with orange scarf")
    
    # Generate image
    img = create_realistic_cat_otter_image()
    
    # Apply slight blur for more realistic look
    img = img.filter(ImageFilter.GaussianBlur(radius=0.5))
    
    # Save main image
    filename = "realistic_cat_and_otter.png"
    img.save(filename, "PNG", quality=95)
    
    # Save with timestamp
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_filename = f"realistic_mock_{timestamp}.png"
    img.save(backup_filename, "PNG", quality=95)
    
    print(f"✅ Realistic mock image saved: {filename}")
    print(f"📁 File size: {os.path.getsize(filename):,} bytes")
    print(f"📋 Backup saved: {backup_filename}")
    
    return filename

def main():
    """Main function"""
    
    print("🎨 Realistic Mock Image Generator")
    print("Creating high-quality mock version of your requested image")
    print("=" * 70)
    
    filename = save_realistic_image()
    
    print("\n" + "=" * 70)
    print("🎉 SUCCESS!")
    print(f"📁 Your realistic mock image: {filename}")
    print("\n📝 Image features:")
    print("   🐱 Gray tabby cat with stripes")
    print("   🦦 Brown otter")
    print("   🧡 Orange scarf on otter")
    print("   🤗 Hugging pose")
    print("   🌿 Natural grass and sky background")
    print("   🌸 Decorative flowers")
    print("   📏 High resolution (1024x1024)")
    print("\n💡 This is a high-quality mock image.")
    print("   Real AI-generated images require working OpenAI API key.")

if __name__ == "__main__":
    main()
