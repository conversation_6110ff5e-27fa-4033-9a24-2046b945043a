<!DOCTYPE html>
<html lang="so">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment System Demo - Tincada AI</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
            padding: 2rem;
            margin: 0;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 3rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        h1 {
            text-align: center;
            font-size: 2.5rem;
            margin-bottom: 1rem;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .subtitle {
            text-align: center;
            font-size: 1.2rem;
            margin-bottom: 3rem;
            opacity: 0.9;
        }
        
        .feature-section {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .feature-section h2 {
            color: #4ecdc4;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .feature-list {
            list-style: none;
            padding: 0;
        }
        
        .feature-list li {
            padding: 0.8rem 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .feature-list li:last-child {
            border-bottom: none;
        }
        
        .feature-icon {
            width: 40px;
            height: 40px;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
        }
        
        .demo-flow {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 10px;
            padding: 1.5rem;
            margin: 1rem 0;
            border-left: 4px solid #4ecdc4;
        }
        
        .flow-step {
            display: flex;
            align-items: center;
            gap: 15px;
            margin: 1rem 0;
            padding: 1rem;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
        }
        
        .step-number {
            width: 30px;
            height: 30px;
            background: linear-gradient(45deg, #10a37f, #0d8f6f);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 0.9rem;
        }
        
        .success {
            background: rgba(76, 175, 80, 0.2);
            border: 1px solid rgba(76, 175, 80, 0.3);
            border-radius: 10px;
            padding: 1rem;
            margin: 1rem 0;
            border-left: 4px solid #4caf50;
        }
        
        .warning {
            background: rgba(255, 167, 38, 0.2);
            border: 1px solid rgba(255, 167, 38, 0.3);
            border-radius: 10px;
            padding: 1rem;
            margin: 1rem 0;
            border-left: 4px solid #ffa726;
        }
        
        .demo-buttons {
            display: flex;
            gap: 20px;
            justify-content: center;
            margin-top: 3rem;
            flex-wrap: wrap;
        }
        
        .demo-btn {
            background: linear-gradient(45deg, #4ecdc4, #44a08d);
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 25px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 10px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(78, 205, 196, 0.3);
        }
        
        .demo-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(78, 205, 196, 0.4);
        }
        
        .demo-btn.secondary {
            background: linear-gradient(45deg, #ff6b6b, #ee5a52);
            box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
        }
        
        .demo-btn.secondary:hover {
            box-shadow: 0 6px 20px rgba(255, 107, 107, 0.4);
        }
        
        .form-preview {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-top: 2rem;
        }
        
        .form-section-preview {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 1.5rem;
        }
        
        .form-section-preview h4 {
            color: #4ecdc4;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .form-field {
            margin-bottom: 1rem;
            padding: 0.8rem;
            background: rgba(255, 255, 255, 0.03);
            border-radius: 6px;
            border-left: 3px solid #10a37f;
            font-size: 0.9rem;
            color: #cccccc;
        }
        
        @media (max-width: 768px) {
            body {
                padding: 1rem;
            }
            
            .container {
                padding: 2rem 1rem;
            }
            
            h1 {
                font-size: 2rem;
            }
            
            .demo-buttons {
                flex-direction: column;
                align-items: center;
            }
            
            .flow-step {
                flex-direction: column;
                text-align: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>💳 Payment System - Tincada AI</h1>
        <p class="subtitle">Professional payment system oo leh plan selection, payment form, iyo secure processing</p>
        
        <div class="feature-section">
            <h2>✅ Payment Features la dhisay</h2>
            <ul class="feature-list">
                <li>
                    <div class="feature-icon">💳</div>
                    <div>
                        <strong>Get Plus Button Integration</strong><br>
                        <small>Header-ka "Get Plus" button wuxuu furayaa pricing plans modal</small>
                    </div>
                </li>
                <li>
                    <div class="feature-icon">📋</div>
                    <div>
                        <strong>Professional Payment Form</strong><br>
                        <small>Card details, billing address, iyo security badges</small>
                    </div>
                </li>
                <li>
                    <div class="feature-icon">🔄</div>
                    <div>
                        <strong>Multi-Step Flow</strong><br>
                        <small>Plans → Payment Form → Processing → Success</small>
                    </div>
                </li>
                <li>
                    <div class="feature-icon">📱</div>
                    <div>
                        <strong>Mobile Responsive</strong><br>
                        <small>Perfect design mobile iyo desktop labadaba</small>
                    </div>
                </li>
                <li>
                    <div class="feature-icon">🔒</div>
                    <div>
                        <strong>Security Features</strong><br>
                        <small>SSL badges, secure payment icons, validation</small>
                    </div>
                </li>
            </ul>
        </div>
        
        <div class="feature-section">
            <h2>🎯 Payment Flow</h2>
            
            <div class="demo-flow">
                <div class="flow-step">
                    <div class="step-number">1</div>
                    <div>
                        <strong>Click "Get Plus"</strong><br>
                        <small>Header-ka button riix ama message limit gaaro</small>
                    </div>
                </div>
                
                <div class="flow-step">
                    <div class="step-number">2</div>
                    <div>
                        <strong>Select Plan</strong><br>
                        <small>Premium ($9.99), Basic ($4.99), ama Daily Pass ($1.99)</small>
                    </div>
                </div>
                
                <div class="flow-step">
                    <div class="step-number">3</div>
                    <div>
                        <strong>Payment Form</strong><br>
                        <small>Card details, billing address, iyo security validation</small>
                    </div>
                </div>
                
                <div class="flow-step">
                    <div class="step-number">4</div>
                    <div>
                        <strong>Processing</strong><br>
                        <small>Secure payment processing oo leh loading state</small>
                    </div>
                </div>
                
                <div class="flow-step">
                    <div class="step-number">5</div>
                    <div>
                        <strong>Success</strong><br>
                        <small>Plan activation iyo message limit update</small>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="feature-section">
            <h2>📝 Payment Form Sections</h2>
            
            <div class="form-preview">
                <div class="form-section-preview">
                    <h4><i class="fas fa-credit-card"></i> Payment Information</h4>
                    <div class="form-field">Card Number (formatted automatically)</div>
                    <div class="form-field">Expiry Date (MM/YY format)</div>
                    <div class="form-field">CVV (3-4 digits)</div>
                    <div class="form-field">Name on Card</div>
                </div>
                
                <div class="form-section-preview">
                    <h4><i class="fas fa-map-marker-alt"></i> Billing Address</h4>
                    <div class="form-field">Email Address</div>
                    <div class="form-field">Country (dropdown)</div>
                    <div class="form-field">City</div>
                    <div class="form-field">ZIP Code</div>
                </div>
                
                <div class="form-section-preview">
                    <h4><i class="fas fa-shield-alt"></i> Security</h4>
                    <div class="form-field">SSL Encrypted</div>
                    <div class="form-field">Secure Payment</div>
                    <div class="form-field">30-Day Refund</div>
                </div>
            </div>
        </div>
        
        <div class="feature-section">
            <h2>🧪 Test Instructions</h2>
            
            <div class="success">
                <strong>✅ Ready to test!</strong> Payment system waa diyaar oo dhammaan features-yada waa shaqaynayaan.
            </div>
            
            <h3>Test Steps:</h3>
            <div class="demo-flow">
                <div class="flow-step">
                    <div class="step-number">1</div>
                    <div>Login dashboard oo riix "Get Plus" button header-ka</div>
                </div>
                
                <div class="flow-step">
                    <div class="step-number">2</div>
                    <div>Doorso plan (Premium recommended)</div>
                </div>
                
                <div class="flow-step">
                    <div class="step-number">3</div>
                    <div>Buuxi payment form (test data kaliya)</div>
                </div>
                
                <div class="flow-step">
                    <div class="step-number">4</div>
                    <div>Riix "Complete Payment" oo eeg processing</div>
                </div>
                
                <div class="flow-step">
                    <div class="step-number">5</div>
                    <div>Eeg success message iyo plan activation</div>
                </div>
            </div>
            
            <div class="warning">
                <strong>⚠️ Demo Mode:</strong> Payment waa simulation kaliya. Real payment integration (Stripe/PayPal) waa loo baahan yahay production.
            </div>
        </div>
        
        <div class="demo-buttons">
            <a href="dashboard.html" class="demo-btn">
                🚀 Test Payment System
            </a>
            <a href="limits-demo.html" class="demo-btn secondary">
                📊 Message Limits Demo
            </a>
            <a href="api-fix-demo.html" class="demo-btn secondary">
                🔧 API Fix Demo
            </a>
        </div>
    </div>
</body>
</html>
