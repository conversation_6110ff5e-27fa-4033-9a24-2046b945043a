#!/usr/bin/env python3
"""
Direct OpenAI API Test
Test OpenAI API key directly
"""

import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

try:
    from openai import OpenAI
    
    # Get API key
    api_key = os.getenv('OPENAI_API_KEY')
    print(f"🔑 API Key found: {api_key[:20]}..." if api_key else "❌ No API key found")
    
    if not api_key:
        print("❌ OPENAI_API_KEY not set in .env file")
        exit(1)
    
    # Initialize client
    print("🔄 Initializing OpenAI client...")
    client = OpenAI(api_key=api_key)
    
    # Test API call
    print("🧪 Testing API call...")
    response = client.chat.completions.create(
        model="gpt-4o-mini",
        messages=[
            {
                "role": "system",
                "content": "You are Tincada AI. Respond in Somali."
            },
            {
                "role": "user",
                "content": "Maxaad tahay?"
            }
        ],
        max_tokens=100,
        temperature=0.7
    )

    print("✅ API call successful!")
    print(f"📝 Response: {response.choices[0].message.content}")
    print(f"🔢 Tokens used: {response.usage.total_tokens}")
    print(f"💰 Model: {response.model}")

    # Test image generation
    print("\n🎨 Testing DALL-E image generation...")
    try:
        image_response = client.images.generate(
            model="dall-e-3",
            prompt="A beautiful sunset over mountains, photorealistic",
            size="1024x1024",
            quality="standard",
            n=1
        )

        print("✅ Image generation successful!")
        print(f"🖼️ Image URL: {image_response.data[0].url}")
        print(f"📝 Revised prompt: {image_response.data[0].revised_prompt}")

    except Exception as img_error:
        print(f"❌ Image generation error: {img_error}")
        if "billing" in str(img_error).lower():
            print("   No credits for image generation")
        elif "content_policy" in str(img_error).lower():
            print("   Content policy violation")
    
    print("✅ API call successful!")
    print(f"📝 Response: {response.choices[0].message.content}")
    print(f"🔢 Tokens used: {response.usage.total_tokens}")
    print(f"💰 Model: {response.model}")
    
except ImportError:
    print("❌ OpenAI library not installed!")
    print("   Install with: pip install openai python-dotenv")
except Exception as e:
    print(f"❌ Error: {e}")
    if "401" in str(e):
        print("   API key is invalid or expired")
    elif "429" in str(e):
        print("   Rate limit exceeded")
    elif "insufficient_quota" in str(e):
        print("   No credits in OpenAI account")
