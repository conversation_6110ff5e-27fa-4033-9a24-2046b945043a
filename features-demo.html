<!DOCTYPE html>
<html lang="so">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>New Features Demo - Tincada AI</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
            padding: 2rem;
            margin: 0;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 3rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        h1 {
            text-align: center;
            font-size: 2.5rem;
            margin-bottom: 1rem;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .subtitle {
            text-align: center;
            font-size: 1.2rem;
            margin-bottom: 3rem;
            opacity: 0.9;
        }
        
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 2rem;
            margin-bottom: 3rem;
        }
        
        .feature-card {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 2rem;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .feature-card:hover {
            transform: translateY(-5px);
            border-color: rgba(255, 255, 255, 0.2);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);
        }
        
        .feature-icon {
            width: 70px;
            height: 70px;
            background: linear-gradient(45deg, #10a37f, #0d8f6f);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.8rem;
            margin-bottom: 1.5rem;
            color: white;
        }
        
        .feature-card h3 {
            color: #ffffff;
            font-size: 1.3rem;
            margin-bottom: 1rem;
        }
        
        .feature-card p {
            color: #cccccc;
            line-height: 1.6;
            margin-bottom: 1.5rem;
        }
        
        .feature-list {
            list-style: none;
            padding: 0;
            margin-bottom: 1.5rem;
        }
        
        .feature-list li {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 8px;
            color: #cccccc;
            font-size: 0.9rem;
        }
        
        .feature-list li i {
            color: #10a37f;
            font-size: 0.8rem;
        }
        
        .status-badge {
            position: absolute;
            top: 15px;
            right: 15px;
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 0.7rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .status-complete {
            background: linear-gradient(45deg, #10a37f, #0d8f6f);
            color: white;
        }
        
        .status-coming {
            background: linear-gradient(45deg, #ff6b6b, #ee5a52);
            color: white;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }
        
        .demo-section {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            border-left: 4px solid #4ecdc4;
        }
        
        .demo-section h2 {
            color: #4ecdc4;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .chat-demo {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            padding: 1rem;
            margin: 1rem 0;
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .chat-item-demo {
            flex: 1;
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 10px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            position: relative;
        }
        
        .chat-actions-demo {
            opacity: 0.7;
            transition: opacity 0.2s ease;
        }
        
        .chat-item-demo:hover .chat-actions-demo {
            opacity: 1;
        }
        
        .nav-demo {
            display: flex;
            gap: 15px;
            margin: 1rem 0;
            flex-wrap: wrap;
        }
        
        .nav-item-demo {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 12px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 6px;
            position: relative;
            color: #cccccc;
            font-size: 0.9rem;
        }
        
        .coming-soon-badge-demo {
            position: absolute;
            top: -5px;
            right: -5px;
            background: linear-gradient(45deg, #ff6b6b, #ee5a52);
            color: white;
            font-size: 0.6rem;
            font-weight: 600;
            padding: 2px 6px;
            border-radius: 10px;
            text-transform: uppercase;
            animation: pulse 2s infinite;
        }
        
        .success {
            background: rgba(76, 175, 80, 0.2);
            border: 1px solid rgba(76, 175, 80, 0.3);
            border-radius: 10px;
            padding: 1.5rem;
            margin: 2rem 0;
            border-left: 4px solid #4caf50;
            text-align: center;
        }
        
        .demo-buttons {
            display: flex;
            gap: 20px;
            justify-content: center;
            margin-top: 3rem;
            flex-wrap: wrap;
        }
        
        .demo-btn {
            background: linear-gradient(45deg, #4ecdc4, #44a08d);
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 25px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 10px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(78, 205, 196, 0.3);
        }
        
        .demo-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(78, 205, 196, 0.4);
        }
        
        .demo-btn.secondary {
            background: linear-gradient(45deg, #ff6b6b, #ee5a52);
            box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
        }
        
        .demo-btn.secondary:hover {
            box-shadow: 0 6px 20px rgba(255, 107, 107, 0.4);
        }
        
        @media (max-width: 768px) {
            body { padding: 1rem; }
            .container { padding: 2rem 1rem; }
            h1 { font-size: 2rem; }
            .features-grid { grid-template-columns: 1fr; }
            .demo-buttons { flex-direction: column; align-items: center; }
            .nav-demo { justify-content: center; }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 New Features - Tincada AI</h1>
        <p class="subtitle">Chat management, coming soon features, iyo enhanced user experience</p>
        
        <div class="features-grid">
            <div class="feature-card">
                <div class="status-badge status-complete">✅ Complete</div>
                <div class="feature-icon">💬</div>
                <h3>Chat History Management</h3>
                <p>Powerful chat management oo leh export, rename, delete, iyo duplicate functionality.</p>
                <ul class="feature-list">
                    <li><i class="fas fa-check"></i> Export chat to JSON</li>
                    <li><i class="fas fa-check"></i> Rename chat titles</li>
                    <li><i class="fas fa-check"></i> Duplicate conversations</li>
                    <li><i class="fas fa-check"></i> Delete unwanted chats</li>
                    <li><i class="fas fa-check"></i> Context menu interface</li>
                </ul>
            </div>
            
            <div class="feature-card">
                <div class="status-badge status-coming">🔜 Coming Soon</div>
                <div class="feature-icon">🤖</div>
                <h3>GPTs Feature</h3>
                <p>Custom AI assistants oo aad sameyso oo u gaar ah shaqadaada iyo baahiyahaaga.</p>
                <ul class="feature-list">
                    <li><i class="fas fa-star"></i> Custom AI personalities</li>
                    <li><i class="fas fa-star"></i> Specialized knowledge bases</li>
                    <li><i class="fas fa-star"></i> Industry-specific assistants</li>
                    <li><i class="fas fa-star"></i> Template library</li>
                    <li><i class="fas fa-star"></i> Share with community</li>
                </ul>
            </div>
            
            <div class="feature-card">
                <div class="status-badge status-coming">🔜 Coming Soon</div>
                <div class="feature-icon">🎬</div>
                <h3>Sora Feature</h3>
                <p>AI-powered video generation oo ka sameeya text descriptions professional videos.</p>
                <ul class="feature-list">
                    <li><i class="fas fa-star"></i> Text-to-video generation</li>
                    <li><i class="fas fa-star"></i> Multiple video styles</li>
                    <li><i class="fas fa-star"></i> HD quality output</li>
                    <li><i class="fas fa-star"></i> Custom durations</li>
                    <li><i class="fas fa-star"></i> Export in multiple formats</li>
                </ul>
            </div>
            
            <div class="feature-card">
                <div class="status-badge status-coming">🔜 Coming Soon</div>
                <div class="feature-icon">📚</div>
                <h3>Maktabada Feature</h3>
                <p>Knowledge base system oo aad ku kaydin karto oo organize gareyn karto macluumaadkaaga.</p>
                <ul class="feature-list">
                    <li><i class="fas fa-star"></i> Document storage</li>
                    <li><i class="fas fa-star"></i> Smart categorization</li>
                    <li><i class="fas fa-star"></i> Advanced search</li>
                    <li><i class="fas fa-star"></i> AI-powered insights</li>
                    <li><i class="fas fa-star"></i> Team collaboration</li>
                </ul>
            </div>
        </div>
        
        <div class="demo-section">
            <h2>💬 Chat Management Demo</h2>
            <p>Hover over chat items oo eeg context menu options:</p>
            
            <div class="chat-demo">
                <div class="chat-item-demo">
                    <i class="fas fa-comment" style="color: #10a37f;"></i>
                    <span>Sample Chat Title</span>
                    <div class="chat-actions-demo" style="margin-left: auto;">
                        <button style="background: none; border: none; color: rgba(255,255,255,0.6); padding: 8px; cursor: pointer;">
                            <i class="fas fa-ellipsis-v"></i>
                        </button>
                    </div>
                </div>
            </div>
            
            <p style="color: #cccccc; font-size: 0.9rem; margin-top: 1rem;">
                ✅ Right-click or click menu button to access:<br>
                📝 Rename • 📥 Export • 📋 Duplicate • 🗑️ Delete
            </p>
        </div>
        
        <div class="demo-section">
            <h2>🚀 Coming Soon Navigation</h2>
            <p>Navigation menu oo leh coming soon badges:</p>
            
            <div class="nav-demo">
                <div class="nav-item-demo">
                    <i class="fas fa-comments"></i>
                    <span>Sheekooyin</span>
                </div>
                <div class="nav-item-demo">
                    <i class="fas fa-robot"></i>
                    <span>GPTs</span>
                    <div class="coming-soon-badge-demo">Soon</div>
                </div>
                <div class="nav-item-demo">
                    <i class="fas fa-video"></i>
                    <span>Sora</span>
                    <div class="coming-soon-badge-demo">Soon</div>
                </div>
                <div class="nav-item-demo">
                    <i class="fas fa-book"></i>
                    <span>Maktabada</span>
                    <div class="coming-soon-badge-demo">Soon</div>
                </div>
            </div>
            
            <p style="color: #cccccc; font-size: 0.9rem; margin-top: 1rem;">
                ✅ Click on coming soon features oo eeg beautiful modal popups!
            </p>
        </div>
        
        <div class="success">
            <h3>🎉 All Features Ready!</h3>
            <p><strong>Chat Management:</strong> Fully functional with export, rename, duplicate, delete</p>
            <p><strong>Coming Soon Features:</strong> Beautiful modals with feature previews</p>
            <p><strong>Enhanced UX:</strong> Professional animations and interactions</p>
        </div>
        
        <div class="demo-buttons">
            <a href="dashboard.html" class="demo-btn">
                🚀 Test All Features
            </a>
            <a href="ai-response-demo.html" class="demo-btn secondary">
                🤖 AI Responses
            </a>
            <a href="payment-demo.html" class="demo-btn secondary">
                💳 Payment System
            </a>
        </div>
    </div>
</body>
</html>
