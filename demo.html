<!DOCTYPE html>
<html lang="so">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tincada AI - Demo & Instructions</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
            padding: 2rem;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 3rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        h1 {
            text-align: center;
            font-size: 3rem;
            margin-bottom: 1rem;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .subtitle {
            text-align: center;
            font-size: 1.2rem;
            margin-bottom: 3rem;
            opacity: 0.9;
        }
        
        .demo-section {
            margin-bottom: 3rem;
        }
        
        .demo-section h2 {
            font-size: 1.8rem;
            margin-bottom: 1rem;
            color: #4ecdc4;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .demo-content {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            padding: 2rem;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .feature-list {
            list-style: none;
            padding: 0;
        }
        
        .feature-list li {
            padding: 0.8rem 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .feature-list li:last-child {
            border-bottom: none;
        }
        
        .feature-icon {
            width: 40px;
            height: 40px;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
        }
        
        .step-number {
            background: linear-gradient(45deg, #4ecdc4, #44a08d);
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 15px;
        }
        
        .demo-buttons {
            display: flex;
            gap: 20px;
            justify-content: center;
            margin-top: 3rem;
            flex-wrap: wrap;
        }
        
        .demo-btn {
            background: linear-gradient(45deg, #4ecdc4, #44a08d);
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 25px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 10px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(78, 205, 196, 0.3);
        }
        
        .demo-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(78, 205, 196, 0.4);
        }
        
        .demo-btn.secondary {
            background: linear-gradient(45deg, #ff6b6b, #ee5a52);
            box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
        }
        
        .demo-btn.secondary:hover {
            box-shadow: 0 6px 20px rgba(255, 107, 107, 0.4);
        }
        
        .code-block {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 10px;
            padding: 1.5rem;
            margin: 1rem 0;
            font-family: 'Courier New', monospace;
            border-left: 4px solid #4ecdc4;
            overflow-x: auto;
        }
        
        .warning {
            background: rgba(255, 167, 38, 0.2);
            border: 1px solid rgba(255, 167, 38, 0.3);
            border-radius: 10px;
            padding: 1rem;
            margin: 1rem 0;
            border-left: 4px solid #ffa726;
        }
        
        .success {
            background: rgba(76, 175, 80, 0.2);
            border: 1px solid rgba(76, 175, 80, 0.3);
            border-radius: 10px;
            padding: 1rem;
            margin: 1rem 0;
            border-left: 4px solid #4caf50;
        }
        
        @media (max-width: 768px) {
            body {
                padding: 1rem;
            }
            
            .container {
                padding: 2rem 1rem;
            }
            
            h1 {
                font-size: 2rem;
            }
            
            .demo-buttons {
                flex-direction: column;
                align-items: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🤖 Tincada AI</h1>
        <p class="subtitle">Chatbot Casri ah oo Af Soomaali ku hadla</p>
        
        <div class="demo-section">
            <h2>🌟 Astaamaha Muhiimka ah</h2>
            <div class="demo-content">
                <ul class="feature-list">
                    <li>
                        <div class="feature-icon">💬</div>
                        <div>
                            <strong>Chat AI oo Af Soomaali ah</strong><br>
                            <small>La sheekayso AI af Soomaali nadiif ah oo fahma su'aalahaaga</small>
                        </div>
                    </li>
                    <li>
                        <div class="feature-icon">🎨</div>
                        <div>
                            <strong>Interface Qurux Badan</strong><br>
                            <small>Design casri ah oo leh gradients, animations, iyo hover effects</small>
                        </div>
                    </li>
                    <li>
                        <div class="feature-icon">📁</div>
                        <div>
                            <strong>File Upload</strong><br>
                            <small>Ku dar sawir, video, PDF, iyo files kale</small>
                        </div>
                    </li>
                    <li>
                        <div class="feature-icon">🎤</div>
                        <div>
                            <strong>Voice Message</strong><br>
                            <small>Dir fariin cod ah (voice recording)</small>
                        </div>
                    </li>
                    <li>
                        <div class="feature-icon">📚</div>
                        <div>
                            <strong>Taariikhda Sheekada</strong><br>
                            <small>Dib u eeg sheekooyinkii hore oo la kaydsaday</small>
                        </div>
                    </li>
                    <li>
                        <div class="feature-icon">📱</div>
                        <div>
                            <strong>Responsive Design</strong><br>
                            <small>Waxay ku shaqaysaa desktop, tablet, iyo mobile</small>
                        </div>
                    </li>
                    <li>
                        <div class="feature-icon">🔐</div>
                        <div>
                            <strong>Login System</strong><br>
                            <small>Akoon gaar ah oo leh magac, number, sawir, iyo password</small>
                        </div>
                    </li>
                    <li>
                        <div class="feature-icon">👤</div>
                        <div>
                            <strong>User Profile</strong><br>
                            <small>Sawirkaaga ayaa noqon doona icon-ka chat-ka</small>
                        </div>
                    </li>
                    <li>
                        <div class="feature-icon">🎛️</div>
                        <div>
                            <strong>Dashboard Layout</strong><br>
                            <small>Interface u eg ChatGPT oo leh sidebar iyo main content</small>
                        </div>
                    </li>
                    <li>
                        <div class="feature-icon">⚙️</div>
                        <div>
                            <strong>Settings Modal</strong><br>
                            <small>Beddel profile info, sawir, iyo settings kale</small>
                        </div>
                    </li>
                </ul>
            </div>
        </div>
        
        <div class="demo-section">
            <h2>🚀 Sidee Loo Isticmaalo</h2>
            <div class="demo-content">
                <div style="display: flex; align-items: center; margin-bottom: 1rem;">
                    <div class="step-number">1</div>
                    <div>Riix "Sheeko Cusub" button-ka si aad u bilowdo sheeko cusub</div>
                </div>
                <div style="display: flex; align-items: center; margin-bottom: 1rem;">
                    <div class="step-number">2</div>
                    <div>Qor fariintaada sanduuqa hoose ama ku dar files</div>
                </div>
                <div style="display: flex; align-items: center; margin-bottom: 1rem;">
                    <div class="step-number">3</div>
                    <div>Riix "Send" button-ka ama isticmaal Enter key</div>
                </div>
                <div style="display: flex; align-items: center; margin-bottom: 1rem;">
                    <div class="step-number">4</div>
                    <div>Sug jawaabta Tincada AI (waxay qaadan kartaa dhawr ilbiriqsi)</div>
                </div>
                <div style="display: flex; align-items: center;">
                    <div class="step-number">5</div>
                    <div>Sii wad sheekada ama bilow mid cusub!</div>
                </div>
            </div>
        </div>
        
        <div class="demo-section">
            <h2>⚙️ Technical Details</h2>
            <div class="demo-content">
                <p><strong>API:</strong> OpenAI GPT-4o-mini</p>
                <p><strong>Languages:</strong> HTML5, CSS3, JavaScript (ES6+)</p>
                <p><strong>Features:</strong> File Upload, Voice Recording, Local Storage</p>
                <p><strong>Browser Support:</strong> Chrome, Firefox, Safari, Edge</p>
                
                <div class="warning">
                    <strong>⚠️ Muhiim:</strong> Website-ku wuxuu u baahan yahay internet connection si uu ula xiriiro OpenAI API.
                </div>
                
                <div class="success">
                    <strong>✅ Diyaar:</strong> Website-ku waa diyaar oo wuxuu ku shaqaynayaa http://localhost:5000
                </div>
            </div>
        </div>
        
        <div class="demo-section">
            <h2>🎯 Su'aalo Tusaale ah</h2>
            <div class="demo-content">
                <div class="code-block">
                    • "Maxaa ka mid ah magaalooyinka ugu waaweyn Soomaaliya?"<br>
                    • "Ii sharax sidii loo sameeyo canjeero"<br>
                    • "Waa maxay faa'iidooyinka barashada luqado cusub?"<br>
                    • "Ii qor gabay ku saabsan caashaqa"<br>
                    • "Sidee ayaan u baran karaa programming?"
                </div>
            </div>
        </div>
        
        <div class="demo-buttons">
            <a href="dashboard.html" class="demo-btn">
                🚀 Bilow Tincada AI Dashboard
            </a>
            <a href="login.html" class="demo-btn">
                🔐 Soo Gal / Samee Akoon
            </a>
            <a href="index.html" class="demo-btn secondary">
                📱 Old Version (Simple)
            </a>
            <a href="README.md" class="demo-btn secondary">
                📖 Akhri Documentation
            </a>
        </div>
    </div>
</body>
</html>
